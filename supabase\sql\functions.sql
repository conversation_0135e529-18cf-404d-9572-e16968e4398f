-- Fonction pour récupérer les événements d'un utilisateur
CREATE OR REPLACE FUNCTION get_events_for_user(user_id_param UUID)
RETURNS SETOF events AS $$
BEGIN
  RETURN QUERY
  SELECT e.*
  FROM events e
  LEFT JOIN participants p ON e.id = p.event_id AND p.user_id = user_id_param
  WHERE e.organizer_id = user_id_param OR p.user_id = user_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Accorder les droits d'exécution aux utilisateurs authentifiés
GRANT EXECUTE ON FUNCTION public.get_events_for_user(UUID) TO authenticated;

-- Fonction pour récupérer un événement par son ID sans passer par les politiques qui causent la récursion infinie
CREATE OR REPLACE FUNCTION get_event_by_id(event_id_param BIGINT)
RETURNS SETOF events AS $$
BEGIN
  RETURN QUERY
  SELECT e.*
  FROM events e
  WHERE e.id = event_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Accorder les droits d'exécution aux utilisateurs authentifiés
GRANT EXECUTE ON FUNCTION public.get_event_by_id(BIGINT) TO authenticated;

-- Fonction pour vérifier si un utilisateur est participant d'un événement
-- Utilise SECURITY DEFINER pour éviter la récursion avec les politiques RLS sur la table 'participants'
CREATE OR REPLACE FUNCTION public.is_participant(event_id_input bigint, user_id_input uuid)
RETURNS boolean
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.participants
    WHERE event_id = event_id_input AND user_id = user_id_input
  );
$$;

GRANT EXECUTE ON FUNCTION public.is_participant(bigint, uuid) TO authenticated;
