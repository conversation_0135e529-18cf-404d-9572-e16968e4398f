import React from "react";
import { View, ScrollView, Pressable, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge as UIBadge } from "~/components/ui/badge";
import {
  Badge,
  UserStats,
  AchievementProgress,
  LeaderboardEntry,
} from "~/lib/achievementsService";
import { UserStreak } from "~/lib/streaksService";
import {
  CommunityChallenge,
  ChallengeParticipation,
} from "~/lib/challengesService";
import { BadgeCollection, CollectionStats } from "~/lib/collectionsService";

interface BadgeCardProps {
  badge: Badge;
  isUnlocked?: boolean;
  progress?: number;
  current?: number;
  required?: number;
  unlockedAt?: string;
  onPress?: () => void;
}

export function BadgeCard({
  badge,
  isUnlocked = false,
  progress = 0,
  current = 0,
  required = 1,
  unlockedAt,
  onPress,
}: BadgeCardProps) {
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case "common":
        return "bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-600";
      case "rare":
        return "bg-blue-100 border-blue-300 dark:bg-blue-900 dark:border-blue-600";
      case "epic":
        return "bg-purple-100 border-purple-300 dark:bg-purple-900 dark:border-purple-600";
      case "legendary":
        return "bg-yellow-100 border-yellow-300 dark:bg-yellow-900 dark:border-yellow-600";
      default:
        return "bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-600";
    }
  };

  const getRarityText = (rarity: string) => {
    switch (rarity) {
      case "common":
        return "Commun";
      case "rare":
        return "Rare";
      case "epic":
        return "Épique";
      case "legendary":
        return "Légendaire";
      default:
        return "Commun";
    }
  };

  return (
    <Pressable
      onPress={onPress}
      {...(Platform.OS === "web" ? { style: { cursor: "pointer" } } : {})}
    >
      <Card
        className={`${getRarityColor(badge.rarity)} ${
          isUnlocked ? "" : "opacity-60"
        }`}
      >
        <CardContent className="p-4">
          <View className="flex-row items-start gap-3">
            <View className="items-center">
              <Text className={`text-3xl ${isUnlocked ? "" : "grayscale"}`}>
                {badge.icon}
              </Text>
              {isUnlocked && <Text className="text-lg">✅</Text>}
            </View>

            <View className="flex-1">
              <View className="flex-row items-center gap-2 mb-1">
                <Text className="font-semibold flex-1">{badge.title}</Text>
                <UIBadge variant="outline">
                  <Text className="text-xs">{getRarityText(badge.rarity)}</Text>
                </UIBadge>
              </View>

              <Text className="text-sm text-muted-foreground mb-2">
                {badge.description}
              </Text>

              <View className="flex-row items-center justify-between mb-2">
                <Text className="text-xs font-medium">
                  {badge.points} points
                </Text>
                {!isUnlocked && (
                  <Text className="text-xs text-muted-foreground">
                    {current}/{required}
                  </Text>
                )}
              </View>

              {!isUnlocked && progress > 0 && (
                <View className="w-full h-2 bg-muted rounded-full">
                  <View
                    className="h-full bg-primary rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(100, progress)}%` }}
                  />
                </View>
              )}

              {isUnlocked && unlockedAt && (
                <Text className="text-xs text-muted-foreground mt-1">
                  Débloqué le {new Date(unlockedAt).toLocaleDateString("fr-FR")}
                </Text>
              )}
            </View>
          </View>
        </CardContent>
      </Card>
    </Pressable>
  );
}

interface UserStatsCardProps {
  stats: UserStats;
  nextLevelInfo?: {
    currentLevel: number;
    nextLevel: number;
    pointsRequired: number;
    progress: number;
  };
}

export function UserStatsCard({ stats, nextLevelInfo }: UserStatsCardProps) {
  return (
    <Card className="border border-border">
      <CardHeader>
        <CardTitle className="flex-row items-center gap-2">
          <Text className="text-2xl">📊</Text>
          <Text>Vos statistiques</Text>
        </CardTitle>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Niveau et progression */}
        <View className="mb-4 p-3 bg-primary/5 rounded-lg">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="font-semibold">Niveau {stats.level}</Text>
            <Text className="text-sm text-muted-foreground">
              {stats.total_points} points
            </Text>
          </View>

          {nextLevelInfo && (
            <>
              <View className="w-full h-3 bg-muted rounded-full mb-1">
                <View
                  className="h-full bg-primary rounded-full transition-all duration-300"
                  style={{ width: `${nextLevelInfo.progress}%` }}
                />
              </View>
              <Text className="text-xs text-muted-foreground">
                {nextLevelInfo.pointsRequired} points pour le niveau{" "}
                {nextLevelInfo.nextLevel}
              </Text>
            </>
          )}
        </View>

        {/* Statistiques détaillées */}
        <View className="gap-3">
          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center gap-2">
              <Text className="text-lg">🎉</Text>
              <Text className="text-sm">Événements organisés</Text>
            </View>
            <Text className="font-semibold">{stats.events_organized}</Text>
          </View>

          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center gap-2">
              <Text className="text-lg">👥</Text>
              <Text className="text-sm">Événements participés</Text>
            </View>
            <Text className="font-semibold">{stats.events_participated}</Text>
          </View>

          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center gap-2">
              <Text className="text-lg">💌</Text>
              <Text className="text-sm">Participants invités</Text>
            </View>
            <Text className="font-semibold">
              {stats.total_participants_invited}
            </Text>
          </View>

          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center gap-2">
              <Text className="text-lg">📝</Text>
              <Text className="text-sm">Items gérés</Text>
            </View>
            <Text className="font-semibold">{stats.total_items_managed}</Text>
          </View>

          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center gap-2">
              <Text className="text-lg">💰</Text>
              <Text className="text-sm">Argent économisé</Text>
            </View>
            <Text className="font-semibold">
              {stats.total_money_saved.toFixed(0)}€
            </Text>
          </View>

          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center gap-2">
              <Text className="text-lg">⭐</Text>
              <Text className="text-sm">Événements parfaits</Text>
            </View>
            <Text className="font-semibold">{stats.perfect_events}</Text>
          </View>

          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center gap-2">
              <Text className="text-lg">🏆</Text>
              <Text className="text-sm">Badges débloqués</Text>
            </View>
            <Text className="font-semibold">{stats.achievements_unlocked}</Text>
          </View>
        </View>
      </CardContent>
    </Card>
  );
}

interface AchievementProgressListProps {
  progress: AchievementProgress[];
  onBadgePress?: (badge: Badge) => void;
}

export function AchievementProgressList({
  progress,
  onBadgePress,
}: AchievementProgressListProps) {
  const inProgress = progress.filter(
    (p) => !p.is_completed && p.percentage > 0
  );
  const available = progress.filter(
    (p) => !p.is_completed && p.percentage === 0
  );
  const completed = progress.filter((p) => p.is_completed);

  return (
    <ScrollView className="flex-1">
      {/* En cours */}
      {inProgress.length > 0 && (
        <View className="mb-6">
          <Text className="text-lg font-semibold mb-3">🎯 En cours</Text>
          <View className="gap-3">
            {inProgress.map((item) => (
              <BadgeCard
                key={item.badge.id}
                badge={item.badge}
                isUnlocked={false}
                progress={item.percentage}
                current={item.current}
                required={item.required}
                onPress={() => onBadgePress?.(item.badge)}
              />
            ))}
          </View>
        </View>
      )}

      {/* Complétés */}
      {completed.length > 0 && (
        <View className="mb-6">
          <Text className="text-lg font-semibold mb-3">
            ✅ Débloqués ({completed.length})
          </Text>
          <View className="gap-3">
            {completed.map((item) => (
              <BadgeCard
                key={item.badge.id}
                badge={item.badge}
                isUnlocked={true}
                unlockedAt={item.unlocked_at}
                onPress={() => onBadgePress?.(item.badge)}
              />
            ))}
          </View>
        </View>
      )}

      {/* Disponibles */}
      {available.length > 0 && (
        <View className="mb-6">
          <Text className="text-lg font-semibold mb-3">
            🔒 À débloquer ({available.length})
          </Text>
          <View className="gap-3">
            {available.slice(0, 10).map((item) => (
              <BadgeCard
                key={item.badge.id}
                badge={item.badge}
                isUnlocked={false}
                progress={0}
                current={item.current}
                required={item.required}
                onPress={() => onBadgePress?.(item.badge)}
              />
            ))}
          </View>

          {available.length > 10 && (
            <Text className="text-sm text-muted-foreground text-center mt-3">
              ... et {available.length - 10} autres badges à découvrir
            </Text>
          )}
        </View>
      )}
    </ScrollView>
  );
}

interface LeaderboardProps {
  leaderboard: LeaderboardEntry[];
  currentUserId?: string;
}

export function Leaderboard({ leaderboard, currentUserId }: LeaderboardProps) {
  const getRankEmoji = (rank: number) => {
    switch (rank) {
      case 1:
        return "🥇";
      case 2:
        return "🥈";
      case 3:
        return "🥉";
      default:
        return `#${rank}`;
    }
  };

  return (
    <Card className="border border-border">
      <CardHeader>
        <CardTitle className="flex-row items-center gap-2">
          <Text className="text-2xl">🏆</Text>
          <Text>Classement</Text>
        </CardTitle>
      </CardHeader>

      <CardContent className="pt-0">
        <View className="gap-2">
          {leaderboard.map((entry) => (
            <View
              key={entry.user_id}
              className={`flex-row items-center justify-between p-3 rounded-lg ${
                entry.user_id === currentUserId
                  ? "bg-primary/10 border border-primary/30"
                  : "bg-muted/30"
              }`}
            >
              <View className="flex-row items-center gap-3">
                <Text className="text-lg font-bold w-8">
                  {getRankEmoji(entry.rank)}
                </Text>
                <View>
                  <Text className="font-semibold">
                    {entry.username ||
                      `Utilisateur ${entry.user_id.slice(0, 8)}`}
                  </Text>
                  <Text className="text-xs text-muted-foreground">
                    Niveau {entry.level} • {entry.achievements_unlocked} badges
                  </Text>
                </View>
              </View>

              <Text className="font-bold text-primary">
                {entry.total_points} pts
              </Text>
            </View>
          ))}
        </View>
      </CardContent>
    </Card>
  );
}

interface QuickStatsProps {
  stats: UserStats;
  recentBadges: Badge[];
}

export function QuickStats({ stats, recentBadges }: QuickStatsProps) {
  return (
    <Card className="border border-border">
      <CardContent className="p-4">
        <View className="flex-row items-center justify-between mb-3">
          <Text className="font-semibold">🎮 Votre progression</Text>
          <UIBadge>
            <Text className="text-xs">Niveau {stats.level}</Text>
          </UIBadge>
        </View>

        <View className="flex-row justify-between mb-3">
          <View className="items-center">
            <Text className="text-lg font-bold">{stats.total_points}</Text>
            <Text className="text-xs text-muted-foreground">Points</Text>
          </View>
          <View className="items-center">
            <Text className="text-lg font-bold">
              {stats.achievements_unlocked}
            </Text>
            <Text className="text-xs text-muted-foreground">Badges</Text>
          </View>
          <View className="items-center">
            <Text className="text-lg font-bold">{stats.events_organized}</Text>
            <Text className="text-xs text-muted-foreground">Événements</Text>
          </View>
        </View>

        {recentBadges.length > 0 && (
          <View>
            <Text className="text-sm font-medium mb-2">Derniers badges :</Text>
            <View className="flex-row gap-1">
              {recentBadges.slice(0, 5).map((badge) => (
                <Text key={badge.id} className="text-lg">
                  {badge.icon}
                </Text>
              ))}
            </View>
          </View>
        )}
      </CardContent>
    </Card>
  );
}

interface StreakCardProps {
  streaks: UserStreak[];
  onStreakPress?: (streak: UserStreak) => void;
}

export function StreakCard({ streaks, onStreakPress }: StreakCardProps) {
  const getStreakIcon = (type: string) => {
    switch (type) {
      case "daily_login":
        return "🔥";
      case "weekly_event":
        return "📅";
      case "monthly_organization":
        return "🗓️";
      case "participation_streak":
        return "🎉";
      default:
        return "⚡";
    }
  };

  const getStreakTitle = (type: string) => {
    switch (type) {
      case "daily_login":
        return "Connexion quotidienne";
      case "weekly_event":
        return "Événement hebdomadaire";
      case "monthly_organization":
        return "Organisation mensuelle";
      case "participation_streak":
        return "Participation continue";
      default:
        return "Streak";
    }
  };

  const activeStreaks = streaks.filter(
    (s) => s.is_active && s.current_streak > 0
  );

  return (
    <Card className="border border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
      <CardHeader>
        <CardTitle className="flex-row items-center gap-2">
          <Text className="text-2xl">🔥</Text>
          <Text>Vos Streaks</Text>
        </CardTitle>
      </CardHeader>

      <CardContent className="pt-0">
        {activeStreaks.length === 0 ? (
          <Text className="text-muted-foreground text-center">
            Aucun streak actif. Commencez dès aujourd'hui !
          </Text>
        ) : (
          <View className="gap-3">
            {activeStreaks.map((streak) => (
              <Pressable
                key={streak.id}
                onPress={() => onStreakPress?.(streak)}
                {...(Platform.OS === "web"
                  ? { style: { cursor: "pointer" } }
                  : {})}
              >
                <View className="flex-row items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                  <View className="flex-row items-center gap-3">
                    <Text className="text-2xl">
                      {getStreakIcon(streak.streak_type)}
                    </Text>
                    <View>
                      <Text className="font-semibold">
                        {getStreakTitle(streak.streak_type)}
                      </Text>
                      <Text className="text-xs text-muted-foreground">
                        Record : {streak.longest_streak} jours
                      </Text>
                    </View>
                  </View>

                  <View className="items-center">
                    <Text className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                      {streak.current_streak}
                    </Text>
                    <Text className="text-xs text-muted-foreground">jours</Text>
                  </View>
                </View>
              </Pressable>
            ))}
          </View>
        )}
      </CardContent>
    </Card>
  );
}

interface ChallengeCardProps {
  challenge: CommunityChallenge;
  participation?: ChallengeParticipation;
  onJoin?: () => void;
  onViewLeaderboard?: () => void;
}

export function ChallengeCard({
  challenge,
  participation,
  onJoin,
  onViewLeaderboard,
}: ChallengeCardProps) {
  const progress = participation
    ? (participation.current_progress / challenge.target_value) * 100
    : 0;
  const isCompleted = participation?.is_completed || false;
  const isParticipating = !!participation;

  const timeLeft =
    new Date(challenge.end_date).getTime() - new Date().getTime();
  const daysLeft = Math.max(0, Math.ceil(timeLeft / (1000 * 60 * 60 * 24)));

  const getChallengeTypeColor = (type: string) => {
    switch (type) {
      case "individual":
        return "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950";
      case "community":
        return "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950";
      case "competitive":
        return "border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950";
      default:
        return "border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-950";
    }
  };

  return (
    <Card
      className={`${getChallengeTypeColor(challenge.challenge_type)} ${
        isCompleted ? "opacity-75" : ""
      }`}
    >
      <CardContent className="p-4">
        <View className="flex-row items-start gap-3 mb-3">
          <Text className="text-3xl">{challenge.icon}</Text>
          <View className="flex-1">
            <View className="flex-row items-center gap-2 mb-1">
              <Text className="font-semibold flex-1">{challenge.title}</Text>
              <UIBadge variant="outline">
                <Text className="text-xs capitalize">
                  {challenge.challenge_type}
                </Text>
              </UIBadge>
            </View>

            <Text className="text-sm text-muted-foreground mb-2">
              {challenge.description}
            </Text>

            <View className="flex-row items-center justify-between mb-2">
              <Text className="text-xs font-medium">
                🏆 {challenge.reward_points} points
              </Text>
              <Text className="text-xs text-muted-foreground">
                ⏰ {daysLeft} jour{daysLeft !== 1 ? "s" : ""} restant
                {daysLeft !== 1 ? "s" : ""}
              </Text>
            </View>
          </View>
        </View>

        {isParticipating && (
          <View className="mb-3">
            <View className="flex-row items-center justify-between mb-1">
              <Text className="text-sm font-medium">Progression</Text>
              <Text className="text-sm text-muted-foreground">
                {participation.current_progress}/{challenge.target_value}
              </Text>
            </View>
            <View className="w-full h-2 bg-muted rounded-full">
              <View
                className="h-full bg-primary rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, progress)}%` }}
              />
            </View>
          </View>
        )}

        <View className="flex-row gap-2">
          {!isParticipating && (
            <Button onPress={onJoin} className="flex-1">
              <Text className="text-primary-foreground text-xs">
                Participer
              </Text>
            </Button>
          )}

          {challenge.challenge_type === "competitive" && (
            <Button
              variant="outline"
              onPress={onViewLeaderboard}
              className="flex-1"
            >
              <Text className="text-xs">Classement</Text>
            </Button>
          )}

          {isCompleted && (
            <View className="flex-1 items-center justify-center p-2 bg-green-100 dark:bg-green-900 rounded">
              <Text className="text-xs text-green-700 dark:text-green-300 font-semibold">
                ✅ Terminé !
              </Text>
            </View>
          )}
        </View>
      </CardContent>
    </Card>
  );
}

interface CollectionCardProps {
  collection: BadgeCollection;
  userBadges: number[];
  isCompleted?: boolean;
  onPress?: () => void;
}

export function CollectionCard({
  collection,
  userBadges,
  isCompleted = false,
  onPress,
}: CollectionCardProps) {
  const completedBadges = collection.badge_ids.filter((id) =>
    userBadges.includes(id)
  ).length;
  const totalBadges = collection.badge_ids.length;
  const progress = (completedBadges / totalBadges) * 100;

  return (
    <Pressable
      onPress={onPress}
      {...(Platform.OS === "web" ? { style: { cursor: "pointer" } } : {})}
    >
      <Card
        className={`${
          isCompleted
            ? "border-gold bg-yellow-50 dark:border-yellow-600 dark:bg-yellow-950"
            : "border-border"
        }`}
      >
        <CardContent className="p-4">
          <View className="flex-row items-start gap-3">
            <View className="items-center">
              <Text className="text-3xl mb-1">{collection.icon}</Text>
              {isCompleted && <Text className="text-lg">👑</Text>}
            </View>

            <View className="flex-1">
              <View className="flex-row items-center gap-2 mb-1">
                <Text className="font-semibold flex-1">{collection.title}</Text>
                {isCompleted && (
                  <UIBadge className="bg-yellow-500">
                    <Text className="text-xs text-white">Complété</Text>
                  </UIBadge>
                )}
              </View>

              <Text className="text-sm text-muted-foreground mb-3">
                {collection.description}
              </Text>

              <View className="flex-row items-center justify-between mb-2">
                <Text className="text-xs font-medium">
                  {completedBadges}/{totalBadges} badges
                </Text>
                <Text className="text-xs text-muted-foreground">
                  🏆 {collection.completion_reward_points} points
                </Text>
              </View>

              <View className="w-full h-2 bg-muted rounded-full">
                <View
                  className={`h-full rounded-full transition-all duration-300 ${
                    isCompleted ? "bg-yellow-500" : "bg-primary"
                  }`}
                  style={{ width: `${progress}%` }}
                />
              </View>

              {progress > 0 && progress < 100 && (
                <Text className="text-xs text-muted-foreground mt-1">
                  {progress.toFixed(0)}% complété
                </Text>
              )}
            </View>
          </View>
        </CardContent>
      </Card>
    </Pressable>
  );
}
