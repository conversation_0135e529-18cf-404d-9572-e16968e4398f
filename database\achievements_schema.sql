-- Schema pour le système de badges et achievements de Party Organizer

-- Table des badges disponibles
CREATE TABLE IF NOT EXISTS badges (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  icon VARCHAR(10) NOT NULL,
  category VARCHAR(50) NOT NULL,
  rarity VARCHAR(20) NOT NULL DEFAULT 'common', -- common, rare, epic, legendary
  points INTEGER NOT NULL DEFAULT 0,
  requirements JSONB NOT NULL, -- Conditions pour débloquer le badge
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des achievements utilisateur
CREATE TABLE IF NOT EXISTS user_achievements (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  badge_id INTEGER NOT NULL REFERENCES badges(id) ON DELETE CASCADE,
  unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  progress JSONB DEFAULT '{}', -- Progression vers le badge
  is_completed BOOLEAN NOT NULL DEFAULT false,
  completion_data JSONB DEFAULT '{}', -- Données spécifiques à la completion
  UNIQUE(user_id, badge_id)
);

-- Table des statistiques utilisateur pour les achievements
CREATE TABLE IF NOT EXISTS user_stats (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  total_points INTEGER NOT NULL DEFAULT 0,
  level INTEGER NOT NULL DEFAULT 1,
  events_organized INTEGER NOT NULL DEFAULT 0,
  events_participated INTEGER NOT NULL DEFAULT 0,
  total_participants_invited INTEGER NOT NULL DEFAULT 0,
  total_items_managed INTEGER NOT NULL DEFAULT 0,
  total_money_saved DECIMAL(10,2) NOT NULL DEFAULT 0,
  perfect_events INTEGER NOT NULL DEFAULT 0, -- Événements avec 100% completion
  streak_days INTEGER NOT NULL DEFAULT 0, -- Jours consécutifs d'activité
  last_activity_date DATE,
  achievements_unlocked INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_badge_id ON user_achievements(badge_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_completed ON user_achievements(is_completed);
CREATE INDEX IF NOT EXISTS idx_user_stats_user_id ON user_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_user_stats_points ON user_stats(total_points DESC);
CREATE INDEX IF NOT EXISTS idx_badges_category ON badges(category);
CREATE INDEX IF NOT EXISTS idx_badges_rarity ON badges(rarity);

-- Fonction pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
CREATE TRIGGER update_badges_updated_at BEFORE UPDATE ON badges
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_stats_updated_at BEFORE UPDATE ON user_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Politiques RLS
ALTER TABLE badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_stats ENABLE ROW LEVEL SECURITY;

-- Badges : lecture publique
CREATE POLICY "Badges are viewable by everyone" ON badges
    FOR SELECT USING (is_active = true);

-- User achievements : utilisateur peut voir ses propres achievements
CREATE POLICY "Users can view own achievements" ON user_achievements
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own achievements" ON user_achievements
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own achievements" ON user_achievements
    FOR UPDATE USING (auth.uid() = user_id);

-- User stats : utilisateur peut voir ses propres stats
CREATE POLICY "Users can view own stats" ON user_stats
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own stats" ON user_stats
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own stats" ON user_stats
    FOR UPDATE USING (auth.uid() = user_id);

-- Politique pour voir les stats des autres (classements)
CREATE POLICY "Users can view others stats for leaderboard" ON user_stats
    FOR SELECT USING (true); -- Permettre la lecture pour les classements

-- Fonction pour calculer le niveau basé sur les points
CREATE OR REPLACE FUNCTION calculate_level(points INTEGER)
RETURNS INTEGER AS $$
BEGIN
    -- Formule : niveau = racine carrée de (points / 100) + 1
    RETURN FLOOR(SQRT(points / 100.0)) + 1;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour mettre à jour les stats utilisateur
CREATE OR REPLACE FUNCTION update_user_stats(
    p_user_id UUID,
    p_events_organized INTEGER DEFAULT 0,
    p_events_participated INTEGER DEFAULT 0,
    p_participants_invited INTEGER DEFAULT 0,
    p_items_managed INTEGER DEFAULT 0,
    p_money_saved DECIMAL DEFAULT 0,
    p_perfect_events INTEGER DEFAULT 0
)
RETURNS void AS $$
DECLARE
    current_stats user_stats%ROWTYPE;
    new_points INTEGER;
    new_level INTEGER;
BEGIN
    -- Récupérer ou créer les stats utilisateur
    SELECT * INTO current_stats FROM user_stats WHERE user_id = p_user_id;
    
    IF NOT FOUND THEN
        INSERT INTO user_stats (user_id) VALUES (p_user_id);
        SELECT * INTO current_stats FROM user_stats WHERE user_id = p_user_id;
    END IF;
    
    -- Calculer les nouveaux totaux
    new_points := current_stats.total_points + 
                  (p_events_organized * 50) + 
                  (p_events_participated * 10) + 
                  (p_participants_invited * 5) + 
                  (p_items_managed * 2) + 
                  (p_money_saved * 1) + 
                  (p_perfect_events * 100);
    
    new_level := calculate_level(new_points);
    
    -- Mettre à jour les stats
    UPDATE user_stats SET
        events_organized = events_organized + p_events_organized,
        events_participated = events_participated + p_events_participated,
        total_participants_invited = total_participants_invited + p_participants_invited,
        total_items_managed = total_items_managed + p_items_managed,
        total_money_saved = total_money_saved + p_money_saved,
        perfect_events = perfect_events + p_perfect_events,
        total_points = new_points,
        level = new_level,
        last_activity_date = CURRENT_DATE,
        updated_at = NOW()
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour vérifier et débloquer les achievements
CREATE OR REPLACE FUNCTION check_and_unlock_achievements(p_user_id UUID)
RETURNS TABLE(badge_id INTEGER, badge_name VARCHAR, badge_title VARCHAR) AS $$
DECLARE
    user_stat user_stats%ROWTYPE;
    badge_record badges%ROWTYPE;
    achievement_record user_achievements%ROWTYPE;
    requirements JSONB;
    is_eligible BOOLEAN;
BEGIN
    -- Récupérer les stats utilisateur
    SELECT * INTO user_stat FROM user_stats WHERE user_id = p_user_id;
    
    IF NOT FOUND THEN
        RETURN;
    END IF;
    
    -- Parcourir tous les badges actifs
    FOR badge_record IN SELECT * FROM badges WHERE is_active = true LOOP
        -- Vérifier si l'utilisateur a déjà ce badge
        SELECT * INTO achievement_record 
        FROM user_achievements 
        WHERE user_id = p_user_id AND badge_id = badge_record.id;
        
        -- Si pas encore débloqué, vérifier les conditions
        IF NOT FOUND OR NOT achievement_record.is_completed THEN
            requirements := badge_record.requirements;
            is_eligible := true;
            
            -- Vérifier chaque condition
            IF requirements ? 'events_organized' AND 
               user_stat.events_organized < (requirements->>'events_organized')::INTEGER THEN
                is_eligible := false;
            END IF;
            
            IF requirements ? 'events_participated' AND 
               user_stat.events_participated < (requirements->>'events_participated')::INTEGER THEN
                is_eligible := false;
            END IF;
            
            IF requirements ? 'total_points' AND 
               user_stat.total_points < (requirements->>'total_points')::INTEGER THEN
                is_eligible := false;
            END IF;
            
            IF requirements ? 'perfect_events' AND 
               user_stat.perfect_events < (requirements->>'perfect_events')::INTEGER THEN
                is_eligible := false;
            END IF;
            
            IF requirements ? 'level' AND 
               user_stat.level < (requirements->>'level')::INTEGER THEN
                is_eligible := false;
            END IF;
            
            -- Si éligible, débloquer le badge
            IF is_eligible THEN
                INSERT INTO user_achievements (user_id, badge_id, is_completed, completion_data)
                VALUES (p_user_id, badge_record.id, true, '{"auto_unlocked": true}'::JSONB)
                ON CONFLICT (user_id, badge_id) 
                DO UPDATE SET 
                    is_completed = true,
                    unlocked_at = NOW(),
                    completion_data = '{"auto_unlocked": true}'::JSONB;
                
                -- Mettre à jour le compteur d'achievements
                UPDATE user_stats SET 
                    achievements_unlocked = achievements_unlocked + 1,
                    total_points = total_points + badge_record.points
                WHERE user_id = p_user_id;
                
                -- Retourner le badge débloqué
                badge_id := badge_record.id;
                badge_name := badge_record.name;
                badge_title := badge_record.title;
                RETURN NEXT;
            END IF;
        END IF;
    END LOOP;
    
    RETURN;
END;
$$ LANGUAGE plpgsql;
