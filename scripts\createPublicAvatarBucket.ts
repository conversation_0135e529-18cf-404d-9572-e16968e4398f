/**
 * Script pour créer un bucket public pour les avatars sans RLS
 */

require("dotenv").config();
const { createClient } = require("@supabase/supabase-js");

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("❌ Variables d'environnement manquantes");
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function createPublicAvatarBucket() {
  try {
    console.log("🪣 Création d'un bucket public pour les avatars...");

    // 1. Vérifier si le bucket existe déjà
    const { data: buckets, error: listError } =
      await supabaseAdmin.storage.listBuckets();

    if (listError) {
      console.error(
        "❌ Erreur lors de la vérification des buckets:",
        listError
      );
      return;
    }

    const bucketName = "public-avatars";
    const bucketExists = buckets?.some(
      (bucket: any) => bucket.name === bucketName
    );

    if (bucketExists) {
      console.log("✅ Le bucket 'public-avatars' existe déjà");
    } else {
      // 2. Créer le bucket public
      const { error: createError } = await supabaseAdmin.storage.createBucket(
        bucketName,
        {
          public: true,
          allowedMimeTypes: [
            "image/jpeg",
            "image/png",
            "image/webp",
            "image/gif",
          ],
          fileSizeLimit: 5 * 1024 * 1024, // 5MB
        }
      );

      if (createError) {
        console.error("❌ Erreur lors de la création du bucket:", createError);
        return;
      }

      console.log("✅ Bucket 'public-avatars' créé avec succès");
    }

    // 3. Test d'upload
    console.log("🧪 Test d'upload dans le nouveau bucket...");

    const testFileName = `test_${Date.now()}.jpg`;
    const testContent = new Uint8Array([255, 216, 255, 224]); // Header JPEG minimal

    const { error: uploadError } = await supabaseAdmin.storage
      .from(bucketName)
      .upload(testFileName, testContent, {
        contentType: "image/jpeg",
        upsert: true,
      });

    if (uploadError) {
      console.log("❌ Erreur d'upload de test:", uploadError);
    } else {
      console.log("✅ Upload de test réussi dans le bucket public");

      // Obtenir l'URL publique
      const { data: urlData } = supabaseAdmin.storage
        .from(bucketName)
        .getPublicUrl(testFileName);

      console.log("🔗 URL de test:", urlData.publicUrl);

      // Nettoyer le fichier de test
      await supabaseAdmin.storage.from(bucketName).remove([testFileName]);
      console.log("🧹 Fichier de test nettoyé");
    }

    // 4. Instructions pour utiliser le nouveau bucket
    console.log("\n🎯 Instructions pour utiliser le nouveau bucket:");
    console.log("1. Le bucket 'public-avatars' est maintenant disponible");
    console.log("2. Il est public et sans restrictions RLS");
    console.log("3. Modifiez avatarService.ts pour utiliser ce bucket:");
    console.log("   private bucketName = 'public-avatars';");
    console.log("4. Testez l'upload d'avatar dans l'application");

    console.log("\n✅ Configuration terminée !");
  } catch (error) {
    console.error("❌ Erreur:", error);
  }
}

// Exécuter le script
if (require.main === module) {
  createPublicAvatarBucket()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { createPublicAvatarBucket };
