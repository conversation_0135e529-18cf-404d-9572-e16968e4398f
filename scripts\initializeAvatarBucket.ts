/**
 * Script d'initialisation du bucket avatars pour Party Organizer
 * À exécuter une seule fois pour configurer le stockage des avatars
 */

// Charger les variables d'environnement
require("dotenv").config();

// Import direct de Supabase sans dépendances React Native
const { createClient } = require("@supabase/supabase-js");

// Configuration Supabase depuis les variables d'environnement
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("❌ Variables d'environnement Supabase manquantes");
  console.error(
    "Vérifiez EXPO_PUBLIC_SUPABASE_URL et SUPABASE_SERVICE_ROLE_KEY dans .env"
  );
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function initializeAvatarBucket() {
  try {
    console.log("🚀 Initialisation du bucket avatars...");
    console.log("URL Supabase:", supabaseUrl);
    console.log("Service key présente:", !!supabaseServiceKey);

    // Vérifier si le bucket existe déjà
    const { data: buckets, error: listError } =
      await supabaseAdmin.storage.listBuckets();

    if (listError) {
      console.error(
        "❌ Erreur lors de la vérification des buckets:",
        listError
      );
      return;
    }

    const bucketExists = buckets?.some(
      (bucket: any) => bucket.name === "avatars"
    );

    if (bucketExists) {
      console.log("✅ Le bucket avatars existe déjà");
      return;
    }

    // Créer le bucket
    const { error: createError } = await supabaseAdmin.storage.createBucket(
      "avatars",
      {
        public: true,
        allowedMimeTypes: [
          "image/jpeg",
          "image/png",
          "image/webp",
          "image/gif",
        ],
        fileSizeLimit: 5 * 1024 * 1024, // 5MB
      }
    );

    if (createError) {
      console.error("❌ Erreur lors de la création du bucket:", createError);
      return;
    }

    console.log("✅ Bucket avatars créé avec succès");

    // Créer les politiques RLS pour le bucket
    console.log("🔐 Configuration des politiques RLS...");

    // Politique pour permettre la lecture publique
    const readPolicy = `
      CREATE POLICY "Public read access for avatars" ON storage.objects
      FOR SELECT USING (bucket_id = 'avatars');
    `;

    // Politique pour permettre l'upload aux utilisateurs authentifiés
    const uploadPolicy = `
      CREATE POLICY "Authenticated users can upload avatars" ON storage.objects
      FOR INSERT WITH CHECK (
        bucket_id = 'avatars' 
        AND auth.role() = 'authenticated'
        AND (storage.foldername(name))[1] = auth.uid()::text
      );
    `;

    // Politique pour permettre la mise à jour de ses propres avatars
    const updatePolicy = `
      CREATE POLICY "Users can update their own avatars" ON storage.objects
      FOR UPDATE USING (
        bucket_id = 'avatars' 
        AND auth.role() = 'authenticated'
        AND (storage.foldername(name))[1] = auth.uid()::text
      );
    `;

    // Politique pour permettre la suppression de ses propres avatars
    const deletePolicy = `
      CREATE POLICY "Users can delete their own avatars" ON storage.objects
      FOR DELETE USING (
        bucket_id = 'avatars' 
        AND auth.role() = 'authenticated'
        AND (storage.foldername(name))[1] = auth.uid()::text
      );
    `;

    // Exécuter les politiques (note: ceci nécessiterait une connexion SQL directe)
    console.log("📝 Politiques RLS à créer manuellement dans Supabase:");
    console.log("1. Lecture publique:", readPolicy);
    console.log("2. Upload authentifié:", uploadPolicy);
    console.log("3. Mise à jour:", updatePolicy);
    console.log("4. Suppression:", deletePolicy);

    console.log("✅ Initialisation terminée !");
    console.log(
      "⚠️  N'oubliez pas de créer les politiques RLS manuellement dans Supabase"
    );
  } catch (error) {
    console.error("❌ Erreur lors de l'initialisation:", error);
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  initializeAvatarBucket()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { initializeAvatarBucket };
