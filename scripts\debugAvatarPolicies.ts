/**
 * Script de diagnostic pour les politiques RLS des avatars
 */

// Charger les variables d'environnement
require('dotenv').config();

const { createClient } = require("@supabase/supabase-js");

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("❌ Variables d'environnement manquantes");
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function debugAvatarPolicies() {
  try {
    console.log("🔍 Diagnostic des politiques RLS pour les avatars...\n");

    // 1. Vérifier le bucket
    console.log("1. Vérification du bucket avatars:");
    const { data: buckets, error: bucketError } = await supabaseAdmin.storage.listBuckets();
    
    if (bucketError) {
      console.error("❌ Erreur:", bucketError);
      return;
    }

    const avatarBucket = buckets?.find((bucket: any) => bucket.name === 'avatars');
    if (avatarBucket) {
      console.log("✅ Bucket trouvé:", {
        name: avatarBucket.name,
        public: avatarBucket.public,
        file_size_limit: avatarBucket.file_size_limit,
        allowed_mime_types: avatarBucket.allowed_mime_types,
      });
    } else {
      console.error("❌ Bucket 'avatars' non trouvé");
      return;
    }

    // 2. Vérifier les politiques existantes
    console.log("\n2. Vérification des politiques RLS:");
    
    // Utiliser une requête SQL directe
    const { data: policies, error: policyError } = await supabaseAdmin
      .rpc('exec_sql', {
        sql: `
          SELECT 
            policyname,
            cmd,
            permissive,
            roles,
            qual,
            with_check
          FROM pg_policies 
          WHERE schemaname = 'storage' 
          AND tablename = 'objects'
          AND policyname LIKE '%avatar%'
          ORDER BY policyname;
        `
      });

    if (policyError) {
      console.warn("⚠️ Impossible de vérifier les politiques via RPC:", policyError.message);
      
      // Essayer une approche alternative
      console.log("Tentative de vérification alternative...");
      
      // Test d'upload simple pour voir l'erreur exacte
      const testFileName = 'test-file.txt';
      const testContent = 'test content';
      
      const { error: uploadError } = await supabaseAdmin.storage
        .from('avatars')
        .upload(testFileName, testContent);
        
      if (uploadError) {
        console.log("❌ Erreur d'upload de test:", uploadError);
      } else {
        console.log("✅ Upload de test réussi");
        // Nettoyer le fichier de test
        await supabaseAdmin.storage.from('avatars').remove([testFileName]);
      }
      
    } else {
      if (policies && policies.length > 0) {
        console.log("✅ Politiques trouvées:");
        policies.forEach((policy: any) => {
          console.log(`   - ${policy.policyname} (${policy.cmd})`);
        });
      } else {
        console.log("❌ Aucune politique trouvée pour les avatars");
      }
    }

    // 3. Vérifier RLS activé sur la table
    console.log("\n3. Vérification RLS sur storage.objects:");
    const { data: rlsStatus, error: rlsError } = await supabaseAdmin
      .rpc('exec_sql', {
        sql: `
          SELECT 
            schemaname,
            tablename,
            rowsecurity
          FROM pg_tables 
          WHERE schemaname = 'storage' 
          AND tablename = 'objects';
        `
      });

    if (rlsError) {
      console.warn("⚠️ Impossible de vérifier le statut RLS:", rlsError.message);
    } else {
      console.log("RLS Status:", rlsStatus);
    }

    console.log("\n🎯 Recommandations:");
    console.log("1. Créer les politiques RLS manuellement dans Supabase Dashboard");
    console.log("2. Aller sur: https://supabase.com/dashboard");
    console.log("3. SQL Editor → Exécuter le script avatar_policies.sql");

  } catch (error) {
    console.error("❌ Erreur lors du diagnostic:", error);
  }
}

// Exécuter le diagnostic
if (require.main === module) {
  debugAvatarPolicies()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { debugAvatarPolicies };
