# 🚀 Guide de Migration Financière - Party Organizer

## 📋 Instructions pour appliquer les modifications de base de données

### 🔗 Étape 1 : Accéder à Supabase
1. Allez sur [supabase.com](https://supabase.com)
2. Connectez-vous à votre projet Party Organizer
3. C<PERSON>z sur "SQL Editor" dans le menu de gauche

### 💾 Étape 2 : Exécuter les migrations SQL

Copiez et exécutez les requêtes suivantes **une par une** dans l'éditeur SQL :

#### 🔧 Migration 1 : Ajouter les colonnes financières à la table items
```sql
-- Ajouter les colonnes pour les coûts réels et qui a payé
ALTER TABLE items 
ADD COLUMN IF NOT EXISTS actual_cost DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS paid_by_participant_id BIGINT REFERENCES participants(id) ON DELETE SET NULL;
```

#### 🔧 Migration 2 : Créer la table des transactions financières
```sql
-- Table pour gérer les transactions entre participants
CREATE TABLE IF NOT EXISTS financial_transactions (
    id BIGSERIAL PRIMARY KEY,
    event_id BIGINT NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    from_participant_id BIGINT NOT NULL REFERENCES participants(id) ON DELETE CASCADE,
    to_participant_id BIGINT NOT NULL REFERENCES participants(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    description TEXT,
    is_settled BOOLEAN NOT NULL DEFAULT FALSE,
    settled_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

#### 🔧 Migration 3 : Créer la table des résumés financiers
```sql
-- Table pour stocker les calculs financiers (cache)
CREATE TABLE IF NOT EXISTS financial_summaries (
    id BIGSERIAL PRIMARY KEY,
    event_id BIGINT NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    participant_id BIGINT NOT NULL REFERENCES participants(id) ON DELETE CASCADE,
    total_spent DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_owed DECIMAL(10,2) NOT NULL DEFAULT 0,
    net_balance DECIMAL(10,2) NOT NULL DEFAULT 0,
    last_calculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(event_id, participant_id)
);
```

#### 🔧 Migration 4 : Créer les index pour les performances
```sql
-- Index pour optimiser les requêtes
CREATE INDEX IF NOT EXISTS idx_financial_transactions_event_id ON financial_transactions(event_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_from_participant ON financial_transactions(from_participant_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_to_participant ON financial_transactions(to_participant_id);
CREATE INDEX IF NOT EXISTS idx_financial_summaries_event_id ON financial_summaries(event_id);
CREATE INDEX IF NOT EXISTS idx_items_paid_by ON items(paid_by_participant_id);
```

#### 🔧 Migration 5 : Activer la sécurité RLS
```sql
-- Activer Row Level Security sur les nouvelles tables
ALTER TABLE financial_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_summaries ENABLE ROW LEVEL SECURITY;
```

#### 🔧 Migration 6 : Créer les politiques de sécurité
```sql
-- Politique pour les transactions financières
CREATE POLICY "Users can view financial transactions for their events" ON financial_transactions
    FOR SELECT USING (
        event_id IN (
            SELECT e.id FROM events e
            JOIN participants p ON e.id = p.event_id
            WHERE p.user_id = auth.uid() AND p.status = 'accepted'
        )
    );

CREATE POLICY "Organizers can manage financial transactions" ON financial_transactions
    FOR ALL USING (
        event_id IN (
            SELECT id FROM events WHERE organizer_id = auth.uid()
        )
    );

-- Politique pour les résumés financiers
CREATE POLICY "Users can view financial summaries for their events" ON financial_summaries
    FOR SELECT USING (
        event_id IN (
            SELECT e.id FROM events e
            JOIN participants p ON e.id = p.event_id
            WHERE p.user_id = auth.uid() AND p.status = 'accepted'
        )
    );
```

### ✅ Étape 3 : Vérification
Après avoir exécuté toutes les migrations, vérifiez que tout fonctionne :

```sql
-- Vérifier que les nouvelles colonnes existent
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'items' 
AND column_name IN ('actual_cost', 'paid_by_participant_id');

-- Vérifier que les nouvelles tables existent
SELECT table_name 
FROM information_schema.tables 
WHERE table_name IN ('financial_transactions', 'financial_summaries');
```

### 🎯 Étape 4 : Test des fonctionnalités
1. Allez dans votre app Party Organizer
2. Créez ou ouvrez un événement
3. Cliquez sur le menu "..." → "Gestion financière"
4. Testez l'ajout de coûts réels aux items

## 🚨 En cas de problème

### Erreur "relation does not exist"
- Vérifiez que vous êtes dans le bon projet Supabase
- Assurez-vous d'avoir exécuté toutes les migrations précédentes

### Erreur de permissions
- Vérifiez que vous utilisez le bon rôle (service_role pour les migrations)
- Les politiques RLS peuvent bloquer certaines opérations

### Erreur de contrainte
- Vérifiez que les tables référencées (events, participants) existent
- Assurez-vous que les données existantes sont cohérentes

## 📞 Support
Si vous rencontrez des problèmes, vérifiez :
1. Les logs d'erreur dans la console Supabase
2. Les messages d'erreur dans l'app
3. La structure des tables existantes

---

**Une fois les migrations appliquées, votre app aura toutes les fonctionnalités financières automatiques ! 🎉**
