import React, { useState } from "react";
import { View, ScrollView, Platform } from "react-native";
import { useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { useOnboarding } from "~/hooks/useOnboarding";
import { OnboardingProfile } from "~/lib/onboardingService";
import { OnboardingProgressBar } from "~/components/onboarding/OnboardingComponents";

export default function OnboardingProfileScreen() {
  const router = useRouter();
  const { completeStep, updateProfile, progress } = useOnboarding();
  
  const [userType, setUserType] = useState<OnboardingProfile['userType']>('unknown');
  const [experience, setExperience] = useState<OnboardingProfile['experience']>('beginner');
  const [interests, setInterests] = useState<string[]>([]);
  const [goals, setGoals] = useState<string[]>([]);

  const userTypes = [
    {
      id: 'organizer' as const,
      title: 'Organisateur',
      description: 'Je veux organiser des événements pour mes amis/famille',
      icon: '🎯',
    },
    {
      id: 'participant' as const,
      title: 'Participant',
      description: 'Je veux principalement participer aux événements',
      icon: '👥',
    },
    {
      id: 'both' as const,
      title: 'Les deux',
      description: 'J\'organise et participe selon les occasions',
      icon: '🔄',
    },
  ];

  const experienceLevels = [
    {
      id: 'beginner' as const,
      title: 'Débutant',
      description: 'Première fois que j\'organise des événements',
      icon: '🌱',
    },
    {
      id: 'intermediate' as const,
      title: 'Intermédiaire',
      description: 'J\'ai déjà organisé quelques événements',
      icon: '🌿',
    },
    {
      id: 'expert' as const,
      title: 'Expert',
      description: 'J\'organise régulièrement des événements',
      icon: '🌳',
    },
  ];

  const availableInterests = [
    '🎉 Soirées', '🍕 Repas', '🎮 Gaming', '🏃 Sport', 
    '🎬 Cinéma', '🎵 Musique', '🌳 Nature', '🎨 Culture',
    '🍻 Bars', '🏖️ Voyages', '🎂 Anniversaires', '🎄 Fêtes'
  ];

  const availableGoals = [
    '⚡ Organiser plus rapidement',
    '💰 Mieux gérer les finances',
    '👥 Coordonner les participants',
    '📊 Analyser mes événements',
    '🎯 Améliorer la qualité',
    '🔄 Automatiser les tâches'
  ];

  const toggleInterest = (interest: string) => {
    setInterests(prev => 
      prev.includes(interest) 
        ? prev.filter(i => i !== interest)
        : [...prev, interest]
    );
  };

  const toggleGoal = (goal: string) => {
    setGoals(prev => 
      prev.includes(goal) 
        ? prev.filter(g => g !== goal)
        : [...prev, goal]
    );
  };

  const handleContinue = async () => {
    if (userType === 'unknown') {
      return;
    }

    const profileUpdates: Partial<OnboardingProfile> = {
      userType,
      experience,
      interests,
      goals,
    };

    await updateProfile(profileUpdates);
    await completeStep('profile_setup');
    router.push('/onboarding/tutorial');
  };

  const canContinue = userType !== 'unknown';

  return (
    <ScrollView className="flex-1 bg-background">
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto p-6" : "p-6"}>
        
        {/* Progress */}
        <OnboardingProgressBar progress={progress} />

        {/* Header */}
        <View className="mb-6">
          <Text className="text-2xl font-bold mb-2">
            👤 Configurons votre profil
          </Text>
          <Text className="text-muted-foreground">
            Aidez-nous à personnaliser votre expérience Party Organizer
          </Text>
        </View>

        {/* Type d'utilisateur */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Quel est votre profil principal ?</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-3">
              {userTypes.map((type) => (
                <Button
                  key={type.id}
                  variant={userType === type.id ? "default" : "outline"}
                  onPress={() => setUserType(type.id)}
                  className="h-auto p-4"
                >
                  <View className="flex-row items-center gap-3 w-full">
                    <Text className="text-2xl">{type.icon}</Text>
                    <View className="flex-1 items-start">
                      <Text className={`font-semibold ${userType === type.id ? 'text-primary-foreground' : 'text-foreground'}`}>
                        {type.title}
                      </Text>
                      <Text className={`text-sm ${userType === type.id ? 'text-primary-foreground/80' : 'text-muted-foreground'}`}>
                        {type.description}
                      </Text>
                    </View>
                  </View>
                </Button>
              ))}
            </View>
          </CardContent>
        </Card>

        {/* Niveau d'expérience */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Quel est votre niveau d'expérience ?</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-3">
              {experienceLevels.map((level) => (
                <Button
                  key={level.id}
                  variant={experience === level.id ? "default" : "outline"}
                  onPress={() => setExperience(level.id)}
                  className="h-auto p-4"
                >
                  <View className="flex-row items-center gap-3 w-full">
                    <Text className="text-2xl">{level.icon}</Text>
                    <View className="flex-1 items-start">
                      <Text className={`font-semibold ${experience === level.id ? 'text-primary-foreground' : 'text-foreground'}`}>
                        {level.title}
                      </Text>
                      <Text className={`text-sm ${experience === level.id ? 'text-primary-foreground/80' : 'text-muted-foreground'}`}>
                        {level.description}
                      </Text>
                    </View>
                  </View>
                </Button>
              ))}
            </View>
          </CardContent>
        </Card>

        {/* Centres d'intérêt */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Quels types d'événements vous intéressent ?</CardTitle>
            <Text className="text-sm text-muted-foreground">
              Sélectionnez tous ceux qui vous plaisent
            </Text>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="flex-row flex-wrap gap-2">
              {availableInterests.map((interest) => (
                <Button
                  key={interest}
                  variant={interests.includes(interest) ? "default" : "outline"}
                  size="sm"
                  onPress={() => toggleInterest(interest)}
                >
                  <Text className={`text-xs ${interests.includes(interest) ? 'text-primary-foreground' : 'text-foreground'}`}>
                    {interest}
                  </Text>
                </Button>
              ))}
            </View>
          </CardContent>
        </Card>

        {/* Objectifs */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Quels sont vos objectifs ?</CardTitle>
            <Text className="text-sm text-muted-foreground">
              Que souhaitez-vous améliorer dans l'organisation d'événements ?
            </Text>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-2">
              {availableGoals.map((goal) => (
                <Button
                  key={goal}
                  variant={goals.includes(goal) ? "default" : "outline"}
                  onPress={() => toggleGoal(goal)}
                  className="h-auto p-3"
                >
                  <View className="flex-row items-center w-full">
                    <Text className={`text-sm ${goals.includes(goal) ? 'text-primary-foreground' : 'text-foreground'}`}>
                      {goal}
                    </Text>
                  </View>
                </Button>
              ))}
            </View>
          </CardContent>
        </Card>

        {/* Actions */}
        <View className="gap-3">
          <Button 
            onPress={handleContinue}
            disabled={!canContinue}
            className="h-12"
          >
            <Text className="text-primary-foreground font-semibold">
              Continuer →
            </Text>
          </Button>
          
          <Button 
            variant="outline" 
            onPress={() => router.back()}
            className="h-12"
          >
            <Text>← Retour</Text>
          </Button>
        </View>

        {/* Aide */}
        {!canContinue && (
          <Card className="mt-4 border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950">
            <CardContent className="p-3">
              <Text className="text-sm text-yellow-700 dark:text-yellow-300">
                💡 Sélectionnez votre profil principal pour continuer
              </Text>
            </CardContent>
          </Card>
        )}
      </View>
    </ScrollView>
  );
}
