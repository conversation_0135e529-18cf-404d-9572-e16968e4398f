import axios from "axios";

export type GeocodeResult = {
  lat: number;
  lng: number;
  formattedAddress?: string;
};

export enum GeocodeError {
  INVALID_INPUT = "Invalid address input",
  API_ERROR = "Geocoding API error",
  NO_RESULTS = "No results found",
  PERMISSION_DENIED = "Location permission denied",
}

export const geocodeAddress = async (
  address: string
): Promise<GeocodeResult> => {
  if (!address || typeof address !== "string" || address.trim().length < 3) {
    throw new Error(GeocodeError.INVALID_INPUT);
  }

  const apiKey = process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY;
  if (!apiKey) {
    console.error(
      "Google Maps API key missing. Please configure EXPO_PUBLIC_GOOGLE_MAPS_API_KEY in your .env file"
    );
    throw new Error(GeocodeError.API_ERROR);
  }

  try {
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json`,
      {
        params: {
          address: address.trim(),
          key: apiKey,
        },
      }
    );

    if (response.data.status === "OK") {
      const result = response.data.results[0];
      return {
        lat: result.geometry.location.lat,
        lng: result.geometry.location.lng,
        formattedAddress: result.formatted_address,
      };
    } else if (response.data.status === "ZERO_RESULTS") {
      throw new Error(GeocodeError.NO_RESULTS);
    } else {
      throw new Error(`${GeocodeError.API_ERROR}: ${response.data.status}`);
    }
  } catch (error) {
    console.error("Geocoding error:", error);
    throw error instanceof Error ? error : new Error(GeocodeError.API_ERROR);
  }
};

export const getCurrentPosition = async (): Promise<GeocodeResult> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error(GeocodeError.PERMISSION_DENIED));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        });
      },
      (error) => {
        reject(
          new Error(
            error.code === error.PERMISSION_DENIED
              ? GeocodeError.PERMISSION_DENIED
              : GeocodeError.API_ERROR
          )
        );
      },
      { enableHighAccuracy: true, timeout: 10000 }
    );
  });
};
