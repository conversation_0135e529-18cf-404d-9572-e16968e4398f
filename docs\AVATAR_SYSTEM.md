# 🎭 Système d'Avatars - Party Organizer

## 📋 Vue d'ensemble

Le système d'avatars de Party Organizer permet aux utilisateurs de personnaliser leur profil avec :
- **Photos de profil** : Upload d'images depuis la galerie ou la caméra
- **Emojis aléatoires** : Génération automatique d'emojis de visages
- **Emojis personnalis<PERSON>** : Sélection manuelle d'emojis de visages

## 🏗️ Architecture

### Services
- **`avatarService.ts`** : Service principal pour la gestion des avatars
- **`AvatarPicker.tsx`** : Composant de sélection d'avatar
- **`Avatar.tsx`** : Composant d'affichage d'avatar

### Stockage
- **Supabase Storage** : Bucket `avatars` pour les photos de profil
- **Base de données** : Champ `avatar_url` dans la table `profiles`

## 🚀 Installation et Configuration

### 1. Dépendances
Les dépendances suivantes sont automatiquement installées :
```bash
npm install expo-image-picker expo-file-system
```

### 2. Configuration Supabase
Initialiser le bucket avatars :
```bash
npm run init-avatars
```

### 3. Politiques RLS
Créer manuellement dans Supabase SQL Editor :

```sql
-- Lecture publique
CREATE POLICY "Public read access for avatars" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

-- Upload pour utilisateurs authentifiés
CREATE POLICY "Authenticated users can upload avatars" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Mise à jour de ses propres avatars
CREATE POLICY "Users can update their own avatars" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Suppression de ses propres avatars
CREATE POLICY "Users can delete their own avatars" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
```

## 💻 Utilisation

### Composant Avatar
```tsx
import { Avatar } from '~/components/Avatar';

// Avatar basique
<Avatar avatarUrl={user.avatar_url} size="md" />

// Avatar avec fallback
<Avatar 
  avatarUrl={user.avatar_url} 
  size="lg" 
  fallbackEmoji="👤"
/>

// Avatar avec statut
<AvatarWithStatus 
  avatarUrl={user.avatar_url}
  status="online"
  showStatus={true}
/>

// Groupe d'avatars
<AvatarGroup 
  avatars={participants}
  max={3}
  size="sm"
/>
```

### Composant AvatarPicker
```tsx
import { AvatarPicker } from '~/components/AvatarPicker';

const [showPicker, setShowPicker] = useState(false);

const handleAvatarSelected = (avatarData: AvatarData) => {
  // Mettre à jour le profil utilisateur
  updateProfile({ avatar_url: avatarData.value });
};

<AvatarPicker
  isVisible={showPicker}
  currentAvatar={user.avatar_url}
  userId={user.id}
  onAvatarSelected={handleAvatarSelected}
  onClose={() => setShowPicker(false)}
/>
```

### Service Avatar
```tsx
import { avatarService } from '~/lib/avatarService';

// Générer un emoji aléatoire
const randomEmoji = avatarService.generateRandomFaceEmoji();

// Déterminer le type d'avatar
const avatarData = avatarService.getAvatarType(avatarUrl);

// Upload d'image
const result = await avatarService.uploadImage(imageUri, userId);
```

## 🎨 Personnalisation

### Tailles d'avatar
- `sm` : 32x32px (w-8 h-8)
- `md` : 48x48px (w-12 h-12) - par défaut
- `lg` : 64x64px (w-16 h-16)
- `xl` : 80x80px (w-20 h-20)

### Emojis de visages
Le système utilise une liste prédéfinie de 200+ emojis de visages incluant :
- Expressions faciales (😀, 😊, 😢, etc.)
- Personnes (👨, 👩, 👶, etc.)
- Professions (👮, 👩‍⚕️, 👨‍🍳, etc.)
- Fantaisie (🤖, 👽, 🎃, etc.)

## 🧪 Tests

Exécuter les tests du système :
```bash
npm run test-avatars
```

## 🔧 Dépannage

### Problèmes courants

1. **Erreur de permissions**
   - Vérifier les politiques RLS dans Supabase
   - S'assurer que l'utilisateur est authentifié

2. **Upload d'image échoue**
   - Vérifier la taille du fichier (max 5MB)
   - Vérifier le format (JPEG, PNG, WebP)
   - Vérifier la connexion réseau

3. **Avatar ne s'affiche pas**
   - Vérifier l'URL dans la base de données
   - Vérifier les permissions du bucket
   - Utiliser le fallback emoji

### Logs de débogage
```tsx
// Activer les logs détaillés
console.log('Avatar data:', avatarService.getAvatarType(avatarUrl));
```

## 📱 Compatibilité

- ✅ **iOS** : Caméra + Galerie
- ✅ **Android** : Caméra + Galerie  
- ✅ **Web** : Sélection de fichiers
- ✅ **Cross-platform** : Emojis universels

## 🔒 Sécurité

- Upload limité aux utilisateurs authentifiés
- Taille de fichier limitée (5MB)
- Types MIME autorisés uniquement
- Isolation par utilisateur (RLS)
- Suppression automatique des anciens avatars

## 🚀 Améliorations futures

- [ ] Redimensionnement automatique des images
- [ ] Compression d'images
- [ ] Cache local des avatars
- [ ] Support des GIFs animés
- [ ] Filtres et effets photo
- [ ] Synchronisation hors-ligne
