/**
 * Service d'avatars utilisant base64 au lieu de Supabase Storage
 * Solution de contournement pour éviter les problèmes RLS
 */

import { Platform } from "react-native";
import * as FileSystem from "expo-file-system";

export interface AvatarUploadResult {
  success: boolean;
  avatarUrl?: string;
  error?: string;
}

export class AvatarServiceBase64 {
  /**
   * Upload une image et la convertit en base64
   */
  async uploadImage(
    imageUri: string,
    userId: string
  ): Promise<AvatarUploadResult> {
    try {
      console.log(`Upload base64 pour l'utilisateur: ${userId}`);

      let base64Data: string;
      let mimeType: string = "image/jpeg";

      if (Platform.OS === "web") {
        // Sur web, convertir l'URI en base64
        try {
          const response = await fetch(imageUri);
          const blob = await response.blob();
          mimeType = blob.type || "image/jpeg";
          
          // Convertir le blob en base64
          const reader = new FileReader();
          base64Data = await new Promise((resolve, reject) => {
            reader.onload = () => {
              const result = reader.result as string;
              // Extraire seulement la partie base64 (après la virgule)
              const base64 = result.split(',')[1];
              resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          });
        } catch (fetchError) {
          console.error(
            "Erreur lors de la récupération du fichier:",
            fetchError
          );
          return {
            success: false,
            error: "Impossible de récupérer le fichier sélectionné",
          };
        }
      } else {
        // Sur mobile, lire le fichier en base64
        try {
          const fileInfo = await FileSystem.getInfoAsync(imageUri);
          if (!fileInfo.exists) {
            return {
              success: false,
              error: "Le fichier sélectionné n'existe pas",
            };
          }

          base64Data = await FileSystem.readAsStringAsync(imageUri, {
            encoding: FileSystem.EncodingType.Base64,
          });

          // Déterminer le type MIME basé sur l'URI
          if (imageUri.toLowerCase().includes(".png")) {
            mimeType = "image/png";
          } else if (imageUri.toLowerCase().includes(".webp")) {
            mimeType = "image/webp";
          } else {
            mimeType = "image/jpeg";
          }
        } catch (fileError) {
          console.error("Erreur lors de la lecture du fichier:", fileError);
          return {
            success: false,
            error: "Impossible de lire le fichier sélectionné",
          };
        }
      }

      // Vérifier la taille approximative (base64 est ~33% plus gros que l'original)
      const approximateSize = (base64Data.length * 3) / 4;
      if (approximateSize > 5 * 1024 * 1024) {
        return {
          success: false,
          error: "Le fichier est trop volumineux (maximum 5MB)",
        };
      }

      // Créer l'URL data pour l'avatar
      const avatarUrl = `data:${mimeType};base64,${base64Data}`;

      console.log(`Avatar converti en base64, taille: ${Math.round(approximateSize / 1024)} KB`);

      return {
        success: true,
        avatarUrl: avatarUrl,
      };
    } catch (error) {
      console.error("Erreur lors de l'upload d'image:", error);
      return {
        success: false,
        error: "Erreur lors de l'upload de l'image",
      };
    }
  }

  /**
   * Génère un emoji de visage aléatoire
   */
  generateRandomFaceEmoji(): string {
    const faceEmojis = [
      "😀", "😃", "😄", "😁", "😆", "😅", "🤣", "😂", "🙂", "🙃",
      "😉", "😊", "😇", "🥰", "😍", "🤩", "😘", "😗", "😚", "😙",
      "😋", "😛", "😜", "🤪", "😝", "🤑", "🤗", "🤭", "🤫", "🤔",
      "🤐", "🤨", "😐", "😑", "😶", "😏", "😒", "🙄", "😬", "🤥",
      "😔", "😪", "🤤", "😴", "😷", "🤒", "🤕", "🤢", "🤮", "🤧",
      "🥵", "🥶", "🥴", "😵", "🤯", "🤠", "🥳", "😎", "🤓", "🧐",
      "👶", "🧒", "👦", "👧", "🧑", "👱", "👨", "🧔", "👩", "🧓",
      "👴", "👵", "🙍", "🙎", "🙅", "🙆", "💁", "🙋", "🧏", "🙇",
      "🤦", "🤷", "👮", "🕵", "💂", "👷", "🤴", "👸", "👳", "👲",
      "🧕", "🤵", "👰", "🤰", "🤱", "👼", "🎅", "🤶", "🦸", "🦹",
      "🧙", "🧚", "🧛", "🧜", "🧝", "🧞", "🧟", "💆", "💇", "🚶",
      "🏃", "💃", "🕺", "🕴", "👯", "🧖", "🧗", "🤺", "🏇", "⛷",
      "🏂", "🏌", "🏄", "🚣", "🏊", "⛹", "🏋", "🚴", "🚵", "🤸",
      "🤼", "🤽", "🤾", "🤹", "🧘", "🛀", "🛌"
    ];
    
    return faceEmojis[Math.floor(Math.random() * faceEmojis.length)];
  }

  /**
   * Valide qu'un emoji est un visage
   */
  isValidFaceEmoji(emoji: string): boolean {
    const faceEmojiPattern = /^[\u{1F600}-\u{1F64F}\u{1F910}-\u{1F96B}\u{1F970}-\u{1F978}\u{1F97A}\u{1F97C}-\u{1F9FF}\u{1F468}-\u{1F469}\u{1F9D0}-\u{1F9DF}\u{1F466}-\u{1F467}\u{1F474}-\u{1F475}\u{1F476}\u{1F934}-\u{1F935}\u{1F936}\u{1F9B8}-\u{1F9B9}\u{1F9D1}-\u{1F9DD}]$/u;
    return faceEmojiPattern.test(emoji);
  }

  /**
   * Détermine le type d'avatar basé sur l'URL
   */
  getAvatarType(avatarUrl: string): "emoji" | "image" | "default" {
    if (!avatarUrl) return "default";
    if (avatarUrl.startsWith("data:image/")) return "image";
    if (this.isValidFaceEmoji(avatarUrl)) return "emoji";
    return "default";
  }
}

// Instance singleton
export const avatarServiceBase64 = new AvatarServiceBase64();
