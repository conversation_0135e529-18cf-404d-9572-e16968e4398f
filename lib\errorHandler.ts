import { showToast } from './toastService';

// Types d'erreurs
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER_ERROR = 'SERVER_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  UNKNOWN = 'UNKNOWN',
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface AppError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  code?: string;
  context?: Record<string, any>;
  timestamp: number;
  stack?: string;
  retryable: boolean;
}

export interface ErrorHandlerConfig {
  showToasts: boolean;
  logErrors: boolean;
  reportErrors: boolean;
  maxRetries: number;
  retryDelay: number;
}

class ErrorHandler {
  private config: ErrorHandlerConfig = {
    showToasts: true,
    logErrors: true,
    reportErrors: false, // Désactivé pour l'instant
    maxRetries: 3,
    retryDelay: 1000,
  };

  private errorLog: AppError[] = [];
  private maxLogSize = 100;

  /**
   * Configure le gestionnaire d'erreurs
   */
  configure(config: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Crée un objet AppError standardisé
   */
  createError(
    type: ErrorType,
    severity: ErrorSeverity,
    message: string,
    userMessage: string,
    options: {
      code?: string;
      context?: Record<string, any>;
      retryable?: boolean;
      originalError?: Error;
    } = {}
  ): AppError {
    return {
      type,
      severity,
      message,
      userMessage,
      code: options.code,
      context: options.context,
      timestamp: Date.now(),
      stack: options.originalError?.stack,
      retryable: options.retryable ?? false,
    };
  }

  /**
   * Gère une erreur de manière centralisée
   */
  async handle(error: AppError | Error | unknown): Promise<void> {
    let appError: AppError;

    if (this.isAppError(error)) {
      appError = error;
    } else if (error instanceof Error) {
      appError = this.parseError(error);
    } else {
      appError = this.createError(
        ErrorType.UNKNOWN,
        ErrorSeverity.MEDIUM,
        'Une erreur inconnue s\'est produite',
        'Une erreur inattendue s\'est produite. Veuillez réessayer.',
        { context: { originalError: error } }
      );
    }

    // Logger l'erreur
    if (this.config.logErrors) {
      this.logError(appError);
    }

    // Afficher un toast si configuré
    if (this.config.showToasts && appError.severity !== ErrorSeverity.LOW) {
      this.showErrorToast(appError);
    }

    // Rapporter l'erreur si configuré
    if (this.config.reportErrors && appError.severity === ErrorSeverity.CRITICAL) {
      await this.reportError(appError);
    }
  }

  /**
   * Vérifie si l'objet est un AppError
   */
  private isAppError(error: any): error is AppError {
    return error && typeof error === 'object' && 'type' in error && 'severity' in error;
  }

  /**
   * Parse une erreur JavaScript standard en AppError
   */
  private parseError(error: Error): AppError {
    // Erreurs réseau
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return this.createError(
        ErrorType.NETWORK,
        ErrorSeverity.MEDIUM,
        error.message,
        'Problème de connexion. Vérifiez votre connexion internet.',
        { retryable: true, originalError: error }
      );
    }

    // Erreurs d'authentification
    if (error.message.includes('auth') || error.message.includes('unauthorized')) {
      return this.createError(
        ErrorType.AUTHENTICATION,
        ErrorSeverity.HIGH,
        error.message,
        'Problème d\'authentification. Veuillez vous reconnecter.',
        { originalError: error }
      );
    }

    // Erreurs de validation
    if (error.message.includes('validation') || error.message.includes('invalid')) {
      return this.createError(
        ErrorType.VALIDATION,
        ErrorSeverity.LOW,
        error.message,
        'Données invalides. Veuillez vérifier vos informations.',
        { originalError: error }
      );
    }

    // Erreur générique
    return this.createError(
      ErrorType.UNKNOWN,
      ErrorSeverity.MEDIUM,
      error.message,
      'Une erreur s\'est produite. Veuillez réessayer.',
      { retryable: true, originalError: error }
    );
  }

  /**
   * Log une erreur
   */
  private logError(error: AppError): void {
    console.error('AppError:', {
      type: error.type,
      severity: error.severity,
      message: error.message,
      code: error.code,
      context: error.context,
      timestamp: new Date(error.timestamp).toISOString(),
    });

    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }

    // Ajouter au log interne
    this.errorLog.unshift(error);
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }
  }

  /**
   * Affiche un toast d'erreur
   */
  private showErrorToast(error: AppError): void {
    const toastType = this.getToastType(error.severity);
    showToast(error.userMessage, { type: toastType });
  }

  /**
   * Convertit la sévérité en type de toast
   */
  private getToastType(severity: ErrorSeverity): 'error' | 'warning' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warning';
      case ErrorSeverity.LOW:
        return 'info';
      default:
        return 'error';
    }
  }

  /**
   * Rapporte une erreur critique (à implémenter avec un service de monitoring)
   */
  private async reportError(error: AppError): Promise<void> {
    try {
      // Ici on pourrait envoyer l'erreur à un service comme Sentry, Bugsnag, etc.
      console.log('Reporting critical error:', error);
      
      // Exemple d'implémentation avec un service externe
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(error),
      // });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  /**
   * Exécute une fonction avec gestion d'erreurs et retry automatique
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    context: string,
    maxRetries?: number
  ): Promise<T> {
    const retries = maxRetries ?? this.config.maxRetries;
    let lastError: Error;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === retries) {
          // Dernière tentative échouée
          const appError = this.createError(
            ErrorType.UNKNOWN,
            ErrorSeverity.HIGH,
            `${context} failed after ${retries + 1} attempts: ${lastError.message}`,
            'L\'opération a échoué après plusieurs tentatives.',
            { 
              context: { operation: context, attempts: retries + 1 },
              originalError: lastError 
            }
          );
          
          await this.handle(appError);
          throw appError;
        }

        // Attendre avant la prochaine tentative
        if (attempt < retries) {
          await this.delay(this.config.retryDelay * Math.pow(2, attempt)); // Backoff exponentiel
        }
      }
    }

    throw lastError!;
  }

  /**
   * Utilitaire pour attendre
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Récupère les erreurs récentes
   */
  getRecentErrors(limit = 10): AppError[] {
    return this.errorLog.slice(0, limit);
  }

  /**
   * Vide le log d'erreurs
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Récupère les statistiques d'erreurs
   */
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recentCount: number;
  } {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    const byType = {} as Record<ErrorType, number>;
    const bySeverity = {} as Record<ErrorSeverity, number>;
    let recentCount = 0;

    for (const error of this.errorLog) {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
      
      if (error.timestamp > oneHourAgo) {
        recentCount++;
      }
    }

    return {
      total: this.errorLog.length,
      byType,
      bySeverity,
      recentCount,
    };
  }
}

// Instance singleton
export const errorHandler = new ErrorHandler();

// Fonctions utilitaires
export const handleError = (error: AppError | Error | unknown) => errorHandler.handle(error);

export const createNetworkError = (message: string, userMessage?: string) =>
  errorHandler.createError(
    ErrorType.NETWORK,
    ErrorSeverity.MEDIUM,
    message,
    userMessage || 'Problème de connexion réseau',
    { retryable: true }
  );

export const createValidationError = (message: string, userMessage?: string) =>
  errorHandler.createError(
    ErrorType.VALIDATION,
    ErrorSeverity.LOW,
    message,
    userMessage || 'Données invalides',
    { retryable: false }
  );

export const createAuthError = (message: string, userMessage?: string) =>
  errorHandler.createError(
    ErrorType.AUTHENTICATION,
    ErrorSeverity.HIGH,
    message,
    userMessage || 'Problème d\'authentification',
    { retryable: false }
  );

export const withRetry = <T>(operation: () => Promise<T>, context: string, maxRetries?: number) =>
  errorHandler.withRetry(operation, context, maxRetries);
