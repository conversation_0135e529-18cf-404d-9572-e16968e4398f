import React, { useState, useEffect } from "react";
import { View, ScrollView, Platform, Alert } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Checkbox } from "~/components/ui/checkbox";
import { Badge } from "~/components/ui/badge";
import { EventTemplate, TemplateItem, EventFromTemplate } from "~/lib/types";
import { generateEventFromTemplate, generateItemsFromTemplate } from "~/lib/templateService";
import { EmojiPicker } from "~/components/EmojiPicker";

interface TemplateCustomizerProps {
  template: EventTemplate;
  onConfirm: (eventData: EventFromTemplate) => void;
  onBack: () => void;
}

export function TemplateCustomizer({ template, onConfirm, onBack }: TemplateCustomizerProps) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [date, setDate] = useState("");
  const [time, setTime] = useState(template.template_data.default_time);
  const [location, setLocation] = useState("");
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [customItems, setCustomItems] = useState<Omit<TemplateItem, 'id' | 'template_id' | 'created_at'>[]>([]);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState(template.icon);

  useEffect(() => {
    // Initialiser avec les valeurs par défaut du template
    const defaultTitle = template.template_data.title_template.replace('{theme}', '').replace('{lieu}', '').replace('{nom}', '').replace('{occasion}', '').replace('{jeu}', '').replace('{genre}', '').trim();
    setTitle(defaultTitle);
    setDescription(template.template_data.description_template || "");
    
    // Date par défaut (aujourd'hui + temps de préparation)
    const defaultDate = new Date();
    if (template.template_data.preparation_time) {
      defaultDate.setDate(defaultDate.getDate() + template.template_data.preparation_time);
    }
    setDate(defaultDate.toISOString().split('T')[0]);

    // Sélectionner tous les items essentiels par défaut
    if (template.template_items) {
      const essentialItems = template.template_items
        .filter(item => item.is_essential)
        .map(item => item.id);
      setSelectedItems(essentialItems);
    }
  }, [template]);

  const handleItemToggle = (itemId: number) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleAddCustomItem = () => {
    const newItem: Omit<TemplateItem, 'id' | 'template_id' | 'created_at'> = {
      name: "",
      category: null,
      estimated_cost: null,
      estimated_effort: null,
      is_essential: false,
      suggested_quantity: 1,
      description: null,
      order_index: customItems.length,
    };
    setCustomItems(prev => [...prev, newItem]);
  };

  const handleUpdateCustomItem = (index: number, field: string, value: any) => {
    setCustomItems(prev => 
      prev.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    );
  };

  const handleRemoveCustomItem = (index: number) => {
    setCustomItems(prev => prev.filter((_, i) => i !== index));
  };

  const handleConfirm = () => {
    if (!title.trim()) {
      Alert.alert("Erreur", "Le titre de l'événement est obligatoire");
      return;
    }

    if (!date) {
      Alert.alert("Erreur", "La date de l'événement est obligatoire");
      return;
    }

    // Valider les items personnalisés
    const validCustomItems = customItems.filter(item => item.name.trim() !== "");

    const eventFromTemplate: EventFromTemplate = {
      template,
      customizations: {
        title,
        description,
        date,
        time,
        location,
        selected_items: selectedItems,
        custom_items: validCustomItems,
      }
    };

    onConfirm(eventFromTemplate);
  };

  const getCostColor = (cost: string | null) => {
    switch (cost) {
      case "€": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "€€": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "€€€": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200";
    }
  };

  const getEffortColor = (effort: string | null) => {
    switch (effort) {
      case "easy": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "hard": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200";
    }
  };

  return (
    <ScrollView className="flex-1 bg-background">
      <View className={Platform.OS === "web" ? "max-w-4xl mx-auto p-4" : "p-4"}>
        
        {/* Header */}
        <View className="mb-6">
          <View className="flex-row items-center mb-2">
            <Button variant="ghost" size="sm" onPress={onBack} className="mr-2">
              <Text>← Retour</Text>
            </Button>
            <Text className="text-xl font-bold flex-1">
              Personnaliser "{template.name}"
            </Text>
          </View>
          <Text className="text-muted-foreground">
            Adaptez ce template à votre événement
          </Text>
        </View>

        {/* Informations de base */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Informations de l'événement</CardTitle>
          </CardHeader>
          <CardContent className="gap-4">
            {/* Icône */}
            <View>
              <Label>Icône de l'événement</Label>
              <View className="flex-row items-center gap-3 mt-1">
                <Button
                  variant="outline"
                  onPress={() => setShowEmojiPicker(true)}
                  className="w-16 h-16 items-center justify-center"
                >
                  <Text className="text-2xl">{selectedIcon}</Text>
                </Button>
                <Text className="text-sm text-muted-foreground">
                  Cliquez pour changer l'icône
                </Text>
              </View>
            </View>

            {/* Titre */}
            <View>
              <Label htmlFor="title">Titre de l'événement *</Label>
              <Input
                id="title"
                placeholder={template.template_data.title_template}
                value={title}
                onChangeText={setTitle}
                className="mt-1"
              />
            </View>

            {/* Description */}
            <View>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder={template.template_data.description_template || "Décrivez votre événement..."}
                value={description}
                onChangeText={setDescription}
                className="mt-1"
                numberOfLines={3}
              />
            </View>

            {/* Date et heure */}
            <View className="flex-row gap-3">
              <View className="flex-1">
                <Label htmlFor="date">Date *</Label>
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChangeText={setDate}
                  className="mt-1"
                />
              </View>
              <View className="flex-1">
                <Label htmlFor="time">Heure</Label>
                <Input
                  id="time"
                  type="time"
                  value={time}
                  onChangeText={setTime}
                  className="mt-1"
                />
              </View>
            </View>

            {/* Lieu */}
            <View>
              <Label htmlFor="location">Lieu</Label>
              <Input
                id="location"
                placeholder={template.template_data.suggested_location_types?.[0] || "Où aura lieu l'événement ?"}
                value={location}
                onChangeText={setLocation}
                className="mt-1"
              />
              {template.template_data.suggested_location_types && (
                <Text className="text-xs text-muted-foreground mt-1">
                  Suggestions : {template.template_data.suggested_location_types.join(", ")}
                </Text>
              )}
            </View>
          </CardContent>
        </Card>

        {/* Sélection des items */}
        {template.template_items && template.template_items.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Items suggérés</CardTitle>
              <Text className="text-sm text-muted-foreground">
                Sélectionnez les items que vous souhaitez inclure
              </Text>
            </CardHeader>
            <CardContent className="gap-3">
              {template.template_items.map((item) => (
                <View
                  key={item.id}
                  className="flex-row items-center justify-between p-3 bg-muted/30 rounded-lg"
                >
                  <View className="flex-row items-center flex-1">
                    <Checkbox
                      checked={selectedItems.includes(item.id)}
                      onCheckedChange={() => handleItemToggle(item.id)}
                      className="mr-3"
                    />
                    <View className="flex-1">
                      <View className="flex-row items-center gap-2 mb-1">
                        <Text className="font-medium">{item.name}</Text>
                        {item.is_essential && (
                          <Badge variant="destructive" className="px-1 py-0">
                            <Text className="text-xs text-white">Essentiel</Text>
                          </Badge>
                        )}
                      </View>
                      
                      <View className="flex-row items-center gap-2">
                        {item.category && (
                          <Text className="text-xs text-muted-foreground">
                            {item.category}
                          </Text>
                        )}
                        {item.estimated_cost && (
                          <Badge className={getCostColor(item.estimated_cost)}>
                            <Text className="text-xs">{item.estimated_cost}</Text>
                          </Badge>
                        )}
                        {item.estimated_effort && (
                          <Badge className={getEffortColor(item.estimated_effort)}>
                            <Text className="text-xs">
                              {item.estimated_effort === "easy" ? "Facile" :
                               item.estimated_effort === "medium" ? "Moyen" : "Difficile"}
                            </Text>
                          </Badge>
                        )}
                      </View>
                    </View>
                  </View>
                </View>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Items personnalisés */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Items personnalisés</CardTitle>
            <Text className="text-sm text-muted-foreground">
              Ajoutez vos propres items à la liste
            </Text>
          </CardHeader>
          <CardContent className="gap-3">
            {customItems.map((item, index) => (
              <View key={index} className="p-3 bg-muted/30 rounded-lg">
                <View className="flex-row items-center gap-3 mb-2">
                  <Input
                    placeholder="Nom de l'item"
                    value={item.name}
                    onChangeText={(value) => handleUpdateCustomItem(index, 'name', value)}
                    className="flex-1"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onPress={() => handleRemoveCustomItem(index)}
                  >
                    <Text className="text-destructive">✕</Text>
                  </Button>
                </View>
                <Input
                  placeholder="Catégorie (optionnel)"
                  value={item.category || ""}
                  onChangeText={(value) => handleUpdateCustomItem(index, 'category', value || null)}
                />
              </View>
            ))}
            
            <Button
              variant="outline"
              onPress={handleAddCustomItem}
              className="border-dashed"
            >
              <Text>+ Ajouter un item</Text>
            </Button>
          </CardContent>
        </Card>

        {/* Actions */}
        <View className="flex-row gap-3 mb-8">
          <Button variant="outline" onPress={onBack} className="flex-1">
            <Text>Annuler</Text>
          </Button>
          <Button onPress={handleConfirm} className="flex-1">
            <Text className="text-primary-foreground font-medium">
              🚀 Créer l'événement
            </Text>
          </Button>
        </View>

        {/* EmojiPicker Modal */}
        {showEmojiPicker && (
          <View className="absolute inset-0 bg-black/50 items-center justify-center z-50">
            <View className="bg-background rounded-lg p-4 m-4 max-w-sm w-full">
              <EmojiPicker
                onEmojiSelected={(emoji) => {
                  setSelectedIcon(emoji);
                  setShowEmojiPicker(false);
                }}
                onClose={() => setShowEmojiPicker(false)}
              />
            </View>
          </View>
        )}
      </View>
    </ScrollView>
  );
}
