# 🧪 **GUIDE DE TEST - PARTY ORGANIZER MVP**

## 👋 **BIENVENUE TESTEUR !**

Merci de tester notre application **Party Organizer** ! Ce guide vous explique comment tester toutes les fonctionnalités.

---

## 🚀 **ACCÈS À L'APPLICATION**

### **Option 1 : Web (Recommandé)**
- 🌐 **URL** : `http://localhost:8082` (ou l'URL fournie)
- 💻 **Compatible** : Chrome, Firefox, Safari, Edge

### **Option 2 : Mobile (Expo Go)**
- 📱 **Installer** : [Expo Go](https://expo.dev/client) sur votre téléphone
- 📷 **Scanner** : Le QR code fourni
- ⚡ **Avantage** : Expérience mobile native

---

## 🎯 **SCÉNARIOS DE TEST**

### **SCÉNARIO 1 : REJOINDRE UN ÉVÉNEMENT (5 min)**

#### **Étape 1 : Accéder à l'événement**
1. 🔗 Cliquez sur le lien d'invitation fourni
2. 📱 L'app s'ouvre sur la page de participation

#### **Étape 2 : Choisir votre identité**
1. 👤 Sélectionnez votre nom dans la liste :
   - **Alice** (organisatrice)
   - **Bob** (participant)
   - **Charlie** (participant)
   - **Diana** (participant)
2. ✅ Confirmez votre participation

#### **Étape 3 : Explorer l'événement**
1. 📋 Consultez les détails de l'événement
2. 👥 Voir la liste des participants
3. 📝 Parcourir les items à apporter

---

### **SCÉNARIO 2 : GÉRER VOS TÂCHES (10 min)**

#### **Étape 1 : Voir vos tâches assignées**
1. 🏠 Aller sur l'onglet "Mes Tâches"
2. 📋 Voir les items qui vous sont attribués
3. 💰 Noter le coût estimé et la difficulté

#### **Étape 2 : Marquer des tâches terminées**
1. ✅ Cocher des items comme "apportés"
2. 🔄 Voir la mise à jour en temps réel
3. 📊 Observer les statistiques de progression

#### **Étape 3 : Tester la synchronisation**
1. 🔄 Rafraîchir la page/app
2. ✅ Vérifier que vos changements sont sauvegardés
3. 👥 Demander à un autre testeur de vérifier

---

### **SCÉNARIO 3 : INTERACTION COLLABORATIVE (15 min)**

#### **Coordination avec d'autres testeurs**
1. 💬 **Communication** : Utilisez un chat (Discord, WhatsApp, etc.)
2. 🔄 **Actions simultanées** :
   - Marquer des items en même temps
   - Voir les mises à jour des autres
   - Tester les conflits de données

#### **Tests spécifiques**
1. 🎯 **Attribution** : Demander une réattribution d'item
2. 📱 **Multi-plateforme** : Un testeur sur mobile, un sur web
3. 🌐 **Réseau** : Tester avec connexion lente/instable

---

## 🐛 **QUE TESTER ET SIGNALER**

### **✅ FONCTIONNALITÉS À VALIDER**

#### **Navigation et Interface**
- [ ] L'app se charge correctement
- [ ] Navigation entre les onglets fluide
- [ ] Boutons et liens fonctionnent
- [ ] Design responsive (mobile/web)
- [ ] Mode sombre/clair

#### **Gestion des Événements**
- [ ] Voir les détails de l'événement
- [ ] Liste des participants mise à jour
- [ ] Items organisés par catégorie
- [ ] Statuts des items corrects

#### **Gestion des Tâches**
- [ ] Voir ses tâches assignées
- [ ] Marquer comme terminé/non terminé
- [ ] Synchronisation temps réel
- [ ] Statistiques de progression

#### **Performance**
- [ ] Chargement rapide des pages
- [ ] Pas de lag lors des interactions
- [ ] Mise à jour instantanée des données

### **🚨 PROBLÈMES À SIGNALER**

#### **Bugs Critiques**
- ❌ App qui crash/se ferme
- ❌ Impossible de se connecter
- ❌ Données qui disparaissent
- ❌ Fonctionnalité principale cassée

#### **Bugs Mineurs**
- ⚠️ Texte mal affiché
- ⚠️ Bouton qui ne répond pas immédiatement
- ⚠️ Design qui déborde
- ⚠️ Couleurs/contrastes problématiques

#### **Suggestions d'Amélioration**
- 💡 Interface peu intuitive
- 💡 Fonctionnalité manquante
- 💡 Workflow compliqué
- 💡 Idées d'optimisation

---

## 📝 **COMMENT SIGNALER UN PROBLÈME**

### **Format de Rapport**
```
🐛 **TYPE** : Bug / Suggestion / Question

📱 **PLATEFORME** : Web / Mobile (iOS/Android)

🔍 **DESCRIPTION** : 
Décrivez le problème en détail

📋 **ÉTAPES POUR REPRODUIRE** :
1. Faire ceci
2. Puis cela
3. Résultat obtenu

✅ **RÉSULTAT ATTENDU** :
Ce qui devrait se passer

📸 **CAPTURE D'ÉCRAN** : 
(Si possible)
```

### **Canaux de Communication**
- 💬 **Chat de test** : [Lien fourni]
- 📧 **Email** : [Email fourni]
- 📱 **Téléphone** : [Numéro fourni]

---

## 🎉 **MERCI POUR VOTRE AIDE !**

Votre feedback est **essentiel** pour améliorer l'application avant le lancement public.

### **Récompenses pour les Testeurs** 🎁
- 🥇 **Meilleur testeur** : Accès premium gratuit
- 🏆 **Bug le plus critique trouvé** : Mention spéciale
- 🎊 **Tous les testeurs** : Remerciements dans l'app

---

## 📞 **SUPPORT TECHNIQUE**

**Problème technique urgent ?**
- 🆘 **Contact immédiat** : [Numéro/Chat fourni]
- ⏰ **Disponibilité** : 9h-18h pendant les tests
- 🔧 **Assistance** : Résolution en temps réel

**Bonne chance et amusez-vous bien ! 🚀**
