/**
 * Service de synchronisation intelligent pour Party Organizer
 * Gère la synchronisation bidirectionnelle et la résolution de conflits
 */

import { cache, CACHE_KEYS } from './cacheService';
import { networkService } from './networkService';
import { offlineQueue } from './offlineQueueService';
import { supabase } from './supabase';
import { showToast } from './toastService';

export interface SyncConfig {
  autoSync: boolean;
  syncInterval: number; // en millisecondes
  conflictResolution: 'client' | 'server' | 'manual';
  batchSize: number;
  retryAttempts: number;
}

export interface SyncStatus {
  isSync: boolean;
  lastSync?: Date;
  nextSync?: Date;
  pendingChanges: number;
  conflicts: number;
  errors: string[];
}

export interface ConflictItem {
  id: string;
  entity: string;
  localData: any;
  serverData: any;
  timestamp: Date;
  resolved: boolean;
}

class SyncService {
  private config: SyncConfig = {
    autoSync: true,
    syncInterval: 60000, // 1 minute
    conflictResolution: 'server', // Privilégier le serveur par défaut
    batchSize: 10,
    retryAttempts: 3,
  };

  private syncInterval?: NodeJS.Timeout;
  private isSyncing = false;
  private conflicts: ConflictItem[] = [];
  private lastSyncTime?: Date;

  constructor() {
    this.initialize();
  }

  /**
   * Initialise le service de synchronisation
   */
  private async initialize() {
    try {
      // Écouter les changements de réseau
      networkService.addListener({
        onConnected: () => this.startAutoSync(),
        onDisconnected: () => this.stopAutoSync(),
        onReconnected: () => this.performFullSync(),
        onSlowConnection: () => this.adjustSyncFrequency(true),
      });

      // Démarrer la synchronisation automatique si connecté
      if (networkService.isConnected() && this.config.autoSync) {
        this.startAutoSync();
      }

      console.log('SyncService initialized');
    } catch (error) {
      console.error('Error initializing SyncService:', error);
    }
  }

  /**
   * Démarre la synchronisation automatique
   */
  private startAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      if (networkService.isGoodConnection()) {
        this.performIncrementalSync();
      }
    }, this.config.syncInterval);

    console.log(`Auto-sync started (interval: ${this.config.syncInterval}ms)`);
  }

  /**
   * Arrête la synchronisation automatique
   */
  private stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = undefined;
    }
    console.log('Auto-sync stopped');
  }

  /**
   * Ajuste la fréquence de synchronisation selon la qualité réseau
   */
  private adjustSyncFrequency(isSlowConnection: boolean) {
    const baseInterval = 60000; // 1 minute
    this.config.syncInterval = isSlowConnection ? baseInterval * 3 : baseInterval;
    
    if (this.config.autoSync) {
      this.startAutoSync();
    }
  }

  /**
   * Effectue une synchronisation complète
   */
  async performFullSync(): Promise<void> {
    if (this.isSyncing || !networkService.isConnected()) {
      return;
    }

    this.isSyncing = true;
    console.log('Starting full sync...');

    try {
      // 1. Traiter la queue hors-ligne
      await offlineQueue.process();

      // 2. Synchroniser les données principales
      await this.syncEvents();
      await this.syncTemplates();
      await this.syncNotifications();

      // 3. Nettoyer le cache expiré
      await cache.cleanup();

      this.lastSyncTime = new Date();
      console.log('Full sync completed successfully');

      showToast('Synchronisation terminée', { type: 'success' });

    } catch (error) {
      console.error('Full sync failed:', error);
      showToast('Erreur de synchronisation', { type: 'error' });
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Effectue une synchronisation incrémentale
   */
  async performIncrementalSync(): Promise<void> {
    if (this.isSyncing || !networkService.isConnected()) {
      return;
    }

    this.isSyncing = true;

    try {
      // Traiter seulement les actions en attente
      await offlineQueue.process();

      // Synchroniser les données modifiées récemment
      if (this.lastSyncTime) {
        await this.syncRecentChanges(this.lastSyncTime);
      }

      this.lastSyncTime = new Date();

    } catch (error) {
      console.error('Incremental sync failed:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Synchronise les événements
   */
  private async syncEvents(): Promise<void> {
    try {
      // Récupérer les événements depuis le serveur
      const { data: serverEvents, error } = await supabase
        .from('events')
        .select(`
          *,
          participants(*),
          items(*)
        `)
        .order('updated_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Mettre à jour le cache
      if (serverEvents) {
        await cache.set(CACHE_KEYS.EVENTS, serverEvents);
        
        // Mettre en cache chaque événement individuellement
        for (const event of serverEvents) {
          await cache.set(CACHE_KEYS.EVENT_DETAILS, event, { id: event.id });
        }
      }

      console.log(`Synced ${serverEvents?.length || 0} events`);
    } catch (error) {
      console.error('Error syncing events:', error);
      throw error;
    }
  }

  /**
   * Synchronise les templates
   */
  private async syncTemplates(): Promise<void> {
    try {
      const { data: templates, error } = await supabase
        .from('event_templates')
        .select(`
          *,
          template_items(*)
        `)
        .or('category.eq.predefined,is_public.eq.true')
        .order('usage_count', { ascending: false });

      if (error) {
        throw error;
      }

      if (templates) {
        await cache.set(CACHE_KEYS.TEMPLATES, templates);
      }

      console.log(`Synced ${templates?.length || 0} templates`);
    } catch (error) {
      console.error('Error syncing templates:', error);
      throw error;
    }
  }

  /**
   * Synchronise les notifications
   */
  private async syncNotifications(): Promise<void> {
    try {
      // Récupérer les notifications récentes
      const { data: notifications, error } = await supabase
        .from('notification_history')
        .select('*')
        .order('sent_at', { ascending: false })
        .limit(50);

      if (error) {
        throw error;
      }

      if (notifications) {
        await cache.set(CACHE_KEYS.NOTIFICATIONS, notifications);
      }

      console.log(`Synced ${notifications?.length || 0} notifications`);
    } catch (error) {
      console.error('Error syncing notifications:', error);
      throw error;
    }
  }

  /**
   * Synchronise les changements récents
   */
  private async syncRecentChanges(since: Date): Promise<void> {
    try {
      const sinceIso = since.toISOString();

      // Récupérer les événements modifiés
      const { data: recentEvents, error: eventsError } = await supabase
        .from('events')
        .select('*')
        .gte('updated_at', sinceIso);

      if (eventsError) {
        throw eventsError;
      }

      // Mettre à jour le cache pour les événements modifiés
      if (recentEvents && recentEvents.length > 0) {
        for (const event of recentEvents) {
          await cache.set(CACHE_KEYS.EVENT_DETAILS, event, { id: event.id });
        }
        
        // Invalider le cache de la liste des événements
        await cache.invalidate(CACHE_KEYS.EVENTS);
      }

      console.log(`Synced ${recentEvents?.length || 0} recent changes`);
    } catch (error) {
      console.error('Error syncing recent changes:', error);
      throw error;
    }
  }

  /**
   * Détecte et résout les conflits
   */
  private async detectConflicts(localData: any, serverData: any): Promise<ConflictItem | null> {
    // Comparer les timestamps de modification
    const localTimestamp = new Date(localData.updated_at);
    const serverTimestamp = new Date(serverData.updated_at);

    if (localTimestamp.getTime() !== serverTimestamp.getTime()) {
      return {
        id: localData.id,
        entity: 'event', // À adapter selon le type
        localData,
        serverData,
        timestamp: new Date(),
        resolved: false,
      };
    }

    return null;
  }

  /**
   * Résout un conflit selon la stratégie configurée
   */
  private async resolveConflict(conflict: ConflictItem): Promise<any> {
    switch (this.config.conflictResolution) {
      case 'client':
        return conflict.localData;
      case 'server':
        return conflict.serverData;
      case 'manual':
        // Ajouter à la liste des conflits pour résolution manuelle
        this.conflicts.push(conflict);
        return conflict.serverData; // Temporairement utiliser le serveur
      default:
        return conflict.serverData;
    }
  }

  /**
   * Obtient le statut de synchronisation
   */
  getStatus(): SyncStatus {
    const queueStats = offlineQueue.getStats();
    
    return {
      isSync: this.isSyncing,
      lastSync: this.lastSyncTime,
      nextSync: this.syncInterval ? new Date(Date.now() + this.config.syncInterval) : undefined,
      pendingChanges: queueStats.pendingActions,
      conflicts: this.conflicts.filter(c => !c.resolved).length,
      errors: [], // À implémenter avec un système de logs
    };
  }

  /**
   * Configure le service de synchronisation
   */
  configure(newConfig: Partial<SyncConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Redémarrer la synchronisation automatique si nécessaire
    if (this.config.autoSync && networkService.isConnected()) {
      this.startAutoSync();
    } else if (!this.config.autoSync) {
      this.stopAutoSync();
    }
  }

  /**
   * Force une synchronisation manuelle
   */
  async forceSync(): Promise<void> {
    await this.performFullSync();
  }

  /**
   * Obtient les conflits non résolus
   */
  getConflicts(): ConflictItem[] {
    return this.conflicts.filter(c => !c.resolved);
  }

  /**
   * Résout manuellement un conflit
   */
  async resolveConflictManually(conflictId: string, resolution: 'client' | 'server'): Promise<void> {
    const conflict = this.conflicts.find(c => c.id === conflictId);
    if (!conflict) {
      throw new Error('Conflict not found');
    }

    const resolvedData = resolution === 'client' ? conflict.localData : conflict.serverData;
    
    // Appliquer la résolution (à implémenter selon l'entité)
    // ...

    conflict.resolved = true;
    console.log(`Conflict resolved: ${conflictId} (${resolution})`);
  }
}

// Instance singleton
export const syncService = new SyncService();

// Fonctions utilitaires
export const sync = {
  full: () => syncService.performFullSync(),
  incremental: () => syncService.performIncrementalSync(),
  force: () => syncService.forceSync(),
  getStatus: () => syncService.getStatus(),
  configure: (config: Partial<SyncConfig>) => syncService.configure(config),
  getConflicts: () => syncService.getConflicts(),
  resolveConflict: (id: string, resolution: 'client' | 'server') => 
    syncService.resolveConflictManually(id, resolution),
};
