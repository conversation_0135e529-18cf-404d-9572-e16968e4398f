import { 
  fetchEventDetails, 
  fetchItemsForEvent, 
  fetchParticipantsForEvent,
  fetchEventsForUser,
  fetchContacts,
  createEvent,
  updateEvent,
  deleteEvent,
  createItem,
  updateItem,
  deleteItem,
} from './supabaseCrud';
import { 
  EventCache, 
  ItemCache, 
  ParticipantCache, 
  ContactCache,
  invalidateCache 
} from './cache';
import { Event, Item, Participant, Contact, EventInsert, EventUpdate, ItemInsert, ItemUpdate } from './types';

/**
 * Requêtes optimisées avec cache pour les événements
 */
export const OptimizedEventQueries = {
  /**
   * Récupère un événement avec cache
   */
  async getEvent(eventId: number): Promise<Event | null> {
    // Vérifier le cache d'abord
    const cached = await EventCache.get(eventId);
    if (cached) {
      return cached;
    }

    // Récupérer depuis la base de données
    const event = await fetchEventDetails(eventId);
    if (event) {
      await EventCache.set(eventId, event);
    }

    return event;
  },

  /**
   * Récupère les événements d'un utilisateur avec cache
   */
  async getUserEvents(userId: string): Promise<Event[]> {
    const events = await fetchEventsForUser(userId);
    
    // Mettre en cache chaque événement individuellement
    if (events) {
      for (const event of events) {
        await EventCache.set(event.id, event);
      }
    }

    return events || [];
  },

  /**
   * Crée un événement et invalide le cache
   */
  async createEvent(eventData: EventInsert): Promise<Event | null> {
    const event = await createEvent(eventData);
    
    if (event) {
      await EventCache.set(event.id, event);
      // Invalider le cache des événements utilisateur pourrait être fait ici
      // mais on laisse le cache naturellement expirer pour éviter les requêtes inutiles
    }

    return event;
  },

  /**
   * Met à jour un événement et invalide le cache
   */
  async updateEvent(eventId: number, updates: EventUpdate): Promise<Event | null> {
    const event = await updateEvent(eventId, updates);
    
    if (event) {
      await EventCache.set(eventId, event);
    } else {
      // En cas d'échec, supprimer du cache pour forcer un refresh
      await EventCache.remove(eventId);
    }

    return event;
  },

  /**
   * Supprime un événement et invalide le cache
   */
  async deleteEvent(eventId: number): Promise<boolean> {
    const success = await deleteEvent(eventId);
    
    if (success) {
      invalidateCache.event(eventId);
    }

    return success;
  },

  /**
   * Précharge les données d'un événement (événement + items + participants)
   */
  async preloadEventData(eventId: number): Promise<{
    event: Event | null;
    items: Item[];
    participants: Participant[];
  }> {
    // Lancer toutes les requêtes en parallèle
    const [event, items, participants] = await Promise.all([
      this.getEvent(eventId),
      OptimizedItemQueries.getEventItems(eventId),
      OptimizedParticipantQueries.getEventParticipants(eventId),
    ]);

    return {
      event,
      items,
      participants,
    };
  },
};

/**
 * Requêtes optimisées avec cache pour les items
 */
export const OptimizedItemQueries = {
  /**
   * Récupère les items d'un événement avec cache
   */
  async getEventItems(eventId: number): Promise<Item[]> {
    // Vérifier le cache d'abord
    const cached = await ItemCache.get(eventId);
    if (cached) {
      return cached;
    }

    // Récupérer depuis la base de données
    const items = await fetchItemsForEvent(eventId);
    if (items) {
      await ItemCache.set(eventId, items);
    }

    return items || [];
  },

  /**
   * Crée un item et invalide le cache
   */
  async createItem(itemData: ItemInsert): Promise<Item | null> {
    const item = await createItem(itemData);
    
    if (item) {
      // Invalider le cache des items de l'événement
      await ItemCache.remove(item.event_id);
    }

    return item;
  },

  /**
   * Met à jour un item et invalide le cache
   */
  async updateItem(itemId: number, eventId: number, updates: ItemUpdate): Promise<Item | null> {
    const item = await updateItem(itemId, updates);
    
    if (item) {
      // Invalider le cache des items de l'événement
      await ItemCache.remove(eventId);
    }

    return item;
  },

  /**
   * Supprime un item et invalide le cache
   */
  async deleteItem(itemId: number, eventId: number): Promise<boolean> {
    const success = await deleteItem(itemId);
    
    if (success) {
      // Invalider le cache des items de l'événement
      await ItemCache.remove(eventId);
    }

    return success;
  },

  /**
   * Met à jour plusieurs items en lot (optimisation)
   */
  async batchUpdateItems(eventId: number, updates: Array<{ id: number; updates: ItemUpdate }>): Promise<boolean> {
    try {
      // Exécuter toutes les mises à jour en parallèle
      const promises = updates.map(({ id, updates: itemUpdates }) => 
        updateItem(id, itemUpdates)
      );
      
      await Promise.all(promises);
      
      // Invalider le cache une seule fois
      await ItemCache.remove(eventId);
      
      return true;
    } catch (error) {
      console.error('Batch update items error:', error);
      return false;
    }
  },
};

/**
 * Requêtes optimisées avec cache pour les participants
 */
export const OptimizedParticipantQueries = {
  /**
   * Récupère les participants d'un événement avec cache
   */
  async getEventParticipants(eventId: number): Promise<Participant[]> {
    // Vérifier le cache d'abord
    const cached = await ParticipantCache.get(eventId);
    if (cached) {
      return cached;
    }

    // Récupérer depuis la base de données
    const participants = await fetchParticipantsForEvent(eventId);
    if (participants) {
      await ParticipantCache.set(eventId, participants);
    }

    return participants || [];
  },

  /**
   * Invalide le cache des participants après modification
   */
  async invalidateEventParticipants(eventId: number): Promise<void> {
    await ParticipantCache.remove(eventId);
  },
};

/**
 * Requêtes optimisées avec cache pour les contacts
 */
export const OptimizedContactQueries = {
  /**
   * Récupère les contacts d'un utilisateur avec cache
   */
  async getUserContacts(userId: string): Promise<Contact[]> {
    // Vérifier le cache d'abord
    const cached = await ContactCache.get(userId);
    if (cached) {
      return cached;
    }

    // Récupérer depuis la base de données
    const contacts = await fetchContacts(userId);
    if (contacts) {
      await ContactCache.set(userId, contacts);
    }

    return contacts || [];
  },

  /**
   * Invalide le cache des contacts après modification
   */
  async invalidateUserContacts(userId: string): Promise<void> {
    await ContactCache.remove(userId);
  },
};

/**
 * Requêtes en lot pour optimiser les performances
 */
export const BatchQueries = {
  /**
   * Précharge toutes les données utilisateur importantes
   */
  async preloadUserData(userId: string): Promise<{
    events: Event[];
    contacts: Contact[];
  }> {
    const [events, contacts] = await Promise.all([
      OptimizedEventQueries.getUserEvents(userId),
      OptimizedContactQueries.getUserContacts(userId),
    ]);

    return { events, contacts };
  },

  /**
   * Précharge les données de plusieurs événements
   */
  async preloadMultipleEvents(eventIds: number[]): Promise<Map<number, {
    event: Event | null;
    items: Item[];
    participants: Participant[];
  }>> {
    const results = new Map();
    
    // Lancer toutes les requêtes en parallèle
    const promises = eventIds.map(async (eventId) => {
      const data = await OptimizedEventQueries.preloadEventData(eventId);
      results.set(eventId, data);
    });

    await Promise.all(promises);
    
    return results;
  },
};

/**
 * Utilitaires pour la gestion du cache
 */
export const CacheUtils = {
  /**
   * Réchauffe le cache avec les données les plus importantes
   */
  async warmupCache(userId: string): Promise<void> {
    try {
      // Précharger les données utilisateur
      await BatchQueries.preloadUserData(userId);
      
      console.log('Cache warmed up successfully');
    } catch (error) {
      console.error('Cache warmup error:', error);
    }
  },

  /**
   * Nettoie le cache lors de la déconnexion
   */
  async clearUserCache(userId: string): Promise<void> {
    invalidateCache.user(userId);
    invalidateCache.allEvents();
  },
};
