/**
 * Service de gestion réseau pour Party Organizer
 * Détection de connectivité et gestion hors-ligne
 */

import NetInfo, {
  NetInfoState,
  NetInfoStateType,
} from "@react-native-community/netinfo";
import { Platform } from "react-native";
import React from "react";

export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: NetInfoStateType;
  isWifiEnabled: boolean;
  strength: number; // 0-100
  lastConnected?: Date;
  lastDisconnected?: Date;
}

export interface NetworkListener {
  onConnected: () => void;
  onDisconnected: () => void;
  onReconnected: () => void;
  onSlowConnection: () => void;
}

class NetworkService {
  private currentState: NetworkState = {
    isConnected: false,
    isInternetReachable: false,
    type: NetInfoStateType.unknown,
    isWifiEnabled: false,
    strength: 0,
  };

  private listeners: NetworkListener[] = [];
  private wasConnected = false;
  private connectionCheckInterval?: NodeJS.Timeout;
  private slowConnectionThreshold = 2000; // 2 secondes

  constructor() {
    this.initialize();
  }

  /**
   * Initialise le service réseau
   */
  private async initialize() {
    try {
      // Obtenir l'état initial
      const state = await NetInfo.fetch();
      this.updateState(state);

      // Écouter les changements
      NetInfo.addEventListener(this.handleStateChange.bind(this));

      // Vérification périodique de la qualité de connexion
      this.startConnectionQualityCheck();

      console.log("NetworkService initialized");
    } catch (error) {
      console.error("Error initializing NetworkService:", error);
    }
  }

  /**
   * Met à jour l'état réseau
   */
  private updateState(netInfoState: NetInfoState) {
    const wasConnectedBefore = this.currentState.isConnected;

    this.currentState = {
      isConnected: netInfoState.isConnected ?? false,
      isInternetReachable: netInfoState.isInternetReachable ?? false,
      type: netInfoState.type,
      isWifiEnabled: netInfoState.type === NetInfoStateType.wifi,
      strength: this.calculateSignalStrength(netInfoState),
      lastConnected: netInfoState.isConnected
        ? new Date()
        : this.currentState.lastConnected,
      lastDisconnected: !netInfoState.isConnected
        ? new Date()
        : this.currentState.lastDisconnected,
    };

    // Notifier les changements
    if (!wasConnectedBefore && this.currentState.isConnected) {
      this.notifyConnected();
    } else if (wasConnectedBefore && !this.currentState.isConnected) {
      this.notifyDisconnected();
    } else if (
      this.wasConnected &&
      this.currentState.isConnected &&
      !wasConnectedBefore
    ) {
      this.notifyReconnected();
    }

    this.wasConnected = this.currentState.isConnected;
  }

  /**
   * Calcule la force du signal (approximation)
   */
  private calculateSignalStrength(state: NetInfoState): number {
    if (!state.isConnected) return 0;

    // Pour WiFi, utiliser les détails si disponibles
    if (state.type === NetInfoStateType.wifi && state.details) {
      const wifiDetails = state.details as any;
      if (wifiDetails.strength !== undefined) {
        return Math.max(0, Math.min(100, wifiDetails.strength));
      }
      if (wifiDetails.rssi !== undefined) {
        // Convertir RSSI en pourcentage (approximation)
        return Math.max(0, Math.min(100, (wifiDetails.rssi + 100) * 2));
      }
    }

    // Pour mobile, utiliser les détails si disponibles
    if (state.type === NetInfoStateType.cellular && state.details) {
      const cellularDetails = state.details as any;
      if (cellularDetails.cellularGeneration) {
        switch (cellularDetails.cellularGeneration) {
          case "5g":
            return 90;
          case "4g":
            return 75;
          case "3g":
            return 50;
          case "2g":
            return 25;
          default:
            return 60;
        }
      }
    }

    // Valeur par défaut si connecté
    return state.isConnected ? 70 : 0;
  }

  /**
   * Gère les changements d'état réseau
   */
  private handleStateChange(state: NetInfoState) {
    console.log("Network state changed:", {
      isConnected: state.isConnected,
      type: state.type,
      isInternetReachable: state.isInternetReachable,
    });

    this.updateState(state);
  }

  /**
   * Démarre la vérification de qualité de connexion
   */
  private startConnectionQualityCheck() {
    if (this.connectionCheckInterval) {
      clearInterval(this.connectionCheckInterval);
    }

    this.connectionCheckInterval = setInterval(async () => {
      if (this.currentState.isConnected) {
        const isSlowConnection = await this.checkConnectionSpeed();
        if (isSlowConnection) {
          this.notifySlowConnection();
        }
      }
    }, 30000); // Vérifier toutes les 30 secondes
  }

  /**
   * Vérifie la vitesse de connexion
   */
  private async checkConnectionSpeed(): Promise<boolean> {
    try {
      const startTime = Date.now();

      // Faire une requête simple pour tester la vitesse
      const response = await fetch("https://httpbin.org/json", {
        method: "GET",
        cache: "no-cache",
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`Connection speed test: ${duration}ms`);

      return duration > this.slowConnectionThreshold;
    } catch (error) {
      console.log("Connection speed test failed:", error);
      return true; // Considérer comme lent si le test échoue
    }
  }

  /**
   * Notifie les listeners de connexion
   */
  private notifyConnected() {
    console.log("Network connected");
    this.listeners.forEach((listener) => {
      try {
        listener.onConnected();
      } catch (error) {
        console.error("Error in network listener:", error);
      }
    });
  }

  /**
   * Notifie les listeners de déconnexion
   */
  private notifyDisconnected() {
    console.log("Network disconnected");
    this.listeners.forEach((listener) => {
      try {
        listener.onDisconnected();
      } catch (error) {
        console.error("Error in network listener:", error);
      }
    });
  }

  /**
   * Notifie les listeners de reconnexion
   */
  private notifyReconnected() {
    console.log("Network reconnected");
    this.listeners.forEach((listener) => {
      try {
        listener.onReconnected();
      } catch (error) {
        console.error("Error in network listener:", error);
      }
    });
  }

  /**
   * Notifie les listeners de connexion lente
   */
  private notifySlowConnection() {
    console.log("Slow connection detected");
    this.listeners.forEach((listener) => {
      try {
        listener.onSlowConnection();
      } catch (error) {
        console.error("Error in network listener:", error);
      }
    });
  }

  /**
   * Ajoute un listener réseau
   */
  addListener(listener: NetworkListener): () => void {
    this.listeners.push(listener);

    // Retourner une fonction de nettoyage
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Obtient l'état réseau actuel
   */
  getState(): NetworkState {
    return { ...this.currentState };
  }

  /**
   * Vérifie si connecté
   */
  isConnected(): boolean {
    return this.currentState.isConnected;
  }

  /**
   * Vérifie si Internet est accessible
   */
  isInternetReachable(): boolean {
    return this.currentState.isInternetReachable;
  }

  /**
   * Vérifie si la connexion est de bonne qualité
   */
  isGoodConnection(): boolean {
    return (
      this.currentState.isConnected &&
      this.currentState.isInternetReachable &&
      this.currentState.strength > 30
    );
  }

  /**
   * Force une vérification de l'état réseau
   */
  async refresh(): Promise<NetworkState> {
    try {
      const state = await NetInfo.fetch();
      this.updateState(state);
      return this.getState();
    } catch (error) {
      console.error("Error refreshing network state:", error);
      return this.getState();
    }
  }

  /**
   * Nettoie le service
   */
  cleanup() {
    if (this.connectionCheckInterval) {
      clearInterval(this.connectionCheckInterval);
    }
    this.listeners = [];
  }
}

// Instance singleton
export const networkService = new NetworkService();

// Hook React pour utiliser l'état réseau
export function useNetworkState() {
  const [networkState, setNetworkState] = React.useState<NetworkState>(
    networkService.getState()
  );

  React.useEffect(() => {
    const unsubscribe = networkService.addListener({
      onConnected: () => setNetworkState(networkService.getState()),
      onDisconnected: () => setNetworkState(networkService.getState()),
      onReconnected: () => setNetworkState(networkService.getState()),
      onSlowConnection: () => setNetworkState(networkService.getState()),
    });

    return unsubscribe;
  }, []);

  return networkState;
}

// Fonctions utilitaires
export const network = {
  getState: () => networkService.getState(),
  isConnected: () => networkService.isConnected(),
  isInternetReachable: () => networkService.isInternetReachable(),
  isGoodConnection: () => networkService.isGoodConnection(),
  refresh: () => networkService.refresh(),
  addListener: (listener: NetworkListener) =>
    networkService.addListener(listener),
};
