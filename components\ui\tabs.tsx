import React, { createContext, useContext, useState } from "react";
import { View, Pressable, Platform } from "react-native";
import { Text } from "./text";
import { cn } from "~/lib/utils";

interface TabsContextType {
  value: string;
  onValueChange: (value: string) => void;
}

const TabsContext = createContext<TabsContextType | undefined>(undefined);

function useTabsContext() {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error("Tabs components must be used within a Tabs provider");
  }
  return context;
}

interface TabsProps {
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}

export function Tabs({ value, onValueChange, children, className }: TabsProps) {
  return (
    <TabsContext.Provider value={{ value, onValueChange }}>
      <View className={cn("", className)}>
        {children}
      </View>
    </TabsContext.Provider>
  );
}

interface TabsListProps {
  children: React.ReactNode;
  className?: string;
}

export function TabsList({ children, className }: TabsListProps) {
  return (
    <View className={cn("flex-row bg-muted rounded-lg p-1", className)}>
      {children}
    </View>
  );
}

interface TabsTriggerProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

export function TabsTrigger({ value, children, className }: TabsTriggerProps) {
  const { value: selectedValue, onValueChange } = useTabsContext();
  const isSelected = selectedValue === value;

  return (
    <Pressable
      onPress={() => onValueChange(value)}
      className={cn(
        "flex-1 px-3 py-2 rounded-md items-center justify-center",
        isSelected 
          ? "bg-background shadow-sm" 
          : "bg-transparent",
        className
      )}
      {...(Platform.OS === "web" ? { style: { cursor: "pointer" } } : {})}
    >
      <Text className={cn(
        "text-sm font-medium",
        isSelected 
          ? "text-foreground" 
          : "text-muted-foreground"
      )}>
        {children}
      </Text>
    </Pressable>
  );
}

interface TabsContentProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

export function TabsContent({ value, children, className }: TabsContentProps) {
  const { value: selectedValue } = useTabsContext();
  
  if (selectedValue !== value) {
    return null;
  }

  return (
    <View className={cn("mt-4", className)}>
      {children}
    </View>
  );
}
