/**
 * Script pour appliquer les migrations financières à la base de données Supabase
 * Exécute les modifications SQL nécessaires pour la gestion financière
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Configuration Supabase
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  console.error('Vérifiez EXPO_PUBLIC_SUPABASE_URL et SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyFinancialMigration() {
  console.log('🚀 Début de la migration financière...');

  try {
    // 1. Ajouter les colonnes financières à la table items
    console.log('📝 Ajout des colonnes financières à la table items...');
    
    const { error: alterItemsError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE items 
        ADD COLUMN IF NOT EXISTS actual_cost DECIMAL(10,2),
        ADD COLUMN IF NOT EXISTS paid_by_participant_id BIGINT REFERENCES participants(id) ON DELETE SET NULL;
      `
    });

    if (alterItemsError) {
      console.error('❌ Erreur lors de la modification de la table items:', alterItemsError);
    } else {
      console.log('✅ Colonnes ajoutées à la table items');
    }

    // 2. Créer la table des transactions financières
    console.log('📝 Création de la table financial_transactions...');
    
    const { error: createTransactionsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS financial_transactions (
          id BIGSERIAL PRIMARY KEY,
          event_id BIGINT NOT NULL REFERENCES events(id) ON DELETE CASCADE,
          from_participant_id BIGINT NOT NULL REFERENCES participants(id) ON DELETE CASCADE,
          to_participant_id BIGINT NOT NULL REFERENCES participants(id) ON DELETE CASCADE,
          amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
          description TEXT,
          is_settled BOOLEAN NOT NULL DEFAULT FALSE,
          settled_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );
      `
    });

    if (createTransactionsError) {
      console.error('❌ Erreur lors de la création de financial_transactions:', createTransactionsError);
    } else {
      console.log('✅ Table financial_transactions créée');
    }

    // 3. Créer la table des résumés financiers
    console.log('📝 Création de la table financial_summaries...');
    
    const { error: createSummariesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS financial_summaries (
          id BIGSERIAL PRIMARY KEY,
          event_id BIGINT NOT NULL REFERENCES events(id) ON DELETE CASCADE,
          participant_id BIGINT NOT NULL REFERENCES participants(id) ON DELETE CASCADE,
          total_spent DECIMAL(10,2) NOT NULL DEFAULT 0,
          total_owed DECIMAL(10,2) NOT NULL DEFAULT 0,
          net_balance DECIMAL(10,2) NOT NULL DEFAULT 0,
          last_calculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          UNIQUE(event_id, participant_id)
        );
      `
    });

    if (createSummariesError) {
      console.error('❌ Erreur lors de la création de financial_summaries:', createSummariesError);
    } else {
      console.log('✅ Table financial_summaries créée');
    }

    // 4. Créer les index pour les performances
    console.log('📝 Création des index...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_financial_transactions_event_id ON financial_transactions(event_id);',
      'CREATE INDEX IF NOT EXISTS idx_financial_transactions_from_participant ON financial_transactions(from_participant_id);',
      'CREATE INDEX IF NOT EXISTS idx_financial_transactions_to_participant ON financial_transactions(to_participant_id);',
      'CREATE INDEX IF NOT EXISTS idx_financial_summaries_event_id ON financial_summaries(event_id);',
      'CREATE INDEX IF NOT EXISTS idx_items_paid_by ON items(paid_by_participant_id);'
    ];

    for (const indexSql of indexes) {
      const { error } = await supabase.rpc('exec_sql', { sql: indexSql });
      if (error) {
        console.warn('⚠️ Erreur lors de la création d\'un index:', error);
      }
    }
    
    console.log('✅ Index créés');

    // 5. Activer RLS sur les nouvelles tables
    console.log('📝 Activation de RLS...');
    
    const { error: rlsError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE financial_transactions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE financial_summaries ENABLE ROW LEVEL SECURITY;
      `
    });

    if (rlsError) {
      console.warn('⚠️ Erreur lors de l\'activation de RLS:', rlsError);
    } else {
      console.log('✅ RLS activé');
    }

    console.log('🎉 Migration financière terminée avec succès !');
    console.log('');
    console.log('📋 Résumé des modifications :');
    console.log('  • Colonnes actual_cost et paid_by_participant_id ajoutées à items');
    console.log('  • Table financial_transactions créée');
    console.log('  • Table financial_summaries créée');
    console.log('  • Index de performance créés');
    console.log('  • RLS activé sur les nouvelles tables');
    console.log('');
    console.log('🚀 Votre app peut maintenant gérer les finances automatiquement !');

  } catch (error) {
    console.error('❌ Erreur lors de la migration:', error);
    process.exit(1);
  }
}

// Exécuter la migration
applyFinancialMigration();
