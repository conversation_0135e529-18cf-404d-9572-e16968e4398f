{"name": "triparty", "main": "index.js", "version": "1.0.0", "react-native": {"ws": false}, "scripts": {"dev": "expo start -c", "dev:web": "cross-env EXPO_TARGET=web expo start -c --web", "dev:android": "expo start -c --android", "android": "expo start -c --android", "ios": "expo start -c --ios", "web": "cross-env EXPO_TARGET=web expo start -c --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css", "migrate-env": "node scripts/migrate-env-security.js", "security-check": "node scripts/migrate-env-security.js", "migrate-buttons": "node scripts/migrate-button-components.js", "init-avatars": "npx ts-node scripts/initializeAvatarBucket.ts", "setup-avatar-policies": "npx ts-node scripts/setupAvatarPolicies.ts", "debug-avatar-policies": "npx ts-node scripts/debugAvatarPolicies.ts", "fix-avatar-rls": "npx ts-node scripts/fixAvatarRLS.ts", "disable-rls-temp": "npx ts-node scripts/disableRLSTemporarily.ts", "create-public-bucket": "npx ts-node scripts/createPublicAvatarBucket.ts", "test-avatars": "npx ts-node scripts/testAvatarSystem.ts"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@radix-ui/react-slot": "^1.2.0", "@react-google-maps/api": "^2.20.6", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/material-top-tabs": "^7.2.3", "@react-navigation/native": "^7.0.0", "@rn-primitives/accordion": "^1.1.0", "@rn-primitives/alert-dialog": "^1.1.0", "@rn-primitives/aspect-ratio": "^1.1.0", "@rn-primitives/avatar": "~1.1.0", "@rn-primitives/checkbox": "^1.1.0", "@rn-primitives/collapsible": "^1.1.0", "@rn-primitives/context-menu": "^1.1.0", "@rn-primitives/dialog": "^1.1.0", "@rn-primitives/dropdown-menu": "^1.1.0", "@rn-primitives/hover-card": "^1.1.0", "@rn-primitives/label": "^1.1.0", "@rn-primitives/menubar": "^1.1.0", "@rn-primitives/navigation-menu": "^1.1.0", "@rn-primitives/popover": "^1.1.0", "@rn-primitives/portal": "~1.1.0", "@rn-primitives/progress": "~1.1.0", "@rn-primitives/radio-group": "^1.1.0", "@rn-primitives/select": "^1.1.0", "@rn-primitives/separator": "^1.1.0", "@rn-primitives/slot": "~1.1.0", "@rn-primitives/switch": "^1.1.0", "@rn-primitives/table": "^1.1.0", "@rn-primitives/tabs": "^1.1.0", "@rn-primitives/toggle": "^1.1.0", "@rn-primitives/toggle-group": "^1.1.0", "@rn-primitives/tooltip": "~1.1.0", "@rn-primitives/types": "~1.1.0", "@supabase/supabase-js": "^2.49.5-next.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.0.0", "events": "^3.3.0", "expo": "^53.0.9", "expo-barcode-scanner": "^13.0.1", "expo-camera": "^16.1.6", "expo-constants": "^17.1.6", "expo-device": "^7.1.4", "expo-file-system": "^18.1.10", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.4", "expo-location": "^18.1.5", "expo-navigation-bar": "~4.2.4", "expo-notifications": "^0.31.2", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-sqlite": "^15.2.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "lucide-react": "^0.507.0", "lucide-react-native": "^0.378.0", "nativewind": "^4.1.23", "node-fetch": "^3.3.2", "qrcode": "^1.5.4", "react": "19.0.0", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-pager-view": "6.7.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-tab-view": "^4.0.6", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-qr-code": "^2.0.15", "readable-stream": "^4.7.0", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "text-encoding": "^0.7.0", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.26.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@types/react-dom": "^19.0.0", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "jest": "^29.7.0", "jest-expo": "^53.0.5", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"], "collectCoverageFrom": ["**/*.{ts,tsx}", "!**/*.d.ts", "!**/coverage/**", "!**/node_modules/**", "!**/babel.config.js", "!**/expo-env.d.ts", "!**/.expo/**"]}, "private": true}