-- Activer Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE items ENABLE ROW LEVEL SECURITY;
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_group_members ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE messages ENABLE ROW LEVEL SECURITY; -- Messages table does not exist yet

-- Politiques pour profiles
CREATE POLICY "Les utilisateurs peuvent voir leur propre profil"
ON profiles FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Les utilisateurs peuvent mettre à jour leur propre profil"
ON profiles FOR UPDATE
USING (auth.uid() = id);

-- Politiques pour events
CREATE POLICY "Les utilisateurs peuvent voir les événements qu'ils ont créés"
ON events FOR SELECT
USING (organizer_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent créer des événements"
ON events FOR INSERT
WITH CHECK (organizer_id = auth.uid());

CREATE POLICY "Les organisateurs peuvent mettre à jour leurs événements"
ON events FOR UPDATE
USING (organizer_id = auth.uid());

CREATE POLICY "Les organisateurs peuvent supprimer leurs événements"
ON events FOR DELETE
USING (organizer_id = auth.uid());

-- Politiques pour participants
CREATE POLICY "Les utilisateurs peuvent s'ajouter comme participant"
ON participants FOR INSERT
WITH CHECK (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1
        FROM events
        WHERE id = participants.event_id AND organizer_id = auth.uid()
    )
);

CREATE POLICY "Les utilisateurs peuvent mettre à jour leur propre participation"
ON participants FOR UPDATE
USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1
        FROM events
        WHERE id = participants.event_id AND organizer_id = auth.uid()
    )
);

CREATE POLICY "Les utilisateurs peuvent supprimer leur propre participation"
ON participants FOR DELETE
USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1
        FROM events
        WHERE id = participants.event_id AND organizer_id = auth.uid()
    )
);

-- Nouvelle politique SELECT pour participants (non-récursive)
CREATE POLICY "Users can view participants of their events"
ON public.participants
FOR SELECT
USING (
    (EXISTS ( -- L'utilisateur est l'organisateur de l'événement
        SELECT 1
        FROM public.events e
        WHERE e.id = participants.event_id AND e.organizer_id = auth.uid()
    ))
    OR
    (public.is_participant(participants.event_id, auth.uid())) -- L'utilisateur est un participant de l'événement (utilise la fonction corrigée)
);

-- Politiques pour items
CREATE POLICY "Les utilisateurs peuvent voir les items des événements auxquels ils participent"
ON items FOR SELECT
USING (
    EXISTS (
        SELECT 1
        FROM events
        WHERE id = items.event_id AND (
            organizer_id = auth.uid() OR
            EXISTS (
                SELECT 1
                FROM participants
                WHERE event_id = events.id AND user_id = auth.uid()
            )
        )
    )
);

CREATE POLICY "Les utilisateurs peuvent créer des items pour les événements auxquels ils participent"
ON items FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1
        FROM events
        WHERE id = items.event_id AND (
            organizer_id = auth.uid() OR
            (allow_suggestions = TRUE AND EXISTS (
                SELECT 1
                FROM participants
                WHERE event_id = events.id AND user_id = auth.uid()
            ))
        )
    )
);

CREATE POLICY "Les utilisateurs peuvent mettre à jour les items qu'ils ont créés ou les items des événements qu'ils organisent"
ON items FOR UPDATE
USING (
    suggester_id = auth.uid() OR
    EXISTS (
        SELECT 1
        FROM events
        WHERE id = items.event_id AND organizer_id = auth.uid()
    )
);

CREATE POLICY "Les utilisateurs peuvent supprimer les items qu'ils ont créés ou les items des événements qu'ils organisent"
ON items FOR DELETE
USING (
    suggester_id = auth.uid() OR
    EXISTS (
        SELECT 1
        FROM events
        WHERE id = items.event_id AND organizer_id = auth.uid()
    )
);

-- Politiques pour contacts
CREATE POLICY "Les utilisateurs peuvent voir leurs propres contacts"
ON contacts FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent créer leurs propres contacts"
ON contacts FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent mettre à jour leurs propres contacts"
ON contacts FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres contacts"
ON contacts FOR DELETE
USING (user_id = auth.uid());

-- Politiques pour contact_groups
CREATE POLICY "Les utilisateurs peuvent voir leurs propres groupes de contacts"
ON contact_groups FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent créer leurs propres groupes de contacts"
ON contact_groups FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent mettre à jour leurs propres groupes de contacts"
ON contact_groups FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Les utilisateurs peuvent supprimer leurs propres groupes de contacts"
ON contact_groups FOR DELETE
USING (user_id = auth.uid());

-- Politiques pour contact_group_members
CREATE POLICY "Les utilisateurs peuvent voir les membres de leurs propres groupes de contacts"
ON contact_group_members FOR SELECT
USING (
    EXISTS (
        SELECT 1
        FROM contact_groups
        WHERE id = contact_group_members.group_id AND user_id = auth.uid()
    )
);

CREATE POLICY "Les utilisateurs peuvent ajouter des membres à leurs propres groupes de contacts"
ON contact_group_members FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1
        FROM contact_groups
        WHERE id = contact_group_members.group_id AND user_id = auth.uid()
    ) AND
    EXISTS (
        SELECT 1
        FROM contacts
        WHERE id = contact_group_members.contact_id AND user_id = auth.uid()
    )
);

CREATE POLICY "Les utilisateurs peuvent supprimer des membres de leurs propres groupes de contacts"
ON contact_group_members FOR DELETE
USING (
    EXISTS (
        SELECT 1
        FROM contact_groups
        WHERE id = contact_group_members.group_id AND user_id = auth.uid()
    )
);

-- Politiques pour messages -- Commented out as messages table does not exist yet
-- CREATE POLICY "Les utilisateurs peuvent voir les messages des événements auxquels ils participent"
-- ON messages FOR SELECT
-- USING (
--     EXISTS (
--         SELECT 1
--         FROM events
--         WHERE id = messages.event_id AND (
--             organizer_id = auth.uid() OR
--             EXISTS (
--                 SELECT 1
--                 FROM participants
--                 WHERE event_id = events.id AND user_id = auth.uid()
--             )
--         )
--     )
-- );

-- CREATE POLICY "Les utilisateurs peuvent créer des messages pour les événements auxquels ils participent"
-- ON messages FOR INSERT
-- WITH CHECK (
--     sender_id = auth.uid() AND
--     EXISTS (
--         SELECT 1
--         FROM events
--         WHERE id = messages.event_id AND (
--             organizer_id = auth.uid() OR
--             EXISTS (
--                 SELECT 1
--                 FROM participants
--                 WHERE event_id = events.id AND user_id = auth.uid()
--             )
--         )
--     )
-- );

-- Accorder les permissions nécessaires
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
