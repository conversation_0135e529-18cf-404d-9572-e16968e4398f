/**
 * Moteur de calcul financier pour Party Organizer
 * Gère les calculs de répartition des coûts et les suggestions de remboursement
 */

import { 
  FinancialSummary, 
  SettlementSuggestion, 
  EventFinancialSummary,
  Item,
  Participant 
} from './types';

/**
 * Calcule le résumé financier complet d'un événement
 */
export function calculateEventFinancialSummary(
  items: Item[],
  participants: Participant[]
): EventFinancialSummary {
  const acceptedParticipants = participants.filter(p => p.status === 'accepted');
  const itemsWithCost = items.filter(item => item.actual_cost !== null && item.actual_cost > 0);
  
  // Calcul du coût total
  const totalCost = itemsWithCost.reduce((sum, item) => sum + (item.actual_cost || 0), 0);
  
  // Coût par personne (répartition équitable)
  const costPerPerson = acceptedParticipants.length > 0 ? totalCost / acceptedParticipants.length : 0;
  
  // Calcul des résumés par participant
  const participantSummaries = calculateParticipantSummaries(
    itemsWithCost,
    acceptedParticipants,
    costPerPerson
  );
  
  // Calcul des suggestions de remboursement
  const settlementSuggestions = calculateOptimalSettlements(participantSummaries);
  
  return {
    event_id: items[0]?.event_id || 0,
    total_cost: totalCost,
    cost_per_person: costPerPerson,
    participants_count: acceptedParticipants.length,
    items_with_cost: itemsWithCost.length,
    settlement_suggestions: settlementSuggestions,
    participant_summaries: participantSummaries
  };
}

/**
 * Calcule les résumés financiers pour chaque participant
 */
function calculateParticipantSummaries(
  items: Item[],
  participants: Participant[],
  costPerPerson: number
): FinancialSummary[] {
  return participants.map(participant => {
    // Calculer ce que ce participant a payé
    const totalSpent = items
      .filter(item => item.paid_by_participant_id === participant.id)
      .reduce((sum, item) => sum + (item.actual_cost || 0), 0);
    
    // Ce qu'il doit (part équitable)
    const totalOwed = costPerPerson;
    
    // Solde net (positif = doit recevoir, négatif = doit payer)
    const netBalance = totalSpent - totalOwed;
    
    return {
      id: 0, // Sera assigné par la base de données
      event_id: items[0]?.event_id || 0,
      participant_id: participant.id,
      total_spent: totalSpent,
      total_owed: totalOwed,
      net_balance: netBalance,
      last_calculated_at: new Date().toISOString(),
      participant
    };
  });
}

/**
 * Calcule les suggestions de remboursement optimales
 * Utilise un algorithme de minimisation des transactions
 */
function calculateOptimalSettlements(summaries: FinancialSummary[]): SettlementSuggestion[] {
  const suggestions: SettlementSuggestion[] = [];
  
  // Séparer les créditeurs (doivent recevoir) et les débiteurs (doivent payer)
  const creditors = summaries
    .filter(s => s.net_balance > 0.01) // Seuil de 1 centime pour éviter les micro-transactions
    .sort((a, b) => b.net_balance - a.net_balance); // Trier par montant décroissant
  
  const debtors = summaries
    .filter(s => s.net_balance < -0.01)
    .sort((a, b) => a.net_balance - b.net_balance); // Trier par dette décroissante
  
  // Algorithme de compensation optimale
  let creditorIndex = 0;
  let debtorIndex = 0;
  
  while (creditorIndex < creditors.length && debtorIndex < debtors.length) {
    const creditor = creditors[creditorIndex];
    const debtor = debtors[debtorIndex];
    
    const amountToSettle = Math.min(creditor.net_balance, Math.abs(debtor.net_balance));
    
    if (amountToSettle > 0.01) { // Seuil minimum
      suggestions.push({
        from_participant: debtor.participant!,
        to_participant: creditor.participant!,
        amount: Math.round(amountToSettle * 100) / 100, // Arrondir à 2 décimales
        description: `Remboursement pour les frais partagés`
      });
      
      // Mettre à jour les soldes
      creditor.net_balance -= amountToSettle;
      debtor.net_balance += amountToSettle;
    }
    
    // Passer au suivant si le solde est épuisé
    if (Math.abs(creditor.net_balance) < 0.01) {
      creditorIndex++;
    }
    if (Math.abs(debtor.net_balance) < 0.01) {
      debtorIndex++;
    }
  }
  
  return suggestions;
}

/**
 * Convertit un coût estimé (€, €€, €€€) en valeur numérique approximative
 */
export function estimatedCostToNumber(estimatedCost: string | null): number {
  switch (estimatedCost) {
    case '€':
      return 10; // Approximation pour "pas cher"
    case '€€':
      return 25; // Approximation pour "moyen"
    case '€€€':
      return 50; // Approximation pour "cher"
    default:
      return 0;
  }
}

/**
 * Formate un montant en euros avec la devise
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}

/**
 * Calcule les statistiques financières pour un utilisateur
 */
export function calculateUserFinancialStats(
  userEvents: EventFinancialSummary[]
): {
  totalEventsWithCosts: number;
  totalAmountManaged: number;
  averageCostPerEvent: number;
  totalSavingsFromSharing: number;
} {
  const eventsWithCosts = userEvents.filter(event => event.total_cost > 0);
  const totalAmount = eventsWithCosts.reduce((sum, event) => sum + event.total_cost, 0);
  const averageCost = eventsWithCosts.length > 0 ? totalAmount / eventsWithCosts.length : 0;
  
  // Calcul des économies réalisées grâce au partage
  const totalSavings = eventsWithCosts.reduce((sum, event) => {
    // Économie = coût total - coût par personne (ce que l'utilisateur aurait payé seul vs partagé)
    return sum + (event.total_cost - event.cost_per_person);
  }, 0);
  
  return {
    totalEventsWithCosts: eventsWithCosts.length,
    totalAmountManaged: totalAmount,
    averageCostPerEvent: averageCost,
    totalSavingsFromSharing: Math.max(0, totalSavings)
  };
}

/**
 * Valide qu'un montant est valide pour une transaction
 */
export function validateAmount(amount: number): { isValid: boolean; error?: string } {
  if (isNaN(amount) || !isFinite(amount)) {
    return { isValid: false, error: "Le montant doit être un nombre valide" };
  }
  
  if (amount <= 0) {
    return { isValid: false, error: "Le montant doit être positif" };
  }
  
  if (amount > 10000) {
    return { isValid: false, error: "Le montant ne peut pas dépasser 10 000€" };
  }
  
  // Vérifier qu'il n'y a pas plus de 2 décimales
  if (Math.round(amount * 100) !== amount * 100) {
    return { isValid: false, error: "Le montant ne peut avoir plus de 2 décimales" };
  }
  
  return { isValid: true };
}

/**
 * Génère un résumé textuel des remboursements
 */
export function generateSettlementSummary(suggestions: SettlementSuggestion[]): string {
  if (suggestions.length === 0) {
    return "✅ Tous les comptes sont équilibrés ! Aucun remboursement nécessaire.";
  }
  
  const summary = suggestions.map(suggestion => 
    `💰 ${suggestion.from_participant.name_display} doit ${formatCurrency(suggestion.amount)} à ${suggestion.to_participant.name_display}`
  ).join('\n');
  
  const totalAmount = suggestions.reduce((sum, s) => sum + s.amount, 0);
  
  return `${summary}\n\n📊 Total des remboursements : ${formatCurrency(totalAmount)}`;
}
