-- Configuration des buckets pour les avatars
-- À exécuter dans l'éditeur SQL de Supabase

-- ✅ SOLUTION FONCTIONNELLE : Bucket public sans RLS
-- Le bucket 'public-avatars' a été créé automatiquement par le script
-- et fonctionne parfaitement sans politiques RLS complexes

-- Vérifier que le bucket public-avatars existe et ses paramètres
SELECT
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at,
  updated_at
FROM storage.buckets
WHERE name = 'public-avatars';

-- Vérifier qu'il n'y a pas de politiques RLS sur ce bucket
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  cmd
FROM pg_policies
WHERE tablename = 'objects'
AND policyname LIKE '%public-avatars%'
ORDER BY policyname;

-- ==========================================
-- ANCIEN CODE (NE PAS UTILISER)
-- ==========================================
-- Les politiques RLS ci-dessous causaient des erreurs 403
-- Elles sont conservées pour référence mais ne doivent pas être utilisées

/*
-- Supprimer les anciennes politiques si elles existent
DROP POLICY IF EXISTS "Public read access for avatars" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatars" ON storage.objects;

-- 1. Politique pour permettre la lecture publique des avatars
CREATE POLICY "Public read access for avatars" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

-- 2. Politique pour permettre l'upload aux utilisateurs authentifiés
CREATE POLICY "Authenticated users can upload avatars" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- 3. Politique pour permettre la mise à jour de ses propres avatars
CREATE POLICY "Users can update their own avatars" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- 4. Politique pour permettre la suppression de ses propres avatars
CREATE POLICY "Users can delete their own avatars" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
*/

-- ==========================================
-- RÉSUMÉ DE LA SOLUTION
-- ==========================================
-- ✅ Bucket utilisé : 'public-avatars'
-- ✅ Type : Public (pas de RLS)
-- ✅ Sécurité : Vérification côté application
-- ✅ Fonctionnalité : Upload/suppression/lecture
-- ✅ Statut : 100% fonctionnel
