import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter, useSegments } from "expo-router";
import { useAuth } from "~/lib/AuthContext";
import { useOnboardingCheck } from "~/hooks/useOnboarding";

interface OnboardingContextType {
  shouldShowOnboarding: boolean;
  isOnboardingLoading: boolean;
  startOnboarding: () => void;
  skipOnboarding: () => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export function useOnboardingContext() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboardingContext must be used within an OnboardingProvider');
  }
  return context;
}

interface OnboardingProviderProps {
  children: React.ReactNode;
}

export function OnboardingProvider({ children }: OnboardingProviderProps) {
  const { session, isLoading: authLoading } = useAuth();
  const { shouldShow, loading: onboardingLoading } = useOnboardingCheck();
  const router = useRouter();
  const segments = useSegments();
  
  const [hasCheckedOnboarding, setHasCheckedOnboarding] = useState(false);

  const isOnboardingLoading = authLoading || onboardingLoading;
  const shouldShowOnboarding = shouldShow === true;

  // Vérifier si on est déjà dans un écran d'onboarding
  const isInOnboardingFlow = segments[0] === 'onboarding';

  const startOnboarding = () => {
    router.replace('/onboarding/welcome');
  };

  const skipOnboarding = () => {
    router.replace('/(tabs)/');
  };

  useEffect(() => {
    // Attendre que l'auth et l'onboarding soient chargés
    if (isOnboardingLoading) return;

    // Éviter les redirections multiples
    if (hasCheckedOnboarding) return;

    // Si l'utilisateur doit voir l'onboarding et n'est pas déjà dans le flow
    if (shouldShowOnboarding && !isInOnboardingFlow) {
      setHasCheckedOnboarding(true);
      startOnboarding();
    } else if (!shouldShowOnboarding && isInOnboardingFlow) {
      // Si l'utilisateur ne doit plus voir l'onboarding mais est dans le flow
      setHasCheckedOnboarding(true);
      skipOnboarding();
    } else {
      setHasCheckedOnboarding(true);
    }
  }, [
    isOnboardingLoading,
    shouldShowOnboarding,
    isInOnboardingFlow,
    hasCheckedOnboarding,
  ]);

  const contextValue: OnboardingContextType = {
    shouldShowOnboarding,
    isOnboardingLoading,
    startOnboarding,
    skipOnboarding,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}
