/**
 * Script de test pour les fonctionnalités financières
 * Teste les calculs et l'intégration sans modifier la DB
 */

import { 
  calculateEventFinancialSummary,
  formatCurrency,
  validateAmount,
  generateSettlementSummary 
} from '../lib/financialCalculator.js';

// Données de test
const testParticipants = [
  { id: 1, name_display: "<PERSON>", status: "accepted" },
  { id: 2, name_display: "<PERSON>", status: "accepted" },
  { id: 3, name_display: "<PERSON>", status: "accepted" },
  { id: 4, name_display: "<PERSON>", status: "accepted" }
];

const testItems = [
  { 
    id: 1, 
    event_id: 1, 
    name: "Pizza", 
    actual_cost: 25.00, 
    paid_by_participant_id: 1 
  },
  { 
    id: 2, 
    event_id: 1, 
    name: "<PERSON><PERSON>", 
    actual_cost: 15.00, 
    paid_by_participant_id: 2 
  },
  { 
    id: 3, 
    event_id: 1, 
    name: "<PERSON>ser<PERSON>", 
    actual_cost: 8.00, 
    paid_by_participant_id: 3 
  }
];

console.log('🧮 Test du moteur de calcul financier\n');

// Test des calculs
const summary = calculateEventFinancialSummary(testItems, testParticipants);

console.log('📊 Résumé de l\'événement :');
console.log(`Total : ${formatCurrency(summary.total_cost)}`);
console.log(`Par personne : ${formatCurrency(summary.cost_per_person)}`);
console.log(`Participants : ${summary.participants_count}`);
console.log('');

console.log('👥 Résumé par participant :');
summary.participant_summaries.forEach(p => {
  console.log(`${p.participant.name_display}:`);
  console.log(`  Payé: ${formatCurrency(p.total_spent)}`);
  console.log(`  Doit: ${formatCurrency(p.total_owed)}`);
  console.log(`  Solde: ${formatCurrency(p.net_balance)} ${p.net_balance > 0 ? '(à recevoir)' : '(à payer)'}`);
  console.log('');
});

console.log('💰 Suggestions de remboursement :');
if (summary.settlement_suggestions.length > 0) {
  summary.settlement_suggestions.forEach(s => {
    console.log(`${s.from_participant.name_display} → ${s.to_participant.name_display} : ${formatCurrency(s.amount)}`);
  });
} else {
  console.log('✅ Aucun remboursement nécessaire !');
}

console.log('\n🎉 Tests réussis ! Le moteur de calcul fonctionne parfaitement.');
