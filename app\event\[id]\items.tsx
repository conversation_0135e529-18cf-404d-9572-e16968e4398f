import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import {
  fetchEventDetails,
  fetchItemsForEvent,
  createItem,
  updateItem,
  deleteItem,
} from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Item } from "~/lib/types";
import { ItemManager } from "~/components/ItemManager";

export default function EventItemsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { session } = useAuth();

  const [event, setEvent] = useState<Event | null>(null);
  const [items, setItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const isOrganizer = event?.organizer_id === session?.user?.id;

  // Vérification des permissions
  useEffect(() => {
    if (event && session?.user?.id && !isOrganizer) {
      showToast("Seul l'organisateur peut gérer les items", { type: "error" });
      router.back();
    }
  }, [event, session?.user?.id, isOrganizer, router]);

  useEffect(() => {
    if (id && session?.user?.id) {
      loadData();
    }
  }, [id, session?.user?.id]);

  const loadData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const eventId = parseInt(id as string, 10);
      const [eventDetails, eventItems] = await Promise.all([
        fetchEventDetails(eventId),
        fetchItemsForEvent(eventId),
      ]);

      if (eventDetails) {
        setEvent(eventDetails);
      } else {
        showToast("Événement non trouvé", { type: "error" });
        router.back();
        return;
      }

      setItems(eventItems || []);
    } catch (error) {
      console.error("Error loading data:", error);
      showToast("Erreur lors du chargement.", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const handleItemsChange = async (newItems: Item[]) => {
    if (!event) return;

    try {
      setSaving(true);

      // Identifier les nouveaux items (ceux avec un ID temporaire)
      const newItemsToCreate = newItems.filter(
        (item) =>
          item.id > 1000000000 && // ID temporaire (timestamp)
          !items.find((existingItem) => existingItem.id === item.id)
      );

      // Identifier les items supprimés
      const deletedItems = items.filter(
        (existingItem) =>
          !newItems.find((newItem) => newItem.id === existingItem.id)
      );

      // Créer les nouveaux items
      for (const newItem of newItemsToCreate) {
        const itemData = {
          event_id: event.id,
          suggester_id: session?.user?.id || null,
          name: newItem.name,
          category: newItem.category,
          estimated_cost: newItem.estimated_cost,
          estimated_effort: newItem.estimated_effort,
          is_suggestion: !isOrganizer,
          is_personal: false,
          assigned_participant_id: null,
          fixed_by_participant_id: null,
          completed: false,
        };

        await createItem(itemData);
      }

      // Supprimer les items supprimés
      for (const deletedItem of deletedItems) {
        if (deletedItem.id <= 1000000000) {
          // Seulement les vrais items de la DB
          await deleteItem(deletedItem.id);
        }
      }

      // Recharger les données pour avoir les vrais IDs
      await loadData();

      showToast("Items mis à jour !", { type: "success" });
    } catch (error) {
      console.error("Error updating items:", error);
      showToast("Erreur lors de la mise à jour.", { type: "error" });
      // Recharger en cas d'erreur pour revenir à l'état cohérent
      await loadData();
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-muted-foreground">
          Chargement des items...
        </Text>
      </View>
    );
  }

  if (!event) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <Text className="text-destructive">Événement non trouvé</Text>
        <Button onPress={() => router.back()} className="mt-4">
          <Text className="text-primary-foreground">Retour</Text>
        </Button>
      </View>
    );
  }

  return (
    <ScrollView
      className="flex-1 bg-background"
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        {/* Header */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-xl">Items - {event.title}</CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-muted-foreground mb-4">
              Gérez la liste des items à apporter pour cet événement.
            </Text>

            {/* Statistiques */}
            <View className="flex-row justify-between items-center p-3 bg-muted/50 rounded-lg">
              <View className="items-center">
                <Text className="text-lg font-semibold text-foreground">
                  {items.length}
                </Text>
                <Text className="text-xs text-muted-foreground">
                  Items total
                </Text>
              </View>
              <View className="items-center">
                <Text className="text-lg font-semibold text-green-600">
                  {items.filter((item) => item.assigned_participant_id).length}
                </Text>
                <Text className="text-xs text-muted-foreground">Assignés</Text>
              </View>
              <View className="items-center">
                <Text className="text-lg font-semibold text-orange-600">
                  {items.filter((item) => !item.assigned_participant_id).length}
                </Text>
                <Text className="text-xs text-muted-foreground">Libres</Text>
              </View>
              <View className="items-center">
                <Text className="text-lg font-semibold text-blue-600">
                  {items.filter((item) => item.completed).length}
                </Text>
                <Text className="text-xs text-muted-foreground">Apportés</Text>
              </View>
            </View>
          </CardContent>
        </Card>

        {/* Gestionnaire d'items */}
        <ItemManager
          items={items}
          onItemsChange={handleItemsChange}
          eventId={event.id}
          isOrganizer={isOrganizer}
          className="mb-6"
        />

        {/* Actions */}
        <Card>
          <CardContent className="gap-3 pt-6">
            <Button
              onPress={() => router.push(`/event/${id}/distribute`)}
              variant="outline"
              className="w-full"
              disabled={items.length === 0}
            >
              <Text>🎯 Répartir automatiquement</Text>
            </Button>

            <Button
              onPress={() => router.back()}
              variant="outline"
              className="w-full"
            >
              <Text>← Retour à l'événement</Text>
            </Button>
          </CardContent>
        </Card>

        {saving && (
          <View className="absolute inset-0 bg-background/80 justify-center items-center">
            <ActivityIndicator size="large" />
            <Text className="mt-2 text-muted-foreground">
              Sauvegarde en cours...
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
}
