const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function populateTestData() {
  console.log('🚀 Création des données de test...');

  try {
    // 1. Créer un utilisateur de test (organisateur)
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'TestPassword123!',
      email_confirm: true
    });

    if (authError) {
      console.error('Erreur création utilisateur:', authError);
      return;
    }

    const userId = authUser.user.id;
    console.log('✅ Utilisateur créé:', userId);

    // 2. Créer un événement de test
    const { data: event, error: eventError } = await supabase
      .from('events')
      .insert({
        title: '🍕 Soirée Pizza Test',
        description: 'Événement de test pour valider l\'application',
        date: '2024-12-31',
        time: '19:00',
        location: 'Chez Alice - 123 Rue de la Fête',
        organizer_id: userId,
        icon: '🍕'
      })
      .select()
      .single();

    if (eventError) {
      console.error('Erreur création événement:', eventError);
      return;
    }

    console.log('✅ Événement créé:', event.title);

    // 3. Ajouter des participants
    const participants = [
      { event_id: event.id, anonymous_name: 'Alice', status: 'accepted' },
      { event_id: event.id, anonymous_name: 'Bob', status: 'pending' },
      { event_id: event.id, anonymous_name: 'Charlie', status: 'accepted' },
      { event_id: event.id, anonymous_name: 'Diana', status: 'pending' },
    ];

    const { error: participantsError } = await supabase
      .from('participants')
      .insert(participants);

    if (participantsError) {
      console.error('Erreur création participants:', participantsError);
      return;
    }

    console.log('✅ Participants ajoutés:', participants.length);

    // 4. Ajouter des items variés
    const items = [
      // Nourriture
      {
        event_id: event.id,
        name: 'Pizza Margherita (x2)',
        category: 'food',
        estimated_cost: 'medium',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      },
      {
        event_id: event.id,
        name: 'Pizza 4 Fromages (x2)',
        category: 'food',
        estimated_cost: 'medium',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      },
      {
        event_id: event.id,
        name: 'Salade verte',
        category: 'food',
        estimated_cost: 'low',
        estimated_effort: 'medium',
        is_suggestion: false,
        is_personal: false
      },
      {
        event_id: event.id,
        name: 'Tiramisu maison',
        category: 'food',
        estimated_cost: 'medium',
        estimated_effort: 'high',
        is_suggestion: true,
        is_personal: false
      },

      // Boissons
      {
        event_id: event.id,
        name: 'Coca Cola (2L x3)',
        category: 'drinks',
        estimated_cost: 'low',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      },
      {
        event_id: event.id,
        name: 'Bières (pack de 12)',
        category: 'drinks',
        estimated_cost: 'medium',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      },
      {
        event_id: event.id,
        name: 'Vin rouge',
        category: 'drinks',
        estimated_cost: 'medium',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      },
      {
        event_id: event.id,
        name: 'Eau pétillante (6 bouteilles)',
        category: 'drinks',
        estimated_cost: 'low',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      },

      // Matériel
      {
        event_id: event.id,
        name: 'Enceinte Bluetooth',
        category: 'equipment',
        estimated_cost: 'expensive',
        estimated_effort: 'medium',
        is_suggestion: false,
        is_personal: true
      },
      {
        event_id: event.id,
        name: 'Assiettes jetables (x20)',
        category: 'equipment',
        estimated_cost: 'low',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      },
      {
        event_id: event.id,
        name: 'Verres en plastique (x30)',
        category: 'equipment',
        estimated_cost: 'low',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      },
      {
        event_id: event.id,
        name: 'Nappes en papier',
        category: 'equipment',
        estimated_cost: 'low',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      },

      // Divertissement
      {
        event_id: event.id,
        name: 'Playlist Spotify',
        category: 'entertainment',
        estimated_cost: 'free',
        estimated_effort: 'medium',
        is_suggestion: false,
        is_personal: false
      },
      {
        event_id: event.id,
        name: 'Jeu de cartes',
        category: 'entertainment',
        estimated_cost: 'low',
        estimated_effort: 'low',
        is_suggestion: true,
        is_personal: true
      },

      // Autres
      {
        event_id: event.id,
        name: 'Sacs poubelle',
        category: 'other',
        estimated_cost: 'low',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false
      }
    ];

    const { error: itemsError } = await supabase
      .from('items')
      .insert(items);

    if (itemsError) {
      console.error('Erreur création items:', itemsError);
      return;
    }

    console.log('✅ Items ajoutés:', items.length);

    // 5. Créer des contacts de test
    const contacts = [
      {
        user_id: userId,
        name: 'Marie Dupont',
        email: '<EMAIL>',
        phone: '+33 6 12 34 56 78'
      },
      {
        user_id: userId,
        name: 'Jean Martin',
        email: '<EMAIL>',
        phone: '+33 6 87 65 43 21'
      },
      {
        user_id: userId,
        name: 'Sophie Bernard',
        email: '<EMAIL>',
        phone: '+33 6 11 22 33 44'
      }
    ];

    const { error: contactsError } = await supabase
      .from('contacts')
      .insert(contacts);

    if (contactsError) {
      console.error('Erreur création contacts:', contactsError);
      return;
    }

    console.log('✅ Contacts ajoutés:', contacts.length);

    console.log('\n🎉 DONNÉES DE TEST CRÉÉES AVEC SUCCÈS !');
    console.log('\n📋 INFORMATIONS DE CONNEXION :');
    console.log('Email:', '<EMAIL>');
    console.log('Mot de passe:', 'TestPassword123!');
    console.log('ID Événement:', event.id);
    console.log('\n🔗 LIEN DE PARTAGE :');
    console.log(`${process.env.EXPO_PUBLIC_APP_URL || 'http://localhost:8082'}/join/${event.id}`);

  } catch (error) {
    console.error('❌ Erreur générale:', error);
  }
}

// Exécuter le script
populateTestData();
