import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  ActivityIndicator,
  Platform,
  Modal,
  Pressable,
  Share,
  Linking,
  Alert,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { P } from "~/components/ui/typography";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import {
  fetchEventDetails,
  updateEvent,
  fetchParticipantsForEvent,
  deleteEvent,
  leaveEvent,
} from "~/lib/supabaseCrud";
import { Separator } from "~/components/ui/separator";
import { Button } from "~/components/ui/button"; // Changed to Button
import { EventOptionsSection } from "~/components/EventOptionsSection";

import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Participant } from "~/lib/types";
import { EventItemsSection } from "~/components/EventItemsSection"; // Added import
import {
  generateShareUrl,
  generateShareMessage,
  generateSocialShareLinks,
} from "~/components/QRCodeGenerator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { saveEventAsTemplate } from "~/lib/templateService";
import { geocodeAddress } from "~/lib/geocodingService";
import MapComponent from "~/components/MapComponent";
import { Badge } from "~/components/ui/badge";

export default function EventDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { session } = useAuth();
  const router = useRouter();
  const [event, setEvent] = useState<Event | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [updateLoading, setUpdateLoading] = useState<{
    [key: string]: boolean;
  }>({});
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [leaveLoading, setLeaveLoading] = useState(false);
  const [leaveModalVisible, setLeaveModalVisible] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);
  const [currentUserParticipant, setCurrentUserParticipant] =
    useState<Participant | null>(null);
  const [mapLocation, setMapLocation] = useState({ lat: -3.745, lng: -38.523 });

  // Fonction pour charger les détails de l'événement
  const loadEventDetails = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const eventId = parseInt(id as string, 10);
      // Charger l'événement et les participants en parallèle
      const [eventDetails, eventParticipants, eventBadges] = await Promise.all([
        fetchEventDetails(eventId),
        fetchParticipantsForEvent(eventId),
        fetchBadgesForEvent(),
      ]);

      if (eventDetails) {
        setEvent(eventDetails);
        // Le titre est maintenant géré par notre header personnalisé
      }

      setParticipants((eventParticipants || []) as Participant[]);

      const badges = eventBadges || [];

      // Trouver le participant actuel pour vérifier ses permissions
      if (session?.user?.id && eventParticipants) {
        const userParticipant = eventParticipants.find(
          (p) => p.user_id === session.user.id
        );
        setCurrentUserParticipant((userParticipant as Participant) || null);
      }
    } catch (error) {
      console.error("Error loading event details:", error);
      showToast("Erreur lors du chargement des détails.", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  // Charger les données une seule fois au montage du composant
  const fetchBadgesForEvent = async () => {
    return [];
  };
  useEffect(() => {
    loadEventDetails();
  }, [id]);

  useEffect(() => {
    if (event?.location) {
      geocodeAddress(event.location)
        .then((coords) => {
          setMapLocation(coords);
        })
        .catch((error) => {
          console.error("Erreur lors du géocodage:", error);
        });
    }
  }, [event]);

  const handleUpdate = async (field: string, value: any) => {
    if (!event || !session || session.user.id !== event.organizer_id) {
      showToast("Vous n'avez pas les droits pour modifier cet événement.", {
        type: "error",
      });
      return;
    }

    try {
      setUpdateLoading((prev) => ({ ...prev, [field]: true }));
      const updatedEvent = await updateEvent(event.id, { [field]: value });

      if (updatedEvent) {
        showToast("Option mise à jour !", { type: "success" });
        setEvent(updatedEvent);
      } else {
        throw new Error("Échec de la mise à jour");
      }
    } catch (error) {
      console.error("Error updating event:", error);
      showToast("Erreur lors de la mise à jour.", { type: "error" });
    } finally {
      setUpdateLoading((prev) => ({ ...prev, [field]: false }));
    }
  };

  // Fonction pour supprimer l'événement
  const handleDeleteEvent = async () => {
    if (!event || !session || session.user.id !== event.organizer_id) {
      showToast("Vous n'avez pas les droits pour supprimer cet événement.", {
        type: "error",
      });
      return;
    }

    try {
      setDeleteLoading(true);
      const success = await deleteEvent(event.id);

      if (success) {
        showToast("Événement supprimé avec succès !", { type: "success" });
        setDeleteModalVisible(false);
        // Retourner à la page d'accueil des événements
        router.push("/(tabs)");
      } else {
        throw new Error("Échec de la suppression");
      }
    } catch (error) {
      console.error("Error deleting event:", error);
      showToast("Erreur lors de la suppression.", { type: "error" });
    } finally {
      setDeleteLoading(false);
    }
  };

  // Fonction pour quitter l'événement
  const handleLeaveEvent = async () => {
    if (!event || !session?.user?.id) return;

    try {
      setLeaveLoading(true);
      const success = await leaveEvent(event.id, session.user.id);

      if (success) {
        showToast("Vous avez quitté l'événement avec succès !", {
          type: "success",
        });
        router.push("/(tabs)");
      } else {
        showToast("Erreur lors de la sortie de l'événement.", {
          type: "error",
        });
      }
    } catch (error) {
      console.error("Error leaving event:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Erreur inconnue";
      showToast(`Erreur: ${errorMessage}`, {
        type: "error",
      });
    } finally {
      setLeaveLoading(false);
      setLeaveModalVisible(false);
    }
  };

  // Fonction pour partager l'événement
  const handleShareEvent = async () => {
    if (!event) return;

    const shareUrl = generateShareUrl(event.id);
    const shareMessage = generateShareMessage(event.title, shareUrl);

    try {
      if (Platform.OS === "web") {
        // Sur web, copier le lien dans le presse-papiers
        await navigator.clipboard.writeText(shareUrl);
        showToast("Lien copié dans le presse-papiers !", { type: "success" });
      } else {
        // Sur mobile, utiliser l'API Share native
        await Share.share({
          message: shareMessage,
          url: shareUrl,
          title: `Événement: ${event.title}`,
        });
      }
    } catch (error) {
      console.error("Error sharing event:", error);
      showToast("Erreur lors du partage.", { type: "error" });
    }
    setMenuVisible(false);
  };

  // Fonction pour partager via WhatsApp
  const handleShareWhatsApp = async () => {
    if (!event) return;

    const shareUrl = generateShareUrl(event.id);
    const socialLinks = generateSocialShareLinks(event.title, shareUrl);

    try {
      if (Platform.OS === "web") {
        window.open(socialLinks.whatsapp, "_blank");
      } else {
        await Linking.openURL(socialLinks.whatsapp);
      }
      showToast("Ouverture de WhatsApp...", { type: "success" });
    } catch (error) {
      console.error("Error sharing via WhatsApp:", error);
      showToast("Erreur lors du partage WhatsApp.", { type: "error" });
    }
    setMenuVisible(false);
  };

  // Fonction pour naviguer vers la page de partage avec QR code
  const handleShareWithQR = () => {
    setMenuVisible(false);
    router.push(`/event/${id}/share`);
  };

  // Fonction pour sauvegarder l'événement comme template
  const handleSaveAsTemplate = async () => {
    if (!event || !session?.user?.id) return;

    setMenuVisible(false);

    // Demander le nom du template
    Alert.alert(
      "Sauvegarder comme template",
      "Donnez un nom à ce template pour le réutiliser plus tard",
      [
        { text: "Annuler", style: "cancel" },
        {
          text: "Sauvegarder",
          onPress: async () => {
            try {
              const templateName = `Template ${event.title}`;
              const templateDescription = `Template basé sur l'événement "${event.title}"`;

              const template = await saveEventAsTemplate(
                event.id,
                templateName,
                templateDescription,
                session.user.id
              );

              if (template) {
                showToast("Template sauvegardé avec succès !", {
                  type: "success",
                });
              } else {
                showToast("Erreur lors de la sauvegarde", { type: "error" });
              }
            } catch (error) {
              console.error("Error saving template:", error);
              showToast("Erreur lors de la sauvegarde", { type: "error" });
            }
          },
        },
      ]
    );
  };

  // Si l'événement est en cours de chargement, afficher un loader
  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" className="text-primary" />
      </View>
    );
  }

  // Si l'événement n'existe pas, afficher un message d'erreur
  if (!event) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-lg text-destructive">Événement introuvable.</Text>
        <Button className="mt-4" onPress={() => router.back()}>
          <Text>Retour</Text>
        </Button>
      </View>
    );
  }

  const isOrganizer = session?.user?.id === event.organizer_id;
  const isParticipant = !!currentUserParticipant;
  const isGuest = currentUserParticipant?.role === "guest";
  const canManageEvent = isOrganizer; // Pour l'instant, seul l'organisateur peut gérer
  const canViewAsGuest = isParticipant && !isOrganizer;

  const eventDate = new Date(event.date_time);
  const formattedDate = eventDate.toLocaleDateString("fr-FR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  const formattedTime = eventDate.toLocaleTimeString("fr-FR", {
    hour: "2-digit",
    minute: "2-digit",
  });

  return (
    <View className="flex-1 bg-background">
      {/* Header cross-platform avec chevron de retour et menu */}
      <View className="flex-row items-center px-4 pt-14 bg-background border-b border-border">
        <Pressable
          onPress={() => {
            // Si l'utilisateur est un invité, aller directement à l'accueil
            // Sinon, utiliser le comportement de retour normal
            router.back();
          }}
          className="w-12 h-12 flex items-center justify-center"
          {...(Platform.OS === "web"
            ? {
                style: { cursor: "pointer" },
              }
            : {})}
        >
          <Text className="text-3xl font-bold text-primary">‹</Text>
        </Pressable>
        <View className="flex-1 items-center">
          <View>
            <Text
              className="text-lg font-semibold text-foreground"
              numberOfLines={1}
            >
              {event.title}
            </Text>
          </View>
        </View>
        <Popover open={menuVisible} onOpenChange={setMenuVisible}>
          <PopoverTrigger asChild>
            <Pressable
              onPress={() => setMenuVisible(true)}
              className="w-12 h-12 flex items-center justify-center"
            >
              <Text className="text-2xl text-foreground">⋯</Text>
            </Pressable>
          </PopoverTrigger>
          <PopoverContent className="w-64 bg-card p-0">
            <View>
              {/* Option Éditer (seulement pour l'organisateur) */}
              {isOrganizer && (
                <Pressable
                  className="flex-row items-center px-6 py-4 border-b border-border"
                  onPress={() => {
                    setMenuVisible(false);
                    router.push(`/event/${id}/edit`);
                  }}
                >
                  <Text className="text-lg mr-4">⚙️</Text>
                  <Text className="text-foreground font-medium flex-1">
                    Éditer
                  </Text>
                </Pressable>
              )}

              {/* Option Partager */}
              <Pressable
                className="flex-row items-center px-6 py-4 border-b border-border"
                onPress={handleShareEvent}
              >
                <Text className="text-lg mr-4">📤</Text>
                <Text className="text-foreground font-medium flex-1">
                  Partager le lien
                </Text>
              </Pressable>

              {/* Option Partager via WhatsApp */}
              <Pressable
                className="flex-row items-center px-6 py-4 border-b border-border"
                onPress={handleShareWhatsApp}
              >
                <Text className="text-lg mr-4">💬</Text>
                <Text className="text-foreground font-medium flex-1">
                  Via WhatsApp
                </Text>
              </Pressable>

              {/* Option Partager avec QR Code */}
              <Pressable
                className="flex-row items-center px-6 py-4 border-b border-border"
                onPress={handleShareWithQR}
              >
                <Text className="text-lg mr-4">📱</Text>
                <Text className="text-foreground font-medium flex-1">
                  QR Code & Plus
                </Text>
              </Pressable>

              {/* Option Finances */}
              <Pressable
                className="flex-row items-center px-6 py-4 border-b border-border"
                onPress={() => {
                  setMenuVisible(false);
                  router.push(`/event/${id}/finances`);
                }}
              >
                <Text className="text-lg mr-4">💰</Text>
                <Text className="text-foreground font-medium flex-1">
                  Gestion financière
                </Text>
              </Pressable>

              {/* Option Sauvegarder comme template */}
              {isOrganizer && (
                <Pressable
                  className="flex-row items-center px-6 py-4 border-b border-border"
                  onPress={handleSaveAsTemplate}
                >
                  <Text className="text-lg mr-4">📋</Text>
                  <Text className="text-foreground font-medium flex-1">
                    Sauvegarder comme template
                  </Text>
                </Pressable>
              )}

              {/* Option Supprimer (seulement pour l'organisateur) */}
              {isOrganizer && (
                <Pressable
                  className="flex-row items-center px-6 py-4"
                  onPress={() => {
                    setMenuVisible(false);
                    setDeleteModalVisible(true);
                  }}
                >
                  <Text className="text-lg mr-4">🗑️</Text>
                  <Text className="text-destructive font-medium flex-1">
                    Supprimer
                  </Text>
                </Pressable>
              )}
            </View>
          </PopoverContent>
        </Popover>
      </View>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
      >
        <View
          className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}
        >
          <Card className="mb-4 border border-border bg-card rounded-xl shadow-sm">
            <CardHeader className="items-center pb-2">
              <Text className="text-6xl mb-2">{event.icon || "🎉"}</Text>
              <CardTitle className="text-2xl text-center font-bold text-foreground">
                {event.title}
              </CardTitle>
              {isOrganizer ? (
                <Badge variant="organizer" className="mt-2">
                  <Text>Vous êtes l'organisateur</Text>
                </Badge>
              ) : (
                <Badge variant="guest" className="mt-2">
                  <Text>Vous êtes un invité</Text>
                </Badge>
              )}
            </CardHeader>
            <CardContent className="gap-3">
              {event.description && (
                <>
                  <P className="text-center text-muted-foreground">
                    {event.description}
                  </P>
                  <Separator />
                </>
              )}
              <View className="flex-row items-center">
                <Text className="text-muted-foreground mr-3 text-lg">📅</Text>
                <Text className="text-base text-foreground font-medium">
                  {formattedDate}
                </Text>
              </View>
              <View className="flex-row items-center">
                <Text className="text-muted-foreground mr-3 text-lg">🕐</Text>
                <Text className="text-base text-foreground font-medium">
                  {formattedTime}
                </Text>
              </View>
              {event.location && (
                <View className="flex-row items-center">
                  <Text className="text-muted-foreground mr-3 text-lg">📍</Text>
                  <Text className="text-base text-foreground font-medium">
                    {event.location}
                  </Text>
                </View>
              )}
              {event.location && (
                <View className="mt-4">
                  <MapComponent location={mapLocation} />
                </View>
              )}
            </CardContent>
          </Card>

          <Card className="mb-4 border border-border bg-card rounded-xl shadow-sm">
            <CardHeader className="flex-row justify-between items-center">
              <CardTitle className="text-lg font-medium text-foreground">
                Actions
              </CardTitle>
              {isOrganizer && (
                <Button
                  variant="outline"
                  size="sm"
                  onPress={() => router.push(`/event/${id}/edit`)}
                >
                  <Text>✏️ Éditer</Text>
                </Button>
              )}
            </CardHeader>
            <CardContent className="gap-4">
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center flex-1 mr-2">
                  <Text className="text-muted-foreground mr-3 text-lg">👥</Text>
                  <Text
                    className="text-foreground font-medium flex-1"
                    numberOfLines={1}
                  >
                    Participants ({participants.length})
                  </Text>
                </View>
                {isOrganizer && (
                  <Button
                    variant="outline"
                    size="sm"
                    onPress={() => router.push(`/event/${id}/participants`)}
                    className="shrink-0"
                  >
                    <Text>Gérer</Text>
                  </Button>
                )}
              </View>
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center flex-1 mr-2">
                  <Text className="text-muted-foreground mr-3 text-lg">📝</Text>
                  <Text
                    className="text-foreground font-medium flex-1"
                    numberOfLines={1}
                  >
                    Items à apporter
                  </Text>
                </View>
                {isOrganizer && (
                  <Button
                    variant="outline"
                    size="sm"
                    onPress={() => router.push(`/event/${id}/items`)}
                    className="shrink-0"
                  >
                    <Text>Gérer</Text>
                  </Button>
                )}
              </View>
              {isOrganizer && (
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center flex-1 mr-2">
                    <Text className="text-muted-foreground mr-3 text-lg">
                      🎯
                    </Text>
                    <Text
                      className="text-foreground font-medium flex-1"
                      numberOfLines={1}
                    >
                      Répartir les tâches
                    </Text>
                  </View>
                  <Button
                    variant="outline"
                    size="sm"
                    onPress={() => router.push(`/event/${id}/distribute`)}
                    className="shrink-0"
                  >
                    <Text>Répartir</Text>
                  </Button>
                </View>
              )}
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center flex-1 mr-2">
                  <Text className="text-muted-foreground mr-3 text-lg">💬</Text>
                  <Text
                    className="text-foreground font-medium flex-1"
                    numberOfLines={1}
                  >
                    Accéder à la discussion
                  </Text>
                </View>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={true}
                  onPress={() => {}}
                  className="shrink-0"
                >
                  <Text className="text-xs">Bientôt dispo</Text>
                </Button>
              </View>
            </CardContent>
          </Card>

          {/* Item Section Card */}
          {event && id && (
            <EventItemsSection
              eventId={parseInt(id as string, 10)}
              isOrganizer={isOrganizer}
              allowPreAssignment={event.allow_pre_assignment} // Pass event option
            />
          )}

          {isOrganizer && (
            <EventOptionsSection
              allowSuggestions={event.allow_suggestions}
              allowPreAssignment={event.allow_pre_assignment}
              onAllowSuggestionsChange={(value) =>
                handleUpdate("allow_suggestions", value)
              }
              onAllowPreAssignmentChange={(value) =>
                handleUpdate("allow_pre_assignment", value)
              }
              loading={{
                allowSuggestions: updateLoading.allow_suggestions,
                allowPreAssignment: updateLoading.allow_pre_assignment,
              }}
              className="mb-6"
            />
          )}

          {/* Bouton de suppression avec popup de confirmation */}
          {isOrganizer && (
            <Button
              variant="destructive"
              className="w-full h-12 mt-4"
              disabled={deleteLoading}
              onPress={() => setDeleteModalVisible(true)}
            >
              <Text className="mr-2 text-lg">🗑️</Text>
              <Text className="text-white font-medium">
                {deleteLoading ? "Suppression..." : "Supprimer l'événement"}
              </Text>
            </Button>
          )}

          {/* Bouton quitter l'événement pour les invités */}
          {canViewAsGuest && (
            <View className="gap-3 mt-4">
              <Button
                variant="outline"
                className="w-full h-12"
                onPress={() => router.push("/(tabs)")}
              >
                <Text className="mr-2 text-lg">🏠</Text>
                <Text className="font-medium">Retour à l'accueil</Text>
              </Button>
              <Button
                variant="destructive"
                className="w-full h-12"
                disabled={leaveLoading}
                onPress={() => setLeaveModalVisible(true)}
              >
                <Text className="mr-2 text-lg">🚪</Text>
                <Text className="text-white font-medium">
                  {leaveLoading ? "Sortie..." : "Quitter l'événement"}
                </Text>
              </Button>
            </View>
          )}

          {/* Modal de suppression d'événement */}
          <Modal
            visible={deleteModalVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setDeleteModalVisible(false)}
          >
            <View className="flex-1 justify-center items-center bg-black/50">
              <View className="bg-card rounded-lg p-6 mx-4 max-w-sm w-full">
                <Text className="text-lg font-semibold mb-2 text-foreground">
                  Supprimer l'événement
                </Text>
                <Text className="text-muted-foreground mb-6">
                  Cette action est irréversible. Cela supprimera définitivement
                  l'événement "{event?.title}" et toutes les données associées
                  (participants, items, discussions).
                </Text>
                <View className="flex-row justify-end gap-3">
                  <Button
                    variant="outline"
                    onPress={() => setDeleteModalVisible(false)}
                  >
                    <Text>Annuler</Text>
                  </Button>
                  <Button
                    variant="destructive"
                    onPress={handleDeleteEvent}
                    disabled={deleteLoading}
                  >
                    <Text>
                      {deleteLoading ? "Suppression..." : "Supprimer"}
                    </Text>
                  </Button>
                </View>
              </View>
            </View>
          </Modal>

          {/* Modal de confirmation pour quitter l'événement */}
          <Modal
            visible={leaveModalVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setLeaveModalVisible(false)}
          >
            <View className="flex-1 justify-center items-center bg-black/50">
              <View className="bg-card rounded-lg p-6 mx-4 max-w-sm w-full">
                <Text className="text-lg font-semibold mb-2 text-foreground">
                  Quitter l'événement
                </Text>
                <Text className="text-muted-foreground mb-6">
                  Êtes-vous sûr de vouloir quitter l'événement "{event?.title}"
                  ? Vous ne recevrez plus les notifications et ne pourrez plus
                  participer aux discussions.
                </Text>
                <View className="flex-row justify-end gap-3">
                  <Button
                    variant="outline"
                    onPress={() => setLeaveModalVisible(false)}
                  >
                    <Text>Annuler</Text>
                  </Button>
                  <Button
                    variant="destructive"
                    onPress={handleLeaveEvent}
                    disabled={leaveLoading}
                  >
                    <Text>{leaveLoading ? "Sortie..." : "Quitter"}</Text>
                  </Button>
                </View>
              </View>
            </View>
          </Modal>

          <View className="h-16" />
        </View>
      </ScrollView>
    </View>
  );
}
