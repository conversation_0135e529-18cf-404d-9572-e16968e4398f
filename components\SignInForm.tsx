import React, { useState } from "react";
import { View, Platform } from "react-native";
import { supabase } from "~/lib/supabase";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { Label } from "~/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { showToast } from "~/lib/toastService";

export default function SignInForm({ onSuccess }: { onSuccess?: () => void }) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);

  async function signInWithEmail() {
    if (!email || !password) {
      showToast("Veuillez remplir tous les champs.", {
        type: "error",
      });
      return;
    }
    setLoading(true);
    const { error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    });

    setLoading(false);
    if (error) {
      showToast(error.message || "Email ou mot de passe incorrect.", {
        type: "error",
      });
    } else {
      showToast("Connexion réussie !", { type: "success" });
      if (onSuccess) onSuccess();
    }
  }

  const handlePress = (callback: () => void) => {
    return Platform.OS === "web"
      ? { onClick: callback }
      : { onPress: callback };
  };

  return (
    <Card className="w-full border-border">
      <CardHeader>
        <CardTitle className="text-2xl">Se connecter</CardTitle>
        <CardDescription>
          Entrez vos identifiants pour accéder à votre compte.
        </CardDescription>
      </CardHeader>
      <CardContent className="gap-4">
        <View className="gap-1.5">
          <Label nativeID="emailLabelSignIn">Email</Label>
          <Input
            nativeID="emailLabelSignIn"
            placeholder="<EMAIL>"
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
          />
        </View>
        <View className="gap-1.5">
          <Label nativeID="passwordLabelSignIn">Mot de passe</Label>
          <Input
            nativeID="passwordLabelSignIn"
            placeholder="********"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
        </View>
      </CardContent>
      <CardFooter>
        <Button
          className="w-full"
          disabled={loading}
          {...handlePress(signInWithEmail)}
        >
          {loading ? (
            <Text>Connexion en cours...</Text>
          ) : (
            <Text>Se connecter</Text>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
