import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { 
  fetchEventDetails,
  fetchParticipantsForEvent,
  fetchItemsForEvent 
} from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Participant, Item } from "~/lib/types";
import { FinancialSummary } from "~/components/FinancialSummary";
import { ItemCostList } from "~/components/ItemCostManager";

export default function EventFinancesScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { session } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [event, setEvent] = useState<Event | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [items, setItems] = useState<Item[]>([]);
  const [currentParticipant, setCurrentParticipant] = useState<Participant | null>(null);
  const [isOrganizer, setIsOrganizer] = useState(false);

  useEffect(() => {
    if (id && session?.user?.id) {
      loadEventData();
    }
  }, [id, session?.user?.id]);

  const loadEventData = async () => {
    if (!id || !session?.user?.id) return;

    try {
      setLoading(true);
      
      // Charger les données en parallèle
      const [eventData, participantsData, itemsData] = await Promise.all([
        fetchEventDetails(parseInt(id)),
        fetchParticipantsForEvent(parseInt(id)),
        fetchItemsForEvent(parseInt(id))
      ]);

      if (!eventData) {
        showToast("Événement non trouvé", { type: "error" });
        router.back();
        return;
      }

      setEvent(eventData);
      setParticipants(participantsData || []);
      setItems(itemsData || []);

      // Déterminer le participant actuel et s'il est organisateur
      const currentPart = participantsData?.find(p => p.user_id === session.user.id);
      setCurrentParticipant(currentPart || null);
      setIsOrganizer(eventData.organizer_id === session.user.id);

    } catch (error) {
      console.error("Error loading event data:", error);
      showToast("Erreur lors du chargement", { type: "error" });
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleItemUpdate = (updatedItem: Item) => {
    setItems(prevItems => 
      prevItems.map(item => 
        item.id === updatedItem.id ? updatedItem : item
      )
    );
  };

  const handleRefresh = () => {
    loadEventData();
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-muted-foreground">
          Chargement des finances...
        </Text>
      </View>
    );
  }

  if (!event) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <Text className="text-destructive">Événement non trouvé</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background">
      <ScrollView>
        <View className={Platform.OS === "web" ? "max-w-4xl mx-auto p-4" : "p-4"}>
          
          {/* En-tête */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex-row items-center">
                <Text className="text-2xl mr-3">{event.icon || "🎉"}</Text>
                <View className="flex-1">
                  <Text className="text-xl font-bold">{event.title}</Text>
                  <Text className="text-sm text-muted-foreground">
                    Gestion financière
                  </Text>
                </View>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <View className="flex-row items-center justify-between">
                <Text className="text-sm text-muted-foreground">
                  📅 {new Date(event.date_time).toLocaleDateString("fr-FR", {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </Text>
                
                <Button
                  variant="outline"
                  size="sm"
                  onPress={() => router.back()}
                >
                  <Text>← Retour</Text>
                </Button>
              </View>
            </CardContent>
          </Card>

          {/* Onglets */}
          <Tabs defaultValue="summary" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="summary">
                <Text>💰 Résumé</Text>
              </TabsTrigger>
              <TabsTrigger value="items">
                <Text>📝 Coûts des items</Text>
              </TabsTrigger>
            </TabsList>

            {/* Onglet Résumé financier */}
            <TabsContent value="summary" className="mt-4">
              <FinancialSummary
                eventId={parseInt(id)}
                items={items}
                participants={participants}
                currentParticipantId={currentParticipant?.id}
                isOrganizer={isOrganizer}
                onRefresh={handleRefresh}
              />
            </TabsContent>

            {/* Onglet Gestion des coûts */}
            <TabsContent value="items" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Coûts des items</CardTitle>
                  <Text className="text-sm text-muted-foreground">
                    {isOrganizer 
                      ? "Ajoutez les coûts réels pour calculer automatiquement les remboursements"
                      : "Seul l'organisateur peut modifier les coûts"
                    }
                  </Text>
                </CardHeader>
                <CardContent>
                  <ItemCostList
                    items={items}
                    participants={participants}
                    onItemUpdate={handleItemUpdate}
                    isOrganizer={isOrganizer}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Actions rapides */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-base">Actions rapides</CardTitle>
            </CardHeader>
            <CardContent className="gap-3">
              <View className="flex-row gap-3">
                <Button
                  variant="outline"
                  onPress={() => router.push(`/event/${id}/items`)}
                  className="flex-1"
                >
                  <Text>📝 Gérer les items</Text>
                </Button>
                
                <Button
                  variant="outline"
                  onPress={() => router.push(`/event/${id}/participants`)}
                  className="flex-1"
                >
                  <Text>👥 Participants</Text>
                </Button>
              </View>
              
              <Button
                variant="outline"
                onPress={() => router.push(`/event/${id}/share`)}
                className="w-full"
              >
                <Text>📱 Partager l'événement</Text>
              </Button>
            </CardContent>
          </Card>

          {/* Aide */}
          <Card className="mt-6 mb-8">
            <CardHeader>
              <CardTitle className="text-base">💡 Comment ça marche ?</CardTitle>
            </CardHeader>
            <CardContent className="gap-2">
              <Text className="text-sm text-muted-foreground">
                • L'organisateur ajoute les coûts réels des items
              </Text>
              <Text className="text-sm text-muted-foreground">
                • L'app calcule automatiquement qui doit combien à qui
              </Text>
              <Text className="text-sm text-muted-foreground">
                • Les suggestions optimisent le nombre de transactions
              </Text>
              <Text className="text-sm text-muted-foreground">
                • Tous les participants voient les calculs en temps réel
              </Text>
            </CardContent>
          </Card>
        </View>
      </ScrollView>
    </View>
  );
}
