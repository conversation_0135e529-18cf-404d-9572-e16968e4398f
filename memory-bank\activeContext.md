# Active Context

## Current Work Focus

- Revamp the `app/(tabs)/profile.tsx` screen for improved UX, ShadCN component usage, and integrated account creation/login.
- Ensure authentication flows (creation, login, logout, profile updates) are robust.

## Recent Changes

- **Created `components/CreateAccountForm.tsx`**: A dedicated ShadCN-style card form for new user registration.
- **Created `components/SignInForm.tsx`**: A dedicated ShadCN-style card form for user login.
- **Rewrote `app/(tabs)/profile.tsx`**:
  - Implemented distinct views for logged-out, anonymous, and fully logged-in users.
  - Integrated `CreateAccountForm` and `SignInForm` into the logged-out flow.
  - Provided a path for anonymous users to upgrade their accounts using `CreateAccountForm`.
  - Restructured the logged-in profile view into logical `Card` sections (Profile Details, Preferences, Security, Account Actions) with improved UI elements.
  - Added state management for switching between sign-in and create-account views (`AuthView` enum).
  - Ensured consistent use of `showToast` for user feedback.
  - Added `is_discoverable` preference handling.
- **Updated `lib/types.ts`**: Added `is_discoverable?: boolean;` to the `Profile` interface.
- Previous work (ShadCN verification and initial documentation) is now superseded by this revamp.

## Next Steps

- Update `memory-bank/progress.md` to reflect the profile page revamp.
- User to test the new profile page and authentication flows thoroughly.
- Address any TypeScript errors related to Supabase client type inference at a project level if they persist.
- Potentially fill out `projectbrief.md` and `productContext.md` if more high-level project goals are clarified.

## Active Decisions & Considerations

- **Dedicated Auth Forms**: Opted for separate `CreateAccountForm` and `SignInForm` components for clarity and better UX over a combined auth component for the profile page's entry point.
- **Profile Structure**: Maintained a card-based layout for the logged-in profile but with clearer separation and improved interactivity (e.g., edit mode for profile details).
- **Anonymous User Flow**: Included a specific UI to encourage anonymous users to complete their registration.
- **Error Handling**: Used `showToast` for user-facing error messages and feedback.
- **`is_discoverable` field**: Added to `Profile` type and UI; assumes backend table `profiles` will have this column.

## Important Patterns & Preferences

- Continued consistent use of ShadCN UI components and NativeWind.
- Clear, focused components for specific tasks (e.g., `CreateAccountForm`).
- State-driven UI changes (e.g., `AuthView` to switch between login/signup forms).
- User preference for thorough documentation in the Memory Bank.

## Learnings & Project Insights

- Refactoring a key screen like Profile requires careful state management, especially for different authentication states.
- Creating dedicated components for forms (like sign-in and sign-up) improves modularity and reusability, even if they share some similarities.
- The `AuthContext` is central to managing session state, and UI components should react to changes in this context.
- TypeScript errors related to library typings (e.g., Supabase client) can sometimes be distinct from application logic errors and may require broader project configuration adjustments.
- **Emoji Picker UI (Hover Effects):** When using `@rn-primitives/tooltip` with `TooltipTrigger asChild`, NativeWind hover styles (e.g., `hover:bg-muted/20`) should be applied directly to the child component of `TooltipTrigger` (e.g., a `Pressable`). If this child is intended to fill a grid cell defined by a parent `View` (e.g., in a `FlatList` with `numColumns`), the child `Pressable` should have `w-full h-full` and the parent `View` should define the actual grid cell dimensions (e.g., `w-[12.5%] aspect-square`).
- **ScrollView Content Padding (Emoji Picker Category Tabs):** To avoid unwanted padding at the ends of a `ScrollView`'s content (visible when content doesn't overflow), set `px-0` (or `pl-0 pr-0`) on the `contentContainerClassName`. Use `justify-start` to pack items to the beginning. If padding around the scrollable area is desired, apply it to the `ScrollView` component itself, not its `contentContainer`.

_(Cline's Note: This file is dynamic and should be updated frequently to reflect the current state of work. It links `productContext.md`, `systemPatterns.md`, and `techContext.md` to the ongoing development effort.)_
