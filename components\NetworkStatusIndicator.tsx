import React, { useState, useEffect } from "react";
import { View, Pressable, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { useNetworkState, networkService } from "~/lib/networkService";
import { sync, syncService } from "~/lib/syncService";
import { offlineQueue } from "~/lib/offlineQueueService";

interface NetworkStatusIndicatorProps {
  showDetails?: boolean;
  position?: 'top' | 'bottom';
  onPress?: () => void;
}

export function NetworkStatusIndicator({ 
  showDetails = false, 
  position = 'top',
  onPress 
}: NetworkStatusIndicatorProps) {
  const networkState = useNetworkState();
  const [syncStatus, setSyncStatus] = useState(sync.getStatus());
  const [queueStats, setQueueStats] = useState(offlineQueue.getStats());
  const [showDetailedView, setShowDetailedView] = useState(false);

  useEffect(() => {
    // Mettre à jour les statuts périodiquement
    const interval = setInterval(() => {
      setSyncStatus(sync.getStatus());
      setQueueStats(offlineQueue.getStats());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    if (!networkState.isConnected) return "bg-red-500";
    if (syncStatus.isSync) return "bg-blue-500";
    if (queueStats.pendingActions > 0) return "bg-yellow-500";
    if (networkState.strength < 30) return "bg-orange-500";
    return "bg-green-500";
  };

  const getStatusText = () => {
    if (!networkState.isConnected) return "Hors ligne";
    if (syncStatus.isSync) return "Synchronisation...";
    if (queueStats.pendingActions > 0) return `${queueStats.pendingActions} en attente`;
    if (networkState.strength < 30) return "Connexion faible";
    return "En ligne";
  };

  const getStatusIcon = () => {
    if (!networkState.isConnected) return "📵";
    if (syncStatus.isSync) return "🔄";
    if (queueStats.pendingActions > 0) return "⏳";
    if (networkState.strength < 30) return "📶";
    return "✅";
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      setShowDetailedView(!showDetailedView);
    }
  };

  const handleForceSync = async () => {
    try {
      await sync.force();
      setSyncStatus(sync.getStatus());
      setQueueStats(offlineQueue.getStats());
    } catch (error) {
      console.error('Force sync failed:', error);
    }
  };

  if (!showDetails) {
    // Vue compacte - juste un indicateur
    return (
      <Pressable
        onPress={handlePress}
        className={`flex-row items-center px-2 py-1 rounded-full ${getStatusColor()}`}
        {...(Platform.OS === "web" ? { style: { cursor: "pointer" } } : {})}
      >
        <Text className="text-white text-xs mr-1">{getStatusIcon()}</Text>
        <Text className="text-white text-xs font-medium">
          {getStatusText()}
        </Text>
      </Pressable>
    );
  }

  // Vue détaillée
  return (
    <View className={`${position === 'top' ? 'mb-4' : 'mt-4'}`}>
      {/* Indicateur principal */}
      <Pressable
        onPress={handlePress}
        className="flex-row items-center justify-between p-3 bg-muted/30 rounded-lg border border-border"
        {...(Platform.OS === "web" ? { style: { cursor: "pointer" } } : {})}
      >
        <View className="flex-row items-center flex-1">
          <View className={`w-3 h-3 rounded-full mr-3 ${getStatusColor()}`} />
          <View className="flex-1">
            <Text className="font-medium">{getStatusText()}</Text>
            <Text className="text-xs text-muted-foreground">
              {networkState.type} • Force: {networkState.strength}%
            </Text>
          </View>
        </View>
        
        <View className="flex-row items-center gap-2">
          {queueStats.pendingActions > 0 && (
            <Badge variant="secondary">
              <Text className="text-xs">{queueStats.pendingActions}</Text>
            </Badge>
          )}
          <Text className="text-muted-foreground">
            {showDetailedView ? "▼" : "▶"}
          </Text>
        </View>
      </Pressable>

      {/* Vue détaillée expandable */}
      {showDetailedView && (
        <Card className="mt-2">
          <CardContent className="p-4">
            <View className="gap-3">
              {/* Statut réseau */}
              <View>
                <Text className="font-semibold mb-2">📡 Réseau</Text>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-muted-foreground">Type:</Text>
                  <Text className="text-sm">{networkState.type}</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-muted-foreground">Force:</Text>
                  <Text className="text-sm">{networkState.strength}%</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-muted-foreground">Internet:</Text>
                  <Text className="text-sm">
                    {networkState.isInternetReachable ? "✅ Accessible" : "❌ Inaccessible"}
                  </Text>
                </View>
              </View>

              {/* Statut synchronisation */}
              <View>
                <Text className="font-semibold mb-2">🔄 Synchronisation</Text>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-muted-foreground">Statut:</Text>
                  <Text className="text-sm">
                    {syncStatus.isSync ? "En cours..." : "Inactive"}
                  </Text>
                </View>
                {syncStatus.lastSync && (
                  <View className="flex-row justify-between">
                    <Text className="text-sm text-muted-foreground">Dernière:</Text>
                    <Text className="text-sm">
                      {syncStatus.lastSync.toLocaleTimeString()}
                    </Text>
                  </View>
                )}
                {syncStatus.nextSync && (
                  <View className="flex-row justify-between">
                    <Text className="text-sm text-muted-foreground">Prochaine:</Text>
                    <Text className="text-sm">
                      {syncStatus.nextSync.toLocaleTimeString()}
                    </Text>
                  </View>
                )}
              </View>

              {/* Queue hors-ligne */}
              <View>
                <Text className="font-semibold mb-2">⏳ Actions en attente</Text>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-muted-foreground">En attente:</Text>
                  <Text className="text-sm">{queueStats.pendingActions}</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-muted-foreground">Échecs:</Text>
                  <Text className="text-sm">{queueStats.failedActions}</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-muted-foreground">Total:</Text>
                  <Text className="text-sm">{queueStats.totalActions}</Text>
                </View>
              </View>

              {/* Actions */}
              <View className="flex-row gap-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onPress={handleForceSync}
                  disabled={!networkState.isConnected || syncStatus.isSync}
                  className="flex-1"
                >
                  <Text className="text-xs">
                    {syncStatus.isSync ? "Synchronisation..." : "Forcer sync"}
                  </Text>
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onPress={() => networkService.refresh()}
                  className="flex-1"
                >
                  <Text className="text-xs">Actualiser</Text>
                </Button>
              </View>

              {/* Conseils selon le statut */}
              {!networkState.isConnected && (
                <View className="p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
                  <Text className="text-xs text-red-700 dark:text-red-300">
                    💡 Mode hors-ligne activé. Vos modifications seront synchronisées dès le retour du réseau.
                  </Text>
                </View>
              )}

              {networkState.strength < 30 && networkState.isConnected && (
                <View className="p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
                  <Text className="text-xs text-yellow-700 dark:text-yellow-300">
                    💡 Connexion faible détectée. La synchronisation peut être plus lente.
                  </Text>
                </View>
              )}

              {queueStats.pendingActions > 0 && networkState.isConnected && (
                <View className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                  <Text className="text-xs text-blue-700 dark:text-blue-300">
                    💡 {queueStats.pendingActions} action(s) en cours de synchronisation.
                  </Text>
                </View>
              )}

              {queueStats.failedActions > 0 && (
                <View className="p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
                  <Text className="text-xs text-red-700 dark:text-red-300">
                    ⚠️ {queueStats.failedActions} action(s) ont échoué. Vérifiez votre connexion.
                  </Text>
                </View>
              )}
            </View>
          </CardContent>
        </Card>
      )}
    </View>
  );
}
