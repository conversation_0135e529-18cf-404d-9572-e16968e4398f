import React, { useState, useEffect } from "react";
import { View, Platform, Alert, Linking } from "react-native";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { useRouter } from "expo-router";
import { showToast } from "~/lib/toastService";

// Import conditionnel pour éviter les erreurs sur les plateformes non supportées
let Camera: any = null;
let BarCodeScanner: any = null;

try {
  if (Platform.OS !== "web") {
    const ExpoCamera = require("expo-camera");
    const ExpoBarCodeScanner = require("expo-barcode-scanner");
    Camera = ExpoCamera.Camera || ExpoCamera.CameraView;
    BarCodeScanner = ExpoBarCodeScanner.BarCodeScanner;
  }
} catch (error) {
  console.warn("Camera libraries not available:", error);
}

interface QRCodeScannerProps {
  onScan: (data: string) => void;
  onClose: () => void;
  title?: string;
  description?: string;
}

export function QRCodeScanner({
  onScan,
  onClose,
  title = "Scanner un QR Code",
  description = "Pointez votre caméra vers le QR code de l'événement",
}: QRCodeScannerProps) {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const router = useRouter();

  useEffect(() => {
    getCameraPermissions();
  }, []);

  const getCameraPermissions = async () => {
    if (Platform.OS === "web") {
      // Sur web, on ne peut pas utiliser la caméra facilement
      setHasPermission(false);
      return;
    }

    try {
      if (Camera && BarCodeScanner) {
        const { status } = await Camera.requestCameraPermissionsAsync();
        setHasPermission(status === "granted");
      } else {
        setHasPermission(false);
      }
    } catch (error) {
      console.error("Error requesting camera permissions:", error);
      setHasPermission(false);
    }
  };

  const handleBarCodeScanned = ({ type, data }: { type: string; data: string }) => {
    if (scanned) return;
    
    setScanned(true);
    
    // Vérifier si c'est un lien valide vers un événement
    if (isValidEventUrl(data)) {
      onScan(data);
      showToast("QR Code scanné avec succès !", { type: "success" });
    } else {
      showToast("QR Code non reconnu comme un lien d'événement", { type: "error" });
      setScanned(false);
    }
  };

  const isValidEventUrl = (url: string): boolean => {
    try {
      // Vérifier si c'est une URL valide contenant /join/
      const urlPattern = /\/join\/\d+/;
      return urlPattern.test(url) || /^https?:\/\/.*\/join\/\d+/.test(url);
    } catch {
      return false;
    }
  };

  const handleManualEntry = () => {
    onClose();
    router.push("/join-event");
  };

  const handleOpenSettings = () => {
    Alert.alert(
      "Permission requise",
      "L'accès à la caméra est nécessaire pour scanner les QR codes. Voulez-vous ouvrir les paramètres ?",
      [
        { text: "Annuler", style: "cancel" },
        { text: "Paramètres", onPress: () => Linking.openSettings() },
      ]
    );
  };

  // Interface pour le web ou quand la caméra n'est pas disponible
  if (Platform.OS === "web" || !Camera || !BarCodeScanner) {
    return (
      <View className="flex-1 bg-background">
        <Card className="m-4">
          <CardContent className="items-center p-6">
            <Text className="text-6xl mb-4">📱</Text>
            <Text className="text-xl font-semibold mb-2 text-center">
              Scanner non disponible
            </Text>
            <Text className="text-muted-foreground text-center mb-6">
              Le scanner de QR code n'est pas disponible sur cette plateforme.
              Vous pouvez saisir manuellement le lien de l'événement.
            </Text>
            <View className="flex-row gap-3">
              <Button variant="outline" onPress={onClose}>
                <Text>Retour</Text>
              </Button>
              <Button onPress={handleManualEntry}>
                <Text>Saisie manuelle</Text>
              </Button>
            </View>
          </CardContent>
        </Card>
      </View>
    );
  }

  // Demande de permission
  if (hasPermission === null) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <Text className="text-muted-foreground">
          Demande d'autorisation pour la caméra...
        </Text>
      </View>
    );
  }

  // Permission refusée
  if (hasPermission === false) {
    return (
      <View className="flex-1 bg-background">
        <Card className="m-4">
          <CardContent className="items-center p-6">
            <Text className="text-6xl mb-4">📷</Text>
            <Text className="text-xl font-semibold mb-2 text-center">
              Accès à la caméra requis
            </Text>
            <Text className="text-muted-foreground text-center mb-6">
              Pour scanner les QR codes, nous avons besoin d'accéder à votre caméra.
            </Text>
            <View className="flex-row gap-3">
              <Button variant="outline" onPress={onClose}>
                <Text>Retour</Text>
              </Button>
              <Button onPress={handleOpenSettings}>
                <Text>Autoriser</Text>
              </Button>
            </View>
            <Button 
              variant="ghost" 
              onPress={handleManualEntry}
              className="mt-4"
            >
              <Text>Ou saisir manuellement le lien</Text>
            </Button>
          </CardContent>
        </Card>
      </View>
    );
  }

  // Interface de scan
  return (
    <View className="flex-1 bg-black">
      {/* Header */}
      <View className="absolute top-0 left-0 right-0 z-10 bg-black/50 p-4 pt-12">
        <View className="flex-row items-center justify-between">
          <Button variant="ghost" onPress={onClose} className="p-2">
            <Text className="text-white text-lg">✕</Text>
          </Button>
          <View className="flex-1 items-center">
            <Text className="text-white font-semibold">{title}</Text>
          </View>
          <View className="w-10" />
        </View>
      </View>

      {/* Caméra */}
      <Camera
        style={{ flex: 1 }}
        type={Camera.Constants?.Type?.back || "back"}
        onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
        barCodeScannerSettings={{
          barCodeTypes: [BarCodeScanner.Constants?.BarCodeType?.qr || "qr"],
        }}
      />

      {/* Overlay de scan */}
      <View className="absolute inset-0 items-center justify-center">
        <View className="w-64 h-64 border-2 border-white rounded-lg" />
        <Text className="text-white text-center mt-4 px-4">
          {description}
        </Text>
      </View>

      {/* Footer */}
      <View className="absolute bottom-0 left-0 right-0 bg-black/50 p-4 pb-8">
        <View className="flex-row justify-center gap-4">
          {scanned && (
            <Button 
              variant="outline" 
              onPress={() => setScanned(false)}
              className="bg-white/20 border-white/30"
            >
              <Text className="text-white">Scanner à nouveau</Text>
            </Button>
          )}
          <Button 
            variant="outline" 
            onPress={handleManualEntry}
            className="bg-white/20 border-white/30"
          >
            <Text className="text-white">Saisie manuelle</Text>
          </Button>
        </View>
      </View>
    </View>
  );
}

// Hook pour utiliser le scanner de QR code
export function useQRCodeScanner() {
  const [isScanning, setIsScanning] = useState(false);
  const router = useRouter();

  const startScanning = (onScan?: (data: string) => void) => {
    setIsScanning(true);
    // Ici on pourrait ouvrir un modal ou naviguer vers une page de scan
  };

  const stopScanning = () => {
    setIsScanning(false);
  };

  const handleScan = (data: string) => {
    try {
      // Extraire l'ID de l'événement depuis l'URL
      const match = data.match(/\/join\/(\d+)/);
      if (match) {
        const eventId = match[1];
        router.push(`/join/${eventId}`);
        stopScanning();
      } else {
        showToast("Lien d'événement non valide", { type: "error" });
      }
    } catch (error) {
      console.error("Error processing scanned data:", error);
      showToast("Erreur lors du traitement du QR code", { type: "error" });
    }
  };

  return {
    isScanning,
    startScanning,
    stopScanning,
    handleScan,
  };
}
