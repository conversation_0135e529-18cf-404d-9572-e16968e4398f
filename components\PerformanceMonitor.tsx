import React, { useState, useEffect } from 'react';
import { View, ScrollView, Platform } from 'react-native';
import { Text } from '~/components/ui/text';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Separator } from '~/components/ui/separator';
import { errorHandler, ErrorType, ErrorSeverity } from '~/lib/errorHandler';
import { cacheManager } from '~/lib/cache';
import { usePerformanceStats } from '~/hooks/useOptimizedData';

interface PerformanceMonitorProps {
  visible?: boolean;
  onClose?: () => void;
}

export function PerformanceMonitor({ visible = false, onClose }: PerformanceMonitorProps) {
  const [errorStats, setErrorStats] = useState(errorHandler.getErrorStats());
  const [cacheStats, setCacheStats] = useState(cacheManager.getStats());
  const [recentErrors, setRecentErrors] = useState(errorHandler.getRecentErrors(5));
  const performanceStats = usePerformanceStats();

  useEffect(() => {
    if (!visible) return;

    const interval = setInterval(() => {
      setErrorStats(errorHandler.getErrorStats());
      setCacheStats(cacheManager.getStats());
      setRecentErrors(errorHandler.getRecentErrors(5));
    }, 2000);

    return () => clearInterval(interval);
  }, [visible]);

  const clearErrorLog = () => {
    errorHandler.clearErrorLog();
    setErrorStats(errorHandler.getErrorStats());
    setRecentErrors([]);
  };

  const clearCache = async () => {
    await cacheManager.clear();
    setCacheStats(cacheManager.getStats());
  };

  if (!visible) return null;

  return (
    <View className="absolute inset-0 bg-background/95 z-50">
      <ScrollView className="flex-1 p-4">
        <View className="gap-4">
          {/* Header */}
          <View className="flex-row items-center justify-between">
            <Text className="text-2xl font-bold">Performance Monitor</Text>
            {onClose && (
              <Button onPress={onClose} variant="outline" size="sm">
                <Text>✕ Fermer</Text>
              </Button>
            )}
          </View>

          {/* Statistiques du Cache */}
          <Card>
            <CardHeader>
              <CardTitle className="flex-row items-center">
                <Text className="text-lg">📊 Statistiques du Cache</Text>
              </CardTitle>
            </CardHeader>
            <CardContent className="gap-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Taux de réussite</Text>
                <Text className="font-semibold text-green-600">
                  {cacheStats.hitRate.toFixed(1)}%
                </Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Cache hits</Text>
                <Text className="font-semibold">{cacheStats.hits}</Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Cache misses</Text>
                <Text className="font-semibold">{cacheStats.misses}</Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Entrées en mémoire</Text>
                <Text className="font-semibold">{cacheStats.memorySize}</Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Évictions</Text>
                <Text className="font-semibold">{cacheStats.evictions}</Text>
              </View>

              <Button onPress={clearCache} variant="outline" className="mt-2">
                <Text>🗑️ Vider le cache</Text>
              </Button>
            </CardContent>
          </Card>

          {/* Statistiques d'Erreurs */}
          <Card>
            <CardHeader>
              <CardTitle className="flex-row items-center justify-between">
                <Text className="text-lg">⚠️ Statistiques d'Erreurs</Text>
                <Text className="text-sm text-muted-foreground">
                  {errorStats.recentCount} récentes
                </Text>
              </CardTitle>
            </CardHeader>
            <CardContent className="gap-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Total d'erreurs</Text>
                <Text className="font-semibold">{errorStats.total}</Text>
              </View>

              <Separator />

              <Text className="font-medium">Par type :</Text>
              {Object.entries(errorStats.byType).map(([type, count]) => (
                <View key={type} className="flex-row justify-between items-center ml-4">
                  <Text className="text-muted-foreground">{type}</Text>
                  <Text className="font-semibold">{count}</Text>
                </View>
              ))}

              <Separator />

              <Text className="font-medium">Par sévérité :</Text>
              {Object.entries(errorStats.bySeverity).map(([severity, count]) => (
                <View key={severity} className="flex-row justify-between items-center ml-4">
                  <Text className="text-muted-foreground">{severity}</Text>
                  <Text className={`font-semibold ${
                    severity === ErrorSeverity.CRITICAL ? 'text-red-600' :
                    severity === ErrorSeverity.HIGH ? 'text-orange-600' :
                    severity === ErrorSeverity.MEDIUM ? 'text-yellow-600' :
                    'text-blue-600'
                  }`}>
                    {count}
                  </Text>
                </View>
              ))}

              <Button onPress={clearErrorLog} variant="outline" className="mt-2">
                <Text>🗑️ Vider le log d'erreurs</Text>
              </Button>
            </CardContent>
          </Card>

          {/* Erreurs Récentes */}
          {recentErrors.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>
                  <Text className="text-lg">🔍 Erreurs Récentes</Text>
                </CardTitle>
              </CardHeader>
              <CardContent className="gap-3">
                {recentErrors.map((error, index) => (
                  <View key={index} className="p-3 bg-muted/50 rounded-lg">
                    <View className="flex-row justify-between items-start mb-2">
                      <Text className="font-medium text-sm">{error.type}</Text>
                      <Text className="text-xs text-muted-foreground">
                        {new Date(error.timestamp).toLocaleTimeString()}
                      </Text>
                    </View>
                    
                    <Text className="text-sm text-muted-foreground mb-1">
                      {error.message}
                    </Text>
                    
                    <View className="flex-row items-center gap-2">
                      <View className={`px-2 py-1 rounded-full ${
                        error.severity === ErrorSeverity.CRITICAL ? 'bg-red-100' :
                        error.severity === ErrorSeverity.HIGH ? 'bg-orange-100' :
                        error.severity === ErrorSeverity.MEDIUM ? 'bg-yellow-100' :
                        'bg-blue-100'
                      }`}>
                        <Text className={`text-xs ${
                          error.severity === ErrorSeverity.CRITICAL ? 'text-red-800' :
                          error.severity === ErrorSeverity.HIGH ? 'text-orange-800' :
                          error.severity === ErrorSeverity.MEDIUM ? 'text-yellow-800' :
                          'text-blue-800'
                        }`}>
                          {error.severity}
                        </Text>
                      </View>
                      
                      {error.retryable && (
                        <View className="px-2 py-1 rounded-full bg-green-100">
                          <Text className="text-xs text-green-800">RETRYABLE</Text>
                        </View>
                      )}
                    </View>
                  </View>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Informations Système */}
          <Card>
            <CardHeader>
              <CardTitle>
                <Text className="text-lg">💻 Informations Système</Text>
              </CardTitle>
            </CardHeader>
            <CardContent className="gap-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Plateforme</Text>
                <Text className="font-semibold">{Platform.OS}</Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Version</Text>
                <Text className="font-semibold">{Platform.Version}</Text>
              </View>
              
              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Temps de fonctionnement</Text>
                <Text className="font-semibold">
                  {Math.floor(Date.now() / 1000 / 60)} min
                </Text>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-muted-foreground">Mémoire utilisée</Text>
                <Text className="font-semibold">
                  {Platform.OS === 'web' ? 'N/A' : 'Estimée'}
                </Text>
              </View>
            </CardContent>
          </Card>

          {/* Actions de Debug */}
          <Card>
            <CardHeader>
              <CardTitle>
                <Text className="text-lg">🔧 Actions de Debug</Text>
              </CardTitle>
            </CardHeader>
            <CardContent className="gap-3">
              <Button 
                onPress={() => {
                  // Simuler une erreur pour tester
                  errorHandler.handle(new Error('Erreur de test simulée'));
                }}
                variant="outline"
              >
                <Text>🧪 Simuler une erreur</Text>
              </Button>
              
              <Button 
                onPress={() => {
                  console.log('Cache Stats:', cacheStats);
                  console.log('Error Stats:', errorStats);
                  console.log('Recent Errors:', recentErrors);
                }}
                variant="outline"
              >
                <Text>📋 Logger les statistiques</Text>
              </Button>
            </CardContent>
          </Card>
        </View>
      </ScrollView>
    </View>
  );
}
