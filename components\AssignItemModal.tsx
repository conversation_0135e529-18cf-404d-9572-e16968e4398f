import React, { useState, useEffect } from "react";
import { View, Modal, ActivityIndicator, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { RobustSelect } from "~/components/ui/robust-select";
import { fetchParticipantsForEvent, updateItem } from "~/lib/supabaseCrud";
import { showToast } from "~/lib/toastService";
import { Participant } from "~/lib/types";

interface AssignItemModalProps {
  visible: boolean;
  onClose: () => void;
  eventId: number;
  itemId: number;
  itemName: string;
  currentAssignedParticipantId?: number | null;
  onAssignmentChanged: () => void;
}

export function AssignItemModal({
  visible,
  onClose,
  eventId,
  itemId,
  itemName,
  currentAssignedParticipantId,
  onAssignmentChanged,
}: AssignItemModalProps) {
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [assigning, setAssigning] = useState(false);
  const [selectedParticipantId, setSelectedParticipantId] = useState<number | null>(
    currentAssignedParticipantId || null
  );

  const loadParticipants = async () => {
    try {
      setLoading(true);
      const eventParticipants = await fetchParticipantsForEvent(eventId);
      // Filtrer pour ne garder que les participants acceptés
      const acceptedParticipants = eventParticipants?.filter(
        (p) => p.status === "accepted"
      ) || [];
      setParticipants(acceptedParticipants);
    } catch (error) {
      console.error("Error loading participants:", error);
      showToast("Erreur lors du chargement des participants.", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      loadParticipants();
      setSelectedParticipantId(currentAssignedParticipantId || null);
    }
  }, [visible, currentAssignedParticipantId]);

  const handleAssign = async () => {
    try {
      setAssigning(true);
      
      const updateData = {
        assigned_participant_id: selectedParticipantId,
      };

      const result = await updateItem(itemId, updateData);
      
      if (result) {
        const participantName = selectedParticipantId 
          ? participants.find(p => p.id === selectedParticipantId)?.name_display || "Participant"
          : null;
          
        if (selectedParticipantId) {
          showToast(`Item "${itemName}" assigné à ${participantName} !`, { type: "success" });
        } else {
          showToast(`Assignation de "${itemName}" supprimée !`, { type: "success" });
        }
        
        onAssignmentChanged();
        onClose();
      } else {
        throw new Error("Échec de l'assignation");
      }
    } catch (error) {
      console.error("Error assigning item:", error);
      showToast("Erreur lors de l'assignation.", { type: "error" });
    } finally {
      setAssigning(false);
    }
  };

  const participantOptions = [
    { label: "Aucun participant", value: null },
    ...participants.map((participant) => ({
      label: participant.name_display || participant.anonymous_email || "Participant inconnu",
      value: participant.id,
    })),
  ];

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 justify-center items-center bg-black/50">
        <View 
          className="bg-card rounded-lg p-6 mx-4 w-full"
          style={{ 
            maxWidth: Platform.OS === "web" ? 400 : "90%",
            maxHeight: Platform.OS === "web" ? "80vh" : "80%"
          }}
        >
          <Card className="border-0 shadow-none">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">Assigner l'item</CardTitle>
              <Text className="text-muted-foreground">
                Choisissez un participant pour l'item "{itemName}"
              </Text>
            </CardHeader>
            
            <CardContent className="gap-4">
              {loading ? (
                <View className="py-8 items-center">
                  <ActivityIndicator size="large" />
                  <Text className="mt-2 text-muted-foreground">
                    Chargement des participants...
                  </Text>
                </View>
              ) : (
                <>
                  <View>
                    <Text className="text-sm font-medium mb-2">Participant</Text>
                    <RobustSelect
                      options={participantOptions}
                      value={selectedParticipantId}
                      onValueChange={setSelectedParticipantId}
                      placeholder="Sélectionner un participant"
                    />
                  </View>
                  
                  <View className="flex-row justify-end gap-3 mt-4">
                    <Button variant="outline" onPress={onClose} disabled={assigning}>
                      <Text>Annuler</Text>
                    </Button>
                    <Button onPress={handleAssign} disabled={assigning}>
                      <Text className="text-primary-foreground">
                        {assigning ? "Assignation..." : "Assigner"}
                      </Text>
                    </Button>
                  </View>
                </>
              )}
            </CardContent>
          </Card>
        </View>
      </View>
    </Modal>
  );
}
