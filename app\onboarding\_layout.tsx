import { Stack } from "expo-router";

export default function OnboardingLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        gestureEnabled: false, // Empêcher le retour par geste
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen 
        name="welcome" 
        options={{
          title: "Bienvenue",
        }}
      />
      <Stack.Screen 
        name="profile" 
        options={{
          title: "Profil",
        }}
      />
      <Stack.Screen 
        name="tutorial" 
        options={{
          title: "Tutoriel",
        }}
      />
      <Stack.Screen 
        name="completion" 
        options={{
          title: "<PERSON><PERSON><PERSON><PERSON>",
        }}
      />
    </Stack>
  );
}
