/**
 * Service de gestion des challenges communautaires pour Party Organizer
 * Gère les défis temporaires et les compétitions entre utilisateurs
 */

import { supabase } from './supabase';
import { cache } from './cacheService';
import { showToast } from './toastService';
import { achievements } from './achievementsService';

export interface CommunityChallenge {
  id: number;
  name: string;
  title: string;
  description: string;
  icon: string;
  challenge_type: 'individual' | 'community' | 'competitive';
  start_date: string;
  end_date: string;
  target_metric: string;
  target_value: number;
  reward_points: number;
  reward_badge_id?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ChallengeParticipation {
  id: number;
  challenge_id: number;
  user_id: string;
  current_progress: number;
  is_completed: boolean;
  completed_at?: string;
  joined_at: string;
  challenge?: CommunityChallenge;
}

export interface ChallengeLeaderboard {
  user_id: string;
  username?: string;
  progress: number;
  rank: number;
  is_completed: boolean;
}

export interface SeasonalEvent {
  id: number;
  name: string;
  title: string;
  description: string;
  icon: string;
  start_date: string;
  end_date: string;
  special_badges: number[];
  bonus_multiplier: number;
  theme_data: Record<string, any>;
  is_active: boolean;
}

class ChallengesService {
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Récupère tous les challenges actifs
   */
  async getActiveChallenges(): Promise<CommunityChallenge[]> {
    try {
      const cacheKey = 'active_challenges';
      const cached = await cache.get<CommunityChallenge[]>(cacheKey);
      
      if (cached) {
        return cached;
      }

      const now = new Date().toISOString();
      const { data, error } = await supabase
        .from('community_challenges')
        .select('*')
        .eq('is_active', true)
        .lte('start_date', now)
        .gte('end_date', now)
        .order('end_date', { ascending: true });

      if (error) throw error;

      const challenges = data || [];
      await cache.set(cacheKey, challenges, {}, this.CACHE_TTL);
      
      return challenges;
    } catch (error) {
      console.error('Error fetching active challenges:', error);
      return [];
    }
  }

  /**
   * Récupère les participations d'un utilisateur
   */
  async getUserParticipations(userId: string): Promise<ChallengeParticipation[]> {
    try {
      const cacheKey = 'user_challenge_participations';
      const cached = await cache.get<ChallengeParticipation[]>(cacheKey, { userId });
      
      if (cached) {
        return cached;
      }

      const { data, error } = await supabase
        .from('challenge_participations')
        .select(`
          *,
          challenge:community_challenges(*)
        `)
        .eq('user_id', userId)
        .order('joined_at', { ascending: false });

      if (error) throw error;

      const participations = data || [];
      await cache.set(cacheKey, participations, { userId }, this.CACHE_TTL);
      
      return participations;
    } catch (error) {
      console.error('Error fetching user participations:', error);
      return [];
    }
  }

  /**
   * Rejoint un challenge
   */
  async joinChallenge(userId: string, challengeId: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('challenge_participations')
        .insert({
          challenge_id: challengeId,
          user_id: userId,
          current_progress: 0,
        });

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          showToast('Vous participez déjà à ce challenge !', { type: 'info' });
          return false;
        }
        throw error;
      }

      // Invalider le cache
      await cache.delete('user_challenge_participations', { userId });

      showToast('Challenge rejoint avec succès !', { type: 'success' });
      return true;
    } catch (error) {
      console.error('Error joining challenge:', error);
      showToast('Erreur lors de la participation au challenge', { type: 'error' });
      return false;
    }
  }

  /**
   * Met à jour la progression d'un challenge
   */
  async updateChallengeProgress(
    userId: string, 
    metric: string, 
    increment: number = 1
  ): Promise<void> {
    try {
      const { error } = await supabase.rpc('update_challenge_progress', {
        p_user_id: userId,
        p_metric: metric,
        p_increment: increment,
      });

      if (error) throw error;

      // Invalider le cache
      await cache.delete('user_challenge_participations', { userId });

      // Vérifier les challenges complétés
      await this.checkCompletedChallenges(userId);
    } catch (error) {
      console.error('Error updating challenge progress:', error);
    }
  }

  /**
   * Vérifie les challenges complétés et récompense
   */
  private async checkCompletedChallenges(userId: string): Promise<void> {
    try {
      const participations = await this.getUserParticipations(userId);
      
      for (const participation of participations) {
        if (!participation.is_completed && 
            participation.challenge && 
            participation.current_progress >= participation.challenge.target_value) {
          
          await this.rewardChallengeCompletion(userId, participation.challenge);
        }
      }
    } catch (error) {
      console.error('Error checking completed challenges:', error);
    }
  }

  /**
   * Récompense la completion d'un challenge
   */
  private async rewardChallengeCompletion(
    userId: string, 
    challenge: CommunityChallenge
  ): Promise<void> {
    try {
      // Ajouter les points
      if (challenge.reward_points > 0) {
        await achievements.updateStats(userId, {
          // Points ajoutés via le système d'achievements
        });
      }

      // Attribuer le badge de récompense
      if (challenge.reward_badge_id) {
        await supabase
          .from('user_achievements')
          .upsert({
            user_id: userId,
            badge_id: challenge.reward_badge_id,
            is_completed: true,
            completion_data: {
              challenge_id: challenge.id,
              challenge_name: challenge.name,
              completed_at: new Date().toISOString(),
            },
          });
      }

      showToast(
        `🏆 Challenge "${challenge.title}" terminé ! +${challenge.reward_points} points`,
        { type: 'success', duration: 5000 }
      );
    } catch (error) {
      console.error('Error rewarding challenge completion:', error);
    }
  }

  /**
   * Récupère le classement d'un challenge
   */
  async getChallengeLeaderboard(challengeId: number, limit: number = 50): Promise<ChallengeLeaderboard[]> {
    try {
      const cacheKey = 'challenge_leaderboard';
      const cached = await cache.get<ChallengeLeaderboard[]>(cacheKey, { challengeId, limit });
      
      if (cached) {
        return cached;
      }

      const { data, error } = await supabase
        .from('challenge_participations')
        .select(`
          user_id,
          current_progress,
          is_completed
        `)
        .eq('challenge_id', challengeId)
        .order('current_progress', { ascending: false })
        .order('completed_at', { ascending: true })
        .limit(limit);

      if (error) throw error;

      const leaderboard = (data || []).map((entry, index) => ({
        user_id: entry.user_id,
        progress: entry.current_progress,
        rank: index + 1,
        is_completed: entry.is_completed,
      }));

      await cache.set(cacheKey, leaderboard, { challengeId, limit }, this.CACHE_TTL);
      
      return leaderboard;
    } catch (error) {
      console.error('Error fetching challenge leaderboard:', error);
      return [];
    }
  }

  /**
   * Crée un nouveau challenge (admin)
   */
  async createChallenge(challengeData: Omit<CommunityChallenge, 'id' | 'created_at' | 'updated_at'>): Promise<CommunityChallenge | null> {
    try {
      const { data, error } = await supabase
        .from('community_challenges')
        .insert(challengeData)
        .select()
        .single();

      if (error) throw error;

      // Invalider le cache
      await cache.invalidate('active_challenges');

      return data;
    } catch (error) {
      console.error('Error creating challenge:', error);
      return null;
    }
  }

  /**
   * Récupère les événements saisonniers actifs
   */
  async getActiveSeasonalEvents(): Promise<SeasonalEvent[]> {
    try {
      const cacheKey = 'active_seasonal_events';
      const cached = await cache.get<SeasonalEvent[]>(cacheKey);
      
      if (cached) {
        return cached;
      }

      const now = new Date().toISOString();
      const { data, error } = await supabase
        .from('seasonal_events')
        .select('*')
        .eq('is_active', true)
        .lte('start_date', now)
        .gte('end_date', now)
        .order('end_date', { ascending: true });

      if (error) throw error;

      const events = data || [];
      await cache.set(cacheKey, events, {}, this.CACHE_TTL);
      
      return events;
    } catch (error) {
      console.error('Error fetching seasonal events:', error);
      return [];
    }
  }

  /**
   * Génère des challenges automatiques
   */
  async generateWeeklyChallenges(): Promise<void> {
    try {
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay()); // Dimanche
      weekStart.setHours(0, 0, 0, 0);
      
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6); // Samedi
      weekEnd.setHours(23, 59, 59, 999);

      const challenges = [
        {
          name: `weekly_organizer_${weekStart.getTime()}`,
          title: 'Organisateur de la Semaine',
          description: 'Organisez 3 événements cette semaine',
          icon: '🎯',
          challenge_type: 'individual' as const,
          start_date: weekStart.toISOString(),
          end_date: weekEnd.toISOString(),
          target_metric: 'events_created',
          target_value: 3,
          reward_points: 200,
          is_active: true,
        },
        {
          name: `weekly_social_${weekStart.getTime()}`,
          title: 'Papillon Social',
          description: 'Participez à 5 événements cette semaine',
          icon: '🦋',
          challenge_type: 'individual' as const,
          start_date: weekStart.toISOString(),
          end_date: weekEnd.toISOString(),
          target_metric: 'events_participated',
          target_value: 5,
          reward_points: 150,
          is_active: true,
        },
        {
          name: `weekly_community_${weekStart.getTime()}`,
          title: 'Bâtisseur de Communauté',
          description: 'Invitez 20 participants cette semaine',
          icon: '🌐',
          challenge_type: 'competitive' as const,
          start_date: weekStart.toISOString(),
          end_date: weekEnd.toISOString(),
          target_metric: 'participants_invited',
          target_value: 20,
          reward_points: 300,
          is_active: true,
        },
      ];

      for (const challenge of challenges) {
        // Vérifier si le challenge n'existe pas déjà
        const { data: existing } = await supabase
          .from('community_challenges')
          .select('id')
          .eq('name', challenge.name)
          .single();

        if (!existing) {
          await this.createChallenge(challenge);
        }
      }
    } catch (error) {
      console.error('Error generating weekly challenges:', error);
    }
  }

  /**
   * Calcule les statistiques de participation aux challenges
   */
  calculateChallengeStats(participations: ChallengeParticipation[]): {
    totalChallenges: number;
    completedChallenges: number;
    activeChallenges: number;
    completionRate: number;
    totalRewardPoints: number;
  } {
    const totalChallenges = participations.length;
    const completedChallenges = participations.filter(p => p.is_completed).length;
    const activeChallenges = participations.filter(p => !p.is_completed && p.challenge?.is_active).length;
    const completionRate = totalChallenges > 0 ? (completedChallenges / totalChallenges) * 100 : 0;
    const totalRewardPoints = participations
      .filter(p => p.is_completed)
      .reduce((sum, p) => sum + (p.challenge?.reward_points || 0), 0);

    return {
      totalChallenges,
      completedChallenges,
      activeChallenges,
      completionRate,
      totalRewardPoints,
    };
  }
}

// Instance singleton
export const challengesService = new ChallengesService();

// Fonctions utilitaires
export const challenges = {
  getActive: () => challengesService.getActiveChallenges(),
  getUserParticipations: (userId: string) => challengesService.getUserParticipations(userId),
  join: (userId: string, challengeId: number) => challengesService.joinChallenge(userId, challengeId),
  updateProgress: (userId: string, metric: string, increment?: number) => 
    challengesService.updateChallengeProgress(userId, metric, increment),
  getLeaderboard: (challengeId: number, limit?: number) => 
    challengesService.getChallengeLeaderboard(challengeId, limit),
  create: (challengeData: any) => challengesService.createChallenge(challengeData),
  getSeasonalEvents: () => challengesService.getActiveSeasonalEvents(),
  generateWeekly: () => challengesService.generateWeeklyChallenges(),
  calculateStats: (participations: ChallengeParticipation[]) => 
    challengesService.calculateChallengeStats(participations),
};
