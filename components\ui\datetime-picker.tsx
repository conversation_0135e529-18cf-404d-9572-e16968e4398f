import React, { useState } from "react";
import { View, Platform } from "react-native";
import { DatePicker } from "./date-picker";
import { TimePicker } from "./time-picker";
import { Label } from "./label";
import { Text } from "./text";

interface DateTimePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  className?: string;
  error?: boolean;
  errorMessage?: string;
  required?: boolean;
  label?: string;
}

export function DateTimePicker({
  value,
  onChange,
  minimumDate,
  maximumDate,
  className,
  error = false,
  errorMessage,
  required = false,
  label = "Date et Heure",
}: DateTimePickerProps) {
  const handleDateChange = (newDate: Date) => {
    // Conserver l'heure actuelle lors du changement de date
    const updatedDate = new Date(newDate);
    updatedDate.setHours(value.getHours(), value.getMinutes());
    onChange(updatedDate);
  };

  const handleTimeChange = (newTime: Date) => {
    // Conserver la date actuelle lors du changement d'heure
    const updatedDate = new Date(value);
    updatedDate.setHours(newTime.getHours(), newTime.getMinutes());
    onChange(updatedDate);
  };

  const ErrorMessage = ({ message }: { message?: string }) => {
    if (!message) return null;
    return <Text className="text-sm text-destructive mt-1">{message}</Text>;
  };

  return (
    <View className={className}>
      <Label className="flex-row mb-1.5">
        {required && <Text className="text-destructive mr-1">*</Text>}
        <Text className="font-medium text-foreground">{label}</Text>
      </Label>
      
      <View className={Platform.OS === "web" ? "flex-row gap-4" : "gap-4"}>
        <View className={Platform.OS === "web" ? "flex-1" : "mb-4"}>
          <Label className="mb-1.5 font-medium text-foreground">Date</Label>
          <DatePicker
            value={value}
            onChange={handleDateChange}
            minimumDate={minimumDate}
            maximumDate={maximumDate}
            error={error}
            placeholder="Sélectionner une date"
          />
        </View>
        
        <View className={Platform.OS === "web" ? "flex-1" : ""}>
          <Label className="mb-1.5 font-medium text-foreground">Heure</Label>
          <TimePicker
            value={value}
            onChange={handleTimeChange}
            error={error}
            placeholder="Sélectionner une heure"
          />
        </View>
      </View>
      
      <ErrorMessage message={errorMessage} />
    </View>
  );
}
