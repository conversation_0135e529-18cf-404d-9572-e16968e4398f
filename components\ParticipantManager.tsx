import React, { useState } from "react";
import { View, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Button } from "~/components/ui/button";
import { showToast } from "~/lib/toastService";

interface ParticipantManagerProps {
  participants: string[];
  onParticipantsChange: (participants: string[]) => void;
  currentUserName?: string;
  placeholder?: string;
  addButtonText?: string;
  className?: string;
}

export function ParticipantManager({
  participants,
  onParticipantsChange,
  currentUserName = "Moi",
  placeholder = "Nom du participant",
  addButtonText = "Ajouter",
  className = "",
}: ParticipantManagerProps) {
  const [newParticipantName, setNewParticipantName] = useState("");

  const addParticipant = () => {
    const name = newParticipantName.trim();

    if (!name) {
      showToast("Veuillez entrer un nom.", { type: "error" });
      return;
    }

    // Vérifier si le nom n'est pas déjà ajouté
    if (participants.includes(name)) {
      showToast("Ce participant est déjà ajouté.", { type: "error" });
      return;
    }

    // Vérifier si ce n'est pas le nom de l'utilisateur actuel
    if (name.toLowerCase() === currentUserName.toLowerCase()) {
      showToast("Vous êtes déjà inclus automatiquement.", { type: "error" });
      return;
    }

    try {
      onParticipantsChange([...participants, name]);
      setNewParticipantName("");
      showToast("Participant ajouté !", { type: "success" });
    } catch (error) {
      console.error("Error adding participant:", error);
      showToast("Erreur lors de l'ajout du participant.", { type: "error" });
    }
  };

  const removeParticipant = (nameToRemove: string) => {
    onParticipantsChange(participants.filter((name) => name !== nameToRemove));
  };

  return (
    <View className={className}>
      {/* Organisateur (utilisateur actuel) */}
      <View className="mb-4">
        <View className="flex-row items-center justify-between p-3 bg-primary/10 rounded-lg border border-primary/20">
          <Text className="flex-1 text-sm font-medium text-foreground">
            {currentUserName}
          </Text>
          <View className="bg-primary px-2 py-1 rounded">
            <Text className="text-primary-foreground text-xs font-medium">
              Moi
            </Text>
          </View>
        </View>
      </View>

      {/* Liste des participants ajoutés */}
      {participants.map((name, index) => (
        <View
          key={index}
          className="flex-row items-center justify-between p-3 bg-muted/50 rounded-lg mb-2"
        >
          <Text className="flex-1 text-sm text-foreground">{name}</Text>
          <Button
            variant="ghost"
            size="sm"
            onPress={() => removeParticipant(name)}
            className="ml-2 h-8 w-8 p-0"
          >
            <Text className="text-destructive text-lg">✕</Text>
          </Button>
        </View>
      ))}

      {/* Champ d'ajout de participant */}
      <View className="mb-4">
        <View className={Platform.OS === "web" ? "flex-row gap-2" : "gap-2"}>
          <View className={Platform.OS === "web" ? "flex-1" : "mb-2"}>
            <Input
              placeholder={placeholder}
              value={newParticipantName}
              onChangeText={setNewParticipantName}
              className="h-11 border-border bg-background text-foreground"
              onSubmitEditing={addParticipant}
            />
          </View>
          <Button onPress={addParticipant} className="h-11 px-4">
            <Text className="text-primary-foreground font-medium">
              {addButtonText}
            </Text>
          </Button>
        </View>
      </View>

      {/* Bouton "Ajouter un autre participant" */}
      <Button
        variant="ghost"
        onPress={() => {
          // Focus sur l'input si possible
          // Pour l'instant, on affiche juste un message d'encouragement
          if (participants.length === 0) {
            showToast("Ajoutez votre premier participant ci-dessus !", {
              type: "info",
            });
          }
        }}
        className="w-full h-12 border border-dashed border-primary/30"
      >
        <Text className="text-primary">+ Ajouter un autre participant</Text>
      </Button>

      {/* Texte d'aide */}
      <Text className="text-xs text-muted-foreground mt-3 text-center">
        💡 Les participants pourront choisir leur identité quand ils rejoindront
        l'événement
      </Text>
    </View>
  );
}
