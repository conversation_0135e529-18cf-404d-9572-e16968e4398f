// --- Begin app/(tabs)/tasks.tsx ---
import React, { useState, useEffect, useCallback } from "react";
import { View, FlatList, ActivityIndicator, Pressable } from "react-native";
import { Text } from "~/components/ui/text";
import { Checkbox } from "~/components/ui/checkbox";

import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button"; // Ajout Button pour retry
import { useAuth } from "~/lib/AuthContext";
import { fetchItemsAssignedToUser, updateItem } from "~/lib/supabaseCrud";
import { Item, CostEnum, EffortEnum, Event } from "~/lib/types"; // Ajout Event type
import { useFocusEffect } from "expo-router";
import { showToast } from "~/lib/toastService";
// Icônes temporairement supprimées pour compatibilité web
import { Separator } from "~/components/ui/separator";

interface GroupedTasks {
  [eventId: number]: {
    eventTitle: string;
    eventIcon?: string | null;
    tasks: Item[];
  };
}

export default function UserTasksScreen() {
  const { session } = useAuth();
  const [tasks, setTasks] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fonction de chargement des tâches
  const loadTasks = useCallback(async () => {
    if (!session?.user?.id) {
      setTasks([]);
      setLoading(false);
      setError(null);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const assignedItems = await fetchItemsAssignedToUser(session.user.id);
      setTasks(assignedItems || []);
    } catch (e: any) {
      console.error("Error loading user tasks:", e);
      if (
        e?.message?.includes("relationship") &&
        e?.message?.includes("does not exist")
      ) {
        setError(
          "Erreur de configuration (relation manquante). Veuillez vérifier la base de données."
        );
      } else {
        setError("Impossible de charger vos tâches.");
      }
      setTasks([]);
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id]);

  // Utilisation du pattern correct pour useFocusEffect avec async
  useFocusEffect(
    useCallback(() => {
      loadTasks(); // Appelle la fonction async
    }, [loadTasks]) // Dépendances du useCallback
  );

  // Grouper les tâches par événement
  const groupedTasks = tasks.reduce((acc, task) => {
    // Vérifier que l'événement est bien chargé (relation via fetchItemsAssignedToUser)
    const eventInfo = task.event as Event | undefined; // Cast avec vérification potentielle
    if (!eventInfo) {
      console.warn(`Task ${task.id} is missing event details.`);
      return acc;
    }

    const eventId = eventInfo.id;
    if (!acc[eventId]) {
      acc[eventId] = {
        eventTitle: eventInfo.title,
        eventIcon: eventInfo.icon,
        tasks: [],
      };
    }
    acc[eventId].tasks.push(task);
    return acc;
  }, {} as GroupedTasks);

  // Convertir l'objet groupé en tableau pour FlatList
  const taskSections = Object.entries(groupedTasks).map(([eventId, data]) => ({
    eventId: parseInt(eventId, 10),
    title: data.eventTitle,
    icon: data.eventIcon,
    data: data.tasks,
  }));

  const handleToggleCompleted = async (item: Item) => {
    try {
      const newCompletedStatus = !item.completed;
      const updatedItem = await updateItem(item.id, {
        completed: newCompletedStatus,
      });

      if (updatedItem) {
        // Mettre à jour l'état local
        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.id === item.id
              ? { ...task, completed: newCompletedStatus }
              : task
          )
        );

        const statusText = newCompletedStatus ? "terminée" : "non terminée";
        showToast(`Tâche marquée comme ${statusText}`, { type: "success" });
      } else {
        throw new Error("Échec de la mise à jour");
      }
    } catch (error) {
      console.error("Error updating task:", error);
      showToast("Erreur lors de la mise à jour", { type: "error" });
    }
  };

  const renderTaskItem = ({ item }: { item: Item }) => {
    return (
      <View
        className={`flex-row items-center py-3 px-2 space-x-3 border-b border-border/30 last:border-b-0 ${
          item.completed ? "opacity-60" : ""
        }`}
      >
        <Checkbox
          aria-label={`Marquer ${item.name} comme ${
            item.completed ? "non fait" : "fait"
          }`}
          checked={item.completed}
          onCheckedChange={() => handleToggleCompleted(item)}
        />
        <View className="flex-1">
          <Text
            className={`text-base ${
              item.completed
                ? "line-through text-muted-foreground"
                : "text-foreground"
            }`}
          >
            {item.name}
          </Text>
          <View className="flex-row gap-1.5 items-center mt-1">
            {item.estimated_cost && (
              <Text className="text-xs text-muted-foreground bg-muted px-2 py-0.5 rounded-md">
                {item.estimated_cost}
              </Text>
            )}
            {item.estimated_effort === EffortEnum.Low && (
              <Text className="text-muted-foreground">🛒</Text>
            )}
            {item.estimated_effort === EffortEnum.Medium && (
              <Text className="text-muted-foreground">🍳</Text>
            )}
            {item.estimated_effort === EffortEnum.High && (
              <Text className="text-muted-foreground">✅</Text>
            )}
            {item.completed && (
              <Text className="text-green-600 text-sm">✓ Terminé</Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  if (!session && !loading) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-center text-muted-foreground">
          Connectez-vous pour voir vos tâches.
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" className="text-primary" />
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background gap-4">
        <Text className="text-lg text-destructive text-center">{error}</Text>
        {/* Correction: Mettre loadTasks dans le onPress */}
        <Button onPress={loadTasks}>
          <Text>Réessayer</Text>
        </Button>
      </View>
    );
  }

  // Calculer les statistiques
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter((task) => task.completed).length;
  const progressPercentage =
    totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  return (
    <View className="flex-1 p-4 bg-background">
      {tasks.length === 0 ? (
        <View className="flex-1 justify-center items-center gap-2">
          <Text className="text-6xl mb-4">📝</Text>
          <Text className="text-xl font-semibold text-foreground">
            Aucune tâche assignée
          </Text>
          <Text className="text-muted-foreground text-center">
            Vos items à apporter apparaîtront ici.
          </Text>
        </View>
      ) : (
        <>
          {/* Statistiques */}
          <Card className="mb-4 border border-border">
            <CardHeader>
              <CardTitle className="text-lg font-semibold">
                Mes Statistiques
              </CardTitle>
            </CardHeader>
            <CardContent>
              <View className="flex-row justify-between items-center mb-3">
                <Text className="text-muted-foreground">Progression</Text>
                <Text className="font-semibold">
                  {completedTasks}/{totalTasks} ({progressPercentage}%)
                </Text>
              </View>
              <View className="w-full bg-gray-200 rounded-full h-2">
                <View
                  className="bg-green-600 h-2 rounded-full"
                  style={{ width: `${progressPercentage}%` }}
                />
              </View>
              <View className="flex-row justify-between mt-3">
                <View className="items-center">
                  <Text className="text-2xl">📋</Text>
                  <Text className="text-sm text-muted-foreground">Total</Text>
                  <Text className="font-semibold">{totalTasks}</Text>
                </View>
                <View className="items-center">
                  <Text className="text-2xl">✅</Text>
                  <Text className="text-sm text-muted-foreground">
                    Terminées
                  </Text>
                  <Text className="font-semibold text-green-600">
                    {completedTasks}
                  </Text>
                </View>
                <View className="items-center">
                  <Text className="text-2xl">⏳</Text>
                  <Text className="text-sm text-muted-foreground">
                    Restantes
                  </Text>
                  <Text className="font-semibold text-orange-600">
                    {totalTasks - completedTasks}
                  </Text>
                </View>
              </View>
            </CardContent>
          </Card>
        </>
      )}

      {tasks.length > 0 && (
        <FlatList
          data={taskSections}
          keyExtractor={(section) => section.eventId.toString()}
          renderItem={({ item: section }) => {
            const sectionCompletedTasks = section.data.filter(
              (task) => task.completed
            ).length;
            const sectionTotalTasks = section.data.length;
            const sectionProgress =
              sectionTotalTasks > 0
                ? Math.round((sectionCompletedTasks / sectionTotalTasks) * 100)
                : 0;

            return (
              <Card className="mb-4 border border-border">
                <CardHeader className="flex-row items-center justify-between pb-2 pt-3 px-4">
                  <View className="flex-row items-center">
                    <Text className="text-2xl mr-2">
                      {section.icon || "🎉"}
                    </Text>
                    <CardTitle className="text-lg font-semibold">
                      {section.title}
                    </CardTitle>
                  </View>
                  <View className="items-center">
                    <Text className="text-xs text-muted-foreground">
                      {sectionCompletedTasks}/{sectionTotalTasks}
                    </Text>
                    <Text className="text-xs font-semibold text-green-600">
                      {sectionProgress}%
                    </Text>
                  </View>
                </CardHeader>
                <CardContent className="px-0 pt-0">
                  {section.data.map((taskItem) => (
                    <View key={taskItem.id} className="px-4">
                      {renderTaskItem({ item: taskItem })}
                    </View>
                  ))}
                </CardContent>
              </Card>
            );
          }}
        />
      )}
    </View>
  );
}
// --- End app/(tabs)/tasks.tsx ---
