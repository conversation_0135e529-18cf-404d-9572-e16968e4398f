import React from "react";
import { View, ActivityIndicator } from "react-native";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Text } from "~/components/ui/text";
import { Switch } from "~/components/ui/switch";

interface EventOptionsSectionProps {
  allowSuggestions: boolean;
  allowPreAssignment: boolean;
  onAllowSuggestionsChange: (value: boolean) => void;
  onAllowPreAssignmentChange: (value: boolean) => void;
  loading?: {
    allowSuggestions?: boolean;
    allowPreAssignment?: boolean;
  };
  disabled?: boolean;
  className?: string;
}

export function EventOptionsSection({
  allowSuggestions,
  allowPreAssignment,
  onAllowSuggestionsChange,
  onAllowPreAssignmentChange,
  loading = {},
  disabled = false,
  className = "mb-8",
}: EventOptionsSectionProps) {
  return (
    <Card className={`border border-border bg-card rounded-xl shadow-sm ${className}`}>
      <CardHeader>
        <CardTitle className="text-lg font-medium text-foreground">
          Options de l'Événement
        </CardTitle>
      </CardHeader>
      <CardContent className="gap-4 px-4">
        <View className="flex-row items-center py-3">
          <View className="flex-1 pr-4">
            <Label
              nativeID="suggestionsLabel"
              className="text-base text-foreground"
            >
              Autoriser les suggestions d'items
            </Label>
            <Text className="text-sm text-muted-foreground mt-1">
              Les participants pourront proposer des items
            </Text>
          </View>
          <View className="items-center justify-center min-w-[80px]">
            {loading.allowSuggestions ? (
              <ActivityIndicator
                size="small"
                className="text-primary"
              />
            ) : (
              <Switch
                nativeID="suggestionsLabel"
                checked={allowSuggestions}
                onCheckedChange={onAllowSuggestionsChange}
                disabled={disabled || loading.allowSuggestions}
              />
            )}
          </View>
        </View>
        
        <View className="h-px bg-border mx-2" />
        
        <View className="flex-row items-center py-3">
          <View className="flex-1 pr-4">
            <Label
              nativeID="preassignLabel"
              className="text-base text-foreground"
            >
              Autoriser la pré-attribution ("Fixer")
            </Label>
            <Text className="text-sm text-muted-foreground mt-1">
              Les invités peuvent réserver des items à l'avance
            </Text>
          </View>
          <View className="items-center justify-center min-w-[80px]">
            {loading.allowPreAssignment ? (
              <ActivityIndicator
                size="small"
                className="text-primary"
              />
            ) : (
              <Switch
                nativeID="preassignLabel"
                checked={allowPreAssignment}
                onCheckedChange={onAllowPreAssignmentChange}
                disabled={disabled || loading.allowPreAssignment}
              />
            )}
          </View>
        </View>
      </CardContent>
    </Card>
  );
}
