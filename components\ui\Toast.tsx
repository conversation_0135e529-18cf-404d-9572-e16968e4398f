// --- Begin components/ui/Toast.tsx ---
import React, { useState, useEffect, useRef, useCallback } from "react";
import { View, StyleSheet } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  runOnJS,
} from "react-native-reanimated";
import { Portal } from "@rn-primitives/portal";
import { Text } from "./text";
import { cn } from "~/lib/utils";
// Icônes temporairement supprimées pour compatibilité web

type ToastType = "success" | "error" | "info";

interface ToastProps {
  message: string;
  duration?: number;
  type?: ToastType;
  isVisible: boolean;
  onDismiss: () => void;
}

const toastConfig = {
  success: { bg: "bg-green-600", emoji: "✅", iconColor: "text-white" },
  error: { bg: "bg-red-600", emoji: "❌", iconColor: "text-white" },
  info: { bg: "bg-blue-600", emoji: "ℹ️", iconColor: "text-white" },
};

export function Toast({
  message,
  duration = 3000,
  type = "info",
  isVisible,
  onDismiss,
}: ToastProps) {
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(-50);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const config = toastConfig[type];

  const hideToast = useCallback(() => {
    opacity.value = withTiming(0, {
      duration: 300,
      easing: Easing.in(Easing.ease),
    });
    translateY.value = withTiming(
      -50,
      { duration: 300, easing: Easing.in(Easing.ease) },
      () => {
        runOnJS(onDismiss)();
      }
    );
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, [opacity, translateY, onDismiss]);

  const showToast = useCallback(() => {
    opacity.value = withTiming(1, {
      duration: 300,
      easing: Easing.out(Easing.ease),
    });
    translateY.value = withTiming(50, {
      duration: 300,
      easing: Easing.out(Easing.ease),
    });

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      runOnJS(hideToast)();
    }, duration);
  }, [duration, opacity, translateY, hideToast]); // Ajout hideToast ici

  useEffect(() => {
    if (isVisible) {
      showToast();
    }
    // Ne pas appeler hideToast si isVisible devient false, onDismiss s'en charge via le timeout ou l'appel externe
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isVisible, showToast]); // Retiré hideToast des dépendances ici

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [{ translateY: translateY.value }],
    };
  });

  // Correction: Condition de rendu ajustée
  if (!isVisible && opacity.value === 0) {
    return null;
  }

  return (
    // Correction: Ajout de la prop 'name' obligatoire pour Portal
    <Portal name="toast-portal">
      <Animated.View style={[styles.container, animatedStyle]}>
        <View
          className={cn(
            "flex-row items-center p-4 rounded-md shadow-lg min-w-[250px] max-w-[90%]",
            config.bg
          )}
        >
          {/* Emoji à la place de l'icône */}
          <Text className="mr-3 text-lg">{config.emoji}</Text>
          <Text className="text-white text-base font-medium">{message}</Text>
        </View>
      </Animated.View>
    </Portal>
  );
}

// ... useToast hook et styles ...
// Hook for managing toast state easily (from toastService.ts, no change here if using store)
// const styles remains the same
const styles = StyleSheet.create({
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    alignItems: "center",
    zIndex: 9999,
    elevation: 9999,
  },
});
// --- End components/ui/Toast.tsx ---
