﻿export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      contact_group_members: {
        Row: {
          contact_id: number;
          group_id: number;
        };
        Insert: {
          contact_id: number;
          group_id: number;
        };
        Update: {
          contact_id?: number;
          group_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: "contact_group_members_contact_id_fkey";
            columns: ["contact_id"];
            isOneToOne: false;
            referencedRelation: "contacts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "contact_group_members_group_id_fkey";
            columns: ["group_id"];
            isOneToOne: false;
            referencedRelation: "contact_groups";
            referencedColumns: ["id"];
          }
        ];
      };
      contact_groups: {
        Row: {
          created_at: string;
          id: number;
          name: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          name: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          name?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "contact_groups_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      contacts: {
        Row: {
          contact_profile_id: string | null;
          created_at: string;
          email: string | null;
          id: number;
          name: string;
          phone: string | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          contact_profile_id?: string | null;
          created_at?: string;
          email?: string | null;
          id?: number;
          name: string;
          phone?: string | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          contact_profile_id?: string | null;
          created_at?: string;
          email?: string | null;
          id?: number;
          name?: string;
          phone?: string | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "contacts_contact_profile_id_fkey";
            columns: ["contact_profile_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "contacts_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      events: {
        Row: {
          allow_pre_assignment: boolean;
          allow_suggestions: boolean;
          created_at: string;
          date_time: string;
          description: string | null;
          icon: string | null;
          id: number;
          location: string | null;
          organizer_delegated: boolean;
          organizer_id: string;
          title: string;
          updated_at: string;
        };
        Insert: {
          allow_pre_assignment?: boolean;
          allow_suggestions?: boolean;
          created_at?: string;
          date_time: string;
          description?: string | null;
          icon?: string | null;
          id?: number;
          location?: string | null;
          organizer_delegated?: boolean;
          organizer_id: string;
          title: string;
          updated_at?: string;
        };
        Update: {
          allow_pre_assignment?: boolean;
          allow_suggestions?: boolean;
          created_at?: string;
          date_time?: string;
          description?: string | null;
          icon?: string | null;
          id?: number;
          location?: string | null;
          organizer_delegated?: boolean;
          organizer_id?: string;
          title?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "events_organizer_id_fkey";
            columns: ["organizer_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      items: {
        Row: {
          assigned_participant_id: number | null;
          category: string | null;
          created_at: string;
          estimated_cost: Database["public"]["Enums"]["cost_enum"] | null;
          estimated_effort: Database["public"]["Enums"]["effort_enum"] | null;
          event_id: number;
          fixed_by_participant_id: number | null;
          id: number;
          is_personal: boolean;
          is_suggestion: boolean;
          name: string;
          suggester_id: string | null;
          updated_at: string;
        };
        Insert: {
          assigned_participant_id?: number | null;
          category?: string | null;
          created_at?: string;
          estimated_cost?: Database["public"]["Enums"]["cost_enum"] | null;
          estimated_effort?: Database["public"]["Enums"]["effort_enum"] | null;
          event_id: number;
          fixed_by_participant_id?: number | null;
          id?: number;
          is_personal?: boolean;
          is_suggestion?: boolean;
          name: string;
          suggester_id?: string | null;
          updated_at?: string;
        };
        Update: {
          assigned_participant_id?: number | null;
          category?: string | null;
          created_at?: string;
          estimated_cost?: Database["public"]["Enums"]["cost_enum"] | null;
          estimated_effort?: Database["public"]["Enums"]["effort_enum"] | null;
          event_id?: number;
          fixed_by_participant_id?: number | null;
          id?: number;
          is_personal?: boolean;
          is_suggestion?: boolean;
          name?: string;
          suggester_id?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "items_assigned_participant_id_fkey";
            columns: ["assigned_participant_id"];
            isOneToOne: false;
            referencedRelation: "participants";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "items_event_id_fkey";
            columns: ["event_id"];
            isOneToOne: false;
            referencedRelation: "events";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "items_fixed_by_participant_id_fkey";
            columns: ["fixed_by_participant_id"];
            isOneToOne: false;
            referencedRelation: "participants";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "items_suggester_id_fkey";
            columns: ["suggester_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      participants: {
        Row: {
          anonymous_email: string | null;
          anonymous_name: string | null;
          anonymous_phone: string | null;
          created_at: string;
          event_id: number;
          id: number;
          invitation_token: string | null;
          role: Database["public"]["Enums"]["participant_role"];
          status: Database["public"]["Enums"]["participant_status"];
          updated_at: string;
          user_id: string | null;
        };
        Insert: {
          anonymous_email?: string | null;
          anonymous_name?: string | null;
          anonymous_phone?: string | null;
          created_at?: string;
          event_id: number;
          id?: number;
          invitation_token?: string | null;
          role?: Database["public"]["Enums"]["participant_role"];
          status?: Database["public"]["Enums"]["participant_status"];
          updated_at?: string;
          user_id?: string | null;
        };
        Update: {
          anonymous_email?: string | null;
          anonymous_name?: string | null;
          anonymous_phone?: string | null;
          created_at?: string;
          event_id?: number;
          id?: number;
          invitation_token?: string | null;
          role?: Database["public"]["Enums"]["participant_role"];
          status?: Database["public"]["Enums"]["participant_status"];
          updated_at?: string;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "participants_event_id_fkey";
            columns: ["event_id"];
            isOneToOne: false;
            referencedRelation: "events";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "participants_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          created_at: string;
          id: string;
          is_discoverable: boolean;
          name: string | null;
          updated_at: string;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string;
          id: string;
          is_discoverable?: boolean;
          name?: string | null;
          updated_at?: string;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string;
          id?: string;
          is_discoverable?: boolean;
          name?: string | null;
          updated_at?: string;
        };
        Relationships: [];
      };
      users: {
        Row: {
          created_at: string | null;
          email: string | null;
          id: number;
          name: string | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          created_at?: string | null;
          email?: string | null;
          id?: never;
          name?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          created_at?: string | null;
          email?: string | null;
          id?: never;
          name?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [];
      };
      badges: {
        Row: {
          id: number;
          name: string;
          title: string;
          description: string;
          icon: string;
          category: string;
          rarity: "common" | "rare" | "epic" | "legendary";
          points: number;
          requirements: Json;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          name: string;
          title: string;
          description: string;
          icon: string;
          category: string;
          rarity?: "common" | "rare" | "epic" | "legendary";
          points?: number;
          requirements: Json;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          name?: string;
          title?: string;
          description?: string;
          icon?: string;
          category?: string;
          rarity?: "common" | "rare" | "epic" | "legendary";
          points?: number;
          requirements?: Json;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      user_achievements: {
        Row: {
          id: number;
          user_id: string;
          badge_id: number;
          unlocked_at: string;
          progress: Json;
          is_completed: boolean;
          completion_data: Json;
        };
        Insert: {
          id?: number;
          user_id: string;
          badge_id: number;
          unlocked_at?: string;
          progress?: Json;
          is_completed?: boolean;
          completion_data?: Json;
        };
        Update: {
          id?: number;
          user_id?: string;
          badge_id?: number;
          unlocked_at?: string;
          progress?: Json;
          is_completed?: boolean;
          completion_data?: Json;
        };
        Relationships: [
          {
            foreignKeyName: "user_achievements_badge_id_fkey";
            columns: ["badge_id"];
            referencedRelation: "badges";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_achievements_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      user_stats: {
        Row: {
          id: number;
          user_id: string;
          total_points: number;
          level: number;
          events_organized: number;
          events_participated: number;
          total_participants_invited: number;
          total_items_managed: number;
          total_money_saved: number;
          perfect_events: number;
          streak_days: number;
          last_activity_date: string | null;
          achievements_unlocked: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          user_id: string;
          total_points?: number;
          level?: number;
          events_organized?: number;
          events_participated?: number;
          total_participants_invited?: number;
          total_items_managed?: number;
          total_money_saved?: number;
          perfect_events?: number;
          streak_days?: number;
          last_activity_date?: string | null;
          achievements_unlocked?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          user_id?: string;
          total_points?: number;
          level?: number;
          events_organized?: number;
          events_participated?: number;
          total_participants_invited?: number;
          total_items_managed?: number;
          total_money_saved?: number;
          perfect_events?: number;
          streak_days?: number;
          last_activity_date?: string | null;
          achievements_unlocked?: number;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_stats_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      get_events_for_user: {
        Args: { user_id_param: string };
        Returns: {
          allow_pre_assignment: boolean;
          allow_suggestions: boolean;
          created_at: string;
          date_time: string;
          description: string | null;
          icon: string | null;
          id: number;
          location: string | null;
          organizer_delegated: boolean;
          organizer_id: string;
          title: string;
          updated_at: string;
        }[];
      };
      is_participant: {
        Args: { event_id_input: number; user_id_input: string };
        Returns: boolean;
      };
      update_user_stats: {
        Args: {
          p_user_id: string;
          p_events_organized?: number;
          p_events_participated?: number;
          p_participants_invited?: number;
          p_items_managed?: number;
          p_money_saved?: number;
          p_perfect_events?: number;
        };
        Returns: undefined;
      };
      check_and_unlock_achievements: {
        Args: { p_user_id: string };
        Returns: Record<string, unknown>[];
      };
    };
    Enums: {
      cost_enum: "€" | "€€" | "€€€";
      effort_enum: "1" | "2" | "3";
      participant_role: "organizer" | "guest";
      participant_status: "pending" | "accepted" | "declined" | "maybe";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      cost_enum: ["€", "€€", "€€€"],
      effort_enum: ["1", "2", "3"],
      participant_role: ["organizer", "guest"],
      participant_status: ["pending", "accepted", "declined", "maybe"],
    },
  },
} as const;
