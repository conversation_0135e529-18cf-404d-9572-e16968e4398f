/**
 * Hook personnalisé pour gérer les achievements
 * Fournit l'état et les actions pour le système de badges
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  achievements, 
  Badge, 
  UserStats, 
  UserAchievement, 
  AchievementProgress,
  LeaderboardEntry 
} from '~/lib/achievementsService';
import { useAuth } from '~/lib/AuthContext';
import { showToast } from '~/lib/toastService';

export interface AchievementsState {
  badges: Badge[];
  userStats: UserStats | null;
  userAchievements: UserAchievement[];
  progress: AchievementProgress[];
  leaderboard: LeaderboardEntry[];
  loading: boolean;
  error: string | null;
  nextLevelInfo: {
    currentLevel: number;
    nextLevel: number;
    pointsRequired: number;
    progress: number;
  } | null;
}

export interface AchievementsActions {
  loadAchievements: () => Promise<void>;
  updateStats: (updates: any) => Promise<void>;
  checkNewAchievements: () => Promise<Badge[]>;
  refreshLeaderboard: () => Promise<void>;
  invalidateCache: () => Promise<void>;
}

export function useAchievements(): AchievementsState & AchievementsActions {
  const { session } = useAuth();
  const userId = session?.user?.id;

  const [state, setState] = useState<AchievementsState>({
    badges: [],
    userStats: null,
    userAchievements: [],
    progress: [],
    leaderboard: [],
    loading: true,
    error: null,
    nextLevelInfo: null,
  });

  /**
   * Charge toutes les données d'achievements
   */
  const loadAchievements = useCallback(async () => {
    if (!userId) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const [badges, userStats, userAchievements, progress, leaderboard] = await Promise.all([
        achievements.getBadges(),
        achievements.getUserStats(userId),
        achievements.getUserAchievements(userId),
        achievements.getProgress(userId),
        achievements.getLeaderboard(50),
      ]);

      const nextLevelInfo = userStats 
        ? achievements.calculateNextLevel(userStats.total_points)
        : null;

      setState({
        badges,
        userStats,
        userAchievements,
        progress,
        leaderboard,
        loading: false,
        error: null,
        nextLevelInfo,
      });
    } catch (error) {
      console.error('Error loading achievements:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Erreur lors du chargement des achievements',
      }));
    }
  }, [userId]);

  /**
   * Met à jour les statistiques utilisateur
   */
  const updateStats = useCallback(async (updates: any) => {
    if (!userId) return;

    try {
      await achievements.updateStats(userId, updates);
      
      // Recharger les données
      await loadAchievements();
      
      showToast('Statistiques mises à jour !', { type: 'success' });
    } catch (error) {
      console.error('Error updating stats:', error);
      showToast('Erreur lors de la mise à jour', { type: 'error' });
    }
  }, [userId, loadAchievements]);

  /**
   * Vérifie les nouveaux achievements
   */
  const checkNewAchievements = useCallback(async (): Promise<Badge[]> => {
    if (!userId) return [];

    try {
      const newBadges = await achievements.checkAchievements(userId);
      
      if (newBadges.length > 0) {
        // Recharger les données pour refléter les nouveaux badges
        await loadAchievements();
      }
      
      return newBadges;
    } catch (error) {
      console.error('Error checking achievements:', error);
      return [];
    }
  }, [userId, loadAchievements]);

  /**
   * Actualise le classement
   */
  const refreshLeaderboard = useCallback(async () => {
    try {
      const leaderboard = await achievements.getLeaderboard(50);
      setState(prev => ({ ...prev, leaderboard }));
    } catch (error) {
      console.error('Error refreshing leaderboard:', error);
    }
  }, []);

  /**
   * Invalide le cache
   */
  const invalidateCache = useCallback(async () => {
    try {
      await achievements.invalidateCache(userId);
      await loadAchievements();
      showToast('Cache invalidé', { type: 'info' });
    } catch (error) {
      console.error('Error invalidating cache:', error);
    }
  }, [userId, loadAchievements]);

  // Charger les achievements au montage et quand l'utilisateur change
  useEffect(() => {
    loadAchievements();
  }, [loadAchievements]);

  return {
    ...state,
    loadAchievements,
    updateStats,
    checkNewAchievements,
    refreshLeaderboard,
    invalidateCache,
  };
}

/**
 * Hook simplifié pour obtenir juste les stats utilisateur
 */
export function useUserStats() {
  const { session } = useAuth();
  const userId = session?.user?.id;
  
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadStats = async () => {
      if (!userId) {
        setLoading(false);
        return;
      }

      try {
        const userStats = await achievements.getUserStats(userId);
        setStats(userStats);
      } catch (error) {
        console.error('Error loading user stats:', error);
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, [userId]);

  const updateStats = async (updates: any) => {
    if (!userId) return;

    try {
      await achievements.updateStats(userId, updates);
      const userStats = await achievements.getUserStats(userId);
      setStats(userStats);
    } catch (error) {
      console.error('Error updating stats:', error);
    }
  };

  return { stats, loading, updateStats };
}

/**
 * Hook pour obtenir la progression vers les badges
 */
export function useAchievementProgress() {
  const { session } = useAuth();
  const userId = session?.user?.id;
  
  const [progress, setProgress] = useState<AchievementProgress[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProgress = async () => {
      if (!userId) {
        setLoading(false);
        return;
      }

      try {
        const achievementProgress = await achievements.getProgress(userId);
        setProgress(achievementProgress);
      } catch (error) {
        console.error('Error loading achievement progress:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProgress();
  }, [userId]);

  const refresh = async () => {
    if (!userId) return;

    setLoading(true);
    try {
      const achievementProgress = await achievements.getProgress(userId);
      setProgress(achievementProgress);
    } catch (error) {
      console.error('Error refreshing progress:', error);
    } finally {
      setLoading(false);
    }
  };

  return { progress, loading, refresh };
}

/**
 * Hook pour le classement
 */
export function useLeaderboard(limit: number = 50) {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadLeaderboard = async () => {
      try {
        const data = await achievements.getLeaderboard(limit);
        setLeaderboard(data);
      } catch (error) {
        console.error('Error loading leaderboard:', error);
      } finally {
        setLoading(false);
      }
    };

    loadLeaderboard();
  }, [limit]);

  const refresh = async () => {
    setLoading(true);
    try {
      const data = await achievements.getLeaderboard(limit);
      setLeaderboard(data);
    } catch (error) {
      console.error('Error refreshing leaderboard:', error);
    } finally {
      setLoading(false);
    }
  };

  return { leaderboard, loading, refresh };
}

/**
 * Hook pour déclencher automatiquement les achievements
 */
export function useAchievementTrigger() {
  const { session } = useAuth();
  const userId = session?.user?.id;

  const triggerEventOrganized = useCallback(async () => {
    if (!userId) return;

    try {
      await achievements.updateStats(userId, { events_organized: 1 });
      await achievements.checkAchievements(userId);
    } catch (error) {
      console.error('Error triggering event organized achievement:', error);
    }
  }, [userId]);

  const triggerEventParticipated = useCallback(async () => {
    if (!userId) return;

    try {
      await achievements.updateStats(userId, { events_participated: 1 });
      await achievements.checkAchievements(userId);
    } catch (error) {
      console.error('Error triggering event participated achievement:', error);
    }
  }, [userId]);

  const triggerParticipantInvited = useCallback(async (count: number = 1) => {
    if (!userId) return;

    try {
      await achievements.updateStats(userId, { participants_invited: count });
      await achievements.checkAchievements(userId);
    } catch (error) {
      console.error('Error triggering participant invited achievement:', error);
    }
  }, [userId]);

  const triggerItemManaged = useCallback(async (count: number = 1) => {
    if (!userId) return;

    try {
      await achievements.updateStats(userId, { items_managed: count });
      await achievements.checkAchievements(userId);
    } catch (error) {
      console.error('Error triggering item managed achievement:', error);
    }
  }, [userId]);

  const triggerMoneySaved = useCallback(async (amount: number) => {
    if (!userId) return;

    try {
      await achievements.updateStats(userId, { money_saved: amount });
      await achievements.checkAchievements(userId);
    } catch (error) {
      console.error('Error triggering money saved achievement:', error);
    }
  }, [userId]);

  const triggerPerfectEvent = useCallback(async () => {
    if (!userId) return;

    try {
      await achievements.updateStats(userId, { perfect_events: 1 });
      await achievements.checkAchievements(userId);
    } catch (error) {
      console.error('Error triggering perfect event achievement:', error);
    }
  }, [userId]);

  return {
    triggerEventOrganized,
    triggerEventParticipated,
    triggerParticipantInvited,
    triggerItemManaged,
    triggerMoneySaved,
    triggerPerfectEvent,
  };
}
