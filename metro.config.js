const { getDefaultConfig } = require("expo/metro-config");
const { withNativeWind } = require("nativewind/metro");
const path = require("path");

const config = getDefaultConfig(__dirname);

config.resolver.unstable_enablePackageExports = false;

// Add resolution for problematic modules
config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  "@supabase/node-fetch": require.resolve("node-fetch"),
  "node-fetch": require.resolve("node-fetch"),
  fetch: require.resolve("node-fetch"),
  "text-encoding": path.resolve(__dirname, "node_modules/text-encoding"),
};

// Add fallback for node modules
config.resolver.nodeModulesPaths = [path.resolve(__dirname, "node_modules")];

// Prevent Metro from resolving modules from the wrong directory
config.watchFolders = [path.resolve(__dirname)];

// Handle specific module issues
config.resolver.sourceExts = [...config.resolver.sourceExts, "mjs", "cjs"];
config.resolver.assetExts = [...config.resolver.assetExts, "db"];

// Handle platform-specific entry points
const defaultSourceExts = config.resolver.sourceExts;
config.resolver.sourceExts =
  process.env.EXPO_TARGET === "web"
    ? ["web.js", "web.ts", "web.tsx", ...defaultSourceExts]
    : defaultSourceExts;

module.exports = withNativeWind(config, { input: "./global.css" });
