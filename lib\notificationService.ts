/**
 * Service de notifications push pour Party Organizer
 * Gère l'enregistrement, l'envoi et la réception de notifications
 */

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { supabase } from './supabase';

// Configuration des notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Types pour les notifications
export interface NotificationData {
  type: 'event_reminder' | 'participant_joined' | 'item_added' | 'cost_updated' | 'settlement_request';
  eventId: number;
  title: string;
  body: string;
  data?: Record<string, any>;
}

export interface PushToken {
  token: string;
  userId: string;
  deviceId?: string;
  platform: 'ios' | 'android' | 'web';
  createdAt: string;
}

/**
 * Demande les permissions pour les notifications push
 */
export async function requestNotificationPermissions(): Promise<boolean> {
  try {
    if (!Device.isDevice) {
      console.warn('Les notifications push ne fonctionnent que sur des appareils physiques');
      return false;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.warn('Permission de notification refusée');
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erreur lors de la demande de permissions:', error);
    return false;
  }
}

/**
 * Obtient le token de notification push
 */
export async function getNotificationToken(): Promise<string | null> {
  try {
    if (!Device.isDevice) {
      return null;
    }

    const hasPermission = await requestNotificationPermissions();
    if (!hasPermission) {
      return null;
    }

    // Configuration du canal Android
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'Party Organizer',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
        sound: 'default',
      });
    }

    const tokenData = await Notifications.getExpoPushTokenAsync({
      projectId: Constants.expoConfig?.extra?.eas?.projectId || 'your-project-id',
    });

    return tokenData.data;
  } catch (error) {
    console.error('Erreur lors de l\'obtention du token:', error);
    return null;
  }
}

/**
 * Enregistre le token de notification pour un utilisateur
 */
export async function registerNotificationToken(userId: string): Promise<boolean> {
  try {
    const token = await getNotificationToken();
    if (!token) {
      return false;
    }

    const deviceId = Constants.deviceId || 'unknown';
    const platform = Platform.OS as 'ios' | 'android' | 'web';

    // Enregistrer le token dans Supabase
    const { error } = await supabase
      .from('notification_tokens')
      .upsert({
        user_id: userId,
        token,
        device_id: deviceId,
        platform,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id,device_id'
      });

    if (error) {
      console.error('Erreur lors de l\'enregistrement du token:', error);
      return false;
    }

    console.log('Token de notification enregistré avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'enregistrement du token:', error);
    return false;
  }
}

/**
 * Supprime le token de notification pour un utilisateur
 */
export async function unregisterNotificationToken(userId: string): Promise<boolean> {
  try {
    const deviceId = Constants.deviceId || 'unknown';

    const { error } = await supabase
      .from('notification_tokens')
      .delete()
      .eq('user_id', userId)
      .eq('device_id', deviceId);

    if (error) {
      console.error('Erreur lors de la suppression du token:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erreur lors de la suppression du token:', error);
    return false;
  }
}

/**
 * Envoie une notification push à un utilisateur spécifique
 */
export async function sendNotificationToUser(
  userId: string,
  notification: NotificationData
): Promise<boolean> {
  try {
    // Récupérer les tokens de l'utilisateur
    const { data: tokens, error } = await supabase
      .from('notification_tokens')
      .select('token')
      .eq('user_id', userId);

    if (error || !tokens || tokens.length === 0) {
      console.warn('Aucun token trouvé pour l\'utilisateur:', userId);
      return false;
    }

    // Préparer le message
    const message = {
      to: tokens.map(t => t.token),
      sound: 'default',
      title: notification.title,
      body: notification.body,
      data: {
        type: notification.type,
        eventId: notification.eventId,
        ...notification.data,
      },
      badge: 1,
    };

    // Envoyer via l'API Expo
    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('Erreurs lors de l\'envoi:', result.errors);
      return false;
    }

    console.log('Notification envoyée avec succès');
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'envoi de notification:', error);
    return false;
  }
}

/**
 * Envoie une notification à tous les participants d'un événement
 */
export async function sendNotificationToEventParticipants(
  eventId: number,
  notification: NotificationData,
  excludeUserId?: string
): Promise<boolean> {
  try {
    // Récupérer les participants de l'événement
    const { data: participants, error } = await supabase
      .from('participants')
      .select('user_id')
      .eq('event_id', eventId)
      .eq('status', 'accepted')
      .not('user_id', 'is', null);

    if (error || !participants) {
      console.error('Erreur lors de la récupération des participants:', error);
      return false;
    }

    // Filtrer l'utilisateur exclu
    const userIds = participants
      .map(p => p.user_id)
      .filter(id => id && id !== excludeUserId);

    if (userIds.length === 0) {
      console.warn('Aucun participant à notifier');
      return true;
    }

    // Envoyer les notifications
    const promises = userIds.map(userId => 
      sendNotificationToUser(userId, notification)
    );

    const results = await Promise.all(promises);
    const successCount = results.filter(Boolean).length;

    console.log(`Notifications envoyées: ${successCount}/${userIds.length}`);
    return successCount > 0;
  } catch (error) {
    console.error('Erreur lors de l\'envoi aux participants:', error);
    return false;
  }
}

/**
 * Configure les listeners pour les notifications reçues
 */
export function setupNotificationListeners(
  onNotificationReceived?: (notification: Notifications.Notification) => void,
  onNotificationResponse?: (response: Notifications.NotificationResponse) => void
) {
  // Notification reçue quand l'app est ouverte
  const receivedListener = Notifications.addNotificationReceivedListener(notification => {
    console.log('Notification reçue:', notification);
    onNotificationReceived?.(notification);
  });

  // Notification cliquée
  const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
    console.log('Notification cliquée:', response);
    onNotificationResponse?.(response);
  });

  return () => {
    Notifications.removeNotificationSubscription(receivedListener);
    Notifications.removeNotificationSubscription(responseListener);
  };
}

/**
 * Planifie une notification locale
 */
export async function scheduleLocalNotification(
  title: string,
  body: string,
  trigger: Date | number,
  data?: Record<string, any>
): Promise<string | null> {
  try {
    const identifier = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        sound: 'default',
      },
      trigger: typeof trigger === 'number' 
        ? { seconds: trigger }
        : { date: trigger },
    });

    return identifier;
  } catch (error) {
    console.error('Erreur lors de la planification de notification:', error);
    return null;
  }
}

/**
 * Annule une notification planifiée
 */
export async function cancelScheduledNotification(identifier: string): Promise<boolean> {
  try {
    await Notifications.cancelScheduledNotificationAsync(identifier);
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'annulation de notification:', error);
    return false;
  }
}
