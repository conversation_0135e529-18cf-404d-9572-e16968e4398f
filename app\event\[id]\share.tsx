import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  ActivityIndicator,
  Platform,
  Share,
  Linking,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { fetchEventDetails } from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event } from "~/lib/types";
import {
  QRCodeGenerator,
  generateShareUrl,
  generateShareMessage,
  generateSocialShareLinks,
} from "~/components/QRCodeGenerator";

export default function ShareEventScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { session } = useAuth();
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [shareUrl, setShareUrl] = useState("");

  useEffect(() => {
    if (id && session?.user?.id) {
      loadEventData();
    }
  }, [id, session?.user?.id]);

  const loadEventData = async () => {
    if (!id || !session?.user?.id) return;

    try {
      setLoading(true);
      const eventData = await fetchEventDetails(parseInt(id));

      if (eventData) {
        setEvent(eventData);
        // Générer l'URL de partage avec la fonction utilitaire
        const url = generateShareUrl(eventData.id);
        setShareUrl(url);
      } else {
        showToast("Événement non trouvé", { type: "error" });
        router.back();
      }
    } catch (error) {
      console.error("Error loading event:", error);
      showToast("Erreur lors du chargement de l'événement", { type: "error" });
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleCopyLink = async () => {
    if (Platform.OS === "web") {
      try {
        await navigator.clipboard.writeText(shareUrl);
        showToast("Lien copié dans le presse-papiers !", { type: "success" });
      } catch (error) {
        showToast("Impossible de copier le lien", { type: "error" });
      }
    } else {
      // Pour mobile, utiliser Share API
      try {
        const message = generateShareMessage(
          event?.title || "Événement",
          shareUrl
        );
        await Share.share({
          message,
          url: shareUrl,
          title: `Événement: ${event?.title}`,
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    }
  };

  const handleSocialShare = async (platform: string) => {
    if (!event) return;

    const socialLinks = generateSocialShareLinks(event.title, shareUrl);

    try {
      let url = "";
      switch (platform) {
        case "whatsapp":
          url = socialLinks.whatsapp;
          break;
        case "telegram":
          url = socialLinks.telegram;
          break;
        case "sms":
          url = socialLinks.sms;
          break;
        case "email":
          url = socialLinks.email;
          break;
        case "facebook":
          url = socialLinks.facebook;
          break;
        case "twitter":
          url = socialLinks.twitter;
          break;
        default:
          return;
      }

      if (Platform.OS === "web") {
        window.open(url, "_blank");
      } else {
        await Linking.openURL(url);
      }

      showToast(`Ouverture de ${platform}...`, { type: "success" });
    } catch (error) {
      console.error(`Error sharing via ${platform}:`, error);
      showToast(`Erreur lors du partage via ${platform}`, { type: "error" });
    }
  };

  const handleInviteLater = () => {
    router.push(`/event/${id}`);
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-muted-foreground">
          Chargement de l'événement...
        </Text>
      </View>
    );
  }

  if (!event) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <Text className="text-destructive">Événement non trouvé</Text>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-background">
      <View className={Platform.OS === "web" ? "max-w-md mx-auto p-6" : "p-4"}>
        {/* Icône de succès */}
        <View className="items-center mb-8 mt-8">
          <View className="w-20 h-20 bg-green-500 rounded-full items-center justify-center mb-4">
            <Text className="text-4xl">✓</Text>
          </View>
          <Text className="text-2xl font-bold text-center mb-2">
            Votre événement est prêt !
          </Text>
          <Text className="text-muted-foreground text-center">
            Invitez tous les participants en leur envoyant un message avec le
            lien d'invitation ou montrez-leur un QR code.
          </Text>
        </View>

        {/* Détails de l'événement */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex-row items-center">
              <Text className="text-2xl mr-3">{event.icon || "🎉"}</Text>
              <Text className="text-lg font-semibold">{event.title}</Text>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-sm text-muted-foreground mb-2">
              📅{" "}
              {new Date(event.date_time).toLocaleDateString("fr-FR", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}{" "}
              à{" "}
              {new Date(event.date_time).toLocaleTimeString("fr-FR", {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </Text>
            {event.location && (
              <Text className="text-sm text-muted-foreground mb-2">
                📍 {event.location}
              </Text>
            )}
            {event.description && (
              <Text className="text-sm text-foreground">
                {event.description}
              </Text>
            )}
          </CardContent>
        </Card>

        {/* Lien d'invitation */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Lien d'invitation</CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-sm text-muted-foreground mb-3">
              Partagez ce lien avec vos invités pour qu'ils puissent rejoindre
              l'événement :
            </Text>

            {/* Affichage du lien */}
            <View className="bg-muted p-3 rounded-lg mb-4">
              <Text className="text-sm font-mono text-foreground" selectable>
                {shareUrl}
              </Text>
            </View>

            {/* QR Code */}
            <View className="items-center mb-6">
              <QRCodeGenerator
                value={shareUrl}
                size={180}
                title="QR Code de l'événement"
                description="Montrez ce code aux participants pour qu'ils rejoignent facilement"
              />
            </View>

            {/* Boutons de partage */}
            <View className="gap-3">
              <Button
                onPress={() => handleSocialShare("whatsapp")}
                className="h-12 bg-green-600 flex-row items-center justify-center"
              >
                <Text className="text-white font-medium mr-2">💬</Text>
                <Text className="text-white font-medium">
                  Partager sur WhatsApp
                </Text>
              </Button>

              <View className="flex-row gap-3">
                <Button
                  variant="outline"
                  onPress={handleCopyLink}
                  className="flex-1 h-12 flex-row items-center justify-center"
                >
                  <Text className="mr-2">📋</Text>
                  <Text>Copier le lien</Text>
                </Button>

                <Button
                  variant="outline"
                  onPress={() => handleSocialShare("sms")}
                  className="h-12 px-4 flex-row items-center justify-center"
                >
                  <Text className="mr-1">💬</Text>
                  <Text>SMS</Text>
                </Button>
              </View>

              {/* Autres options de partage */}
              <View className="border-t border-border pt-4">
                <Text className="text-sm text-muted-foreground mb-3 text-center">
                  Autres options de partage
                </Text>
                <View className="flex-row flex-wrap gap-2 justify-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onPress={() => handleSocialShare("email")}
                    className="flex-row items-center"
                  >
                    <Text className="mr-1">📧</Text>
                    <Text>Email</Text>
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onPress={() => handleSocialShare("telegram")}
                    className="flex-row items-center"
                  >
                    <Text className="mr-1">✈️</Text>
                    <Text>Telegram</Text>
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onPress={() => handleSocialShare("facebook")}
                    className="flex-row items-center"
                  >
                    <Text className="mr-1">📘</Text>
                    <Text>Facebook</Text>
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onPress={() => handleSocialShare("twitter")}
                    className="flex-row items-center"
                  >
                    <Text className="mr-1">🐦</Text>
                    <Text>Twitter</Text>
                  </Button>
                </View>
              </View>
            </View>
          </CardContent>
        </Card>

        {/* Instructions pour les invités */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Comment vos invités rejoignent l'événement</CardTitle>
          </CardHeader>
          <CardContent>
            <View className="gap-3">
              <View className="flex-row items-start gap-3">
                <Text className="text-primary font-bold">1.</Text>
                <Text className="text-sm text-muted-foreground flex-1">
                  Ils cliquent sur le lien ou le collent dans l'app
                </Text>
              </View>

              <View className="flex-row items-start gap-3">
                <Text className="text-primary font-bold">2.</Text>
                <Text className="text-sm text-muted-foreground flex-1">
                  Ils choisissent leur nom dans la liste des participants
                </Text>
              </View>

              <View className="flex-row items-start gap-3">
                <Text className="text-primary font-bold">3.</Text>
                <Text className="text-sm text-muted-foreground flex-1">
                  Ils peuvent participer à l'organisation !
                </Text>
              </View>
            </View>
          </CardContent>
        </Card>

        {/* Bouton Inviter plus tard */}
        <Button
          variant="ghost"
          onPress={handleInviteLater}
          className="h-12 mb-8"
        >
          <Text className="text-primary">Inviter plus tard</Text>
        </Button>
      </View>
    </ScrollView>
  );
}
