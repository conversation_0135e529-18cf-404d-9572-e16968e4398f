import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Badge } from "~/components/ui/badge";
import { EventTemplate } from "~/lib/types";
import { fetchAvailableTemplates, searchTemplates } from "~/lib/templateService";
import { useAuth } from "~/lib/AuthContext";

interface TemplateSelectorProps {
  onSelectTemplate: (template: EventTemplate) => void;
  onCreateFromScratch: () => void;
}

export function TemplateSelector({ onSelectTemplate, onCreateFromScratch }: TemplateSelectorProps) {
  const { session } = useAuth();
  const [templates, setTemplates] = useState<EventTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  useEffect(() => {
    loadTemplates();
  }, [session?.user?.id]);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const data = await fetchAvailableTemplates(session?.user?.id);
      setTemplates(data);
    } catch (error) {
      console.error("Error loading templates:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.trim() === "") {
      loadTemplates();
      return;
    }

    setLoading(true);
    try {
      const results = await searchTemplates(query, session?.user?.id);
      setTemplates(results);
    } catch (error) {
      console.error("Error searching templates:", error);
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    if (selectedCategory === "all") return true;
    return template.category === selectedCategory;
  });

  const categories = [
    { id: "all", name: "Tous", icon: "📋" },
    { id: "predefined", name: "Prédéfinis", icon: "⭐" },
    { id: "custom", name: "Mes templates", icon: "👤" },
    { id: "community", name: "Communauté", icon: "👥" },
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "predefined": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "custom": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "community": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200";
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center p-6">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-muted-foreground">
          Chargement des templates...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-background">
      <View className={Platform.OS === "web" ? "max-w-4xl mx-auto p-4" : "p-4"}>
        
        {/* Header */}
        <View className="mb-6">
          <Text className="text-2xl font-bold text-center mb-2">
            Choisir un template
          </Text>
          <Text className="text-muted-foreground text-center">
            Créez votre événement en quelques secondes
          </Text>
        </View>

        {/* Recherche */}
        <View className="mb-4">
          <Input
            placeholder="Rechercher un template..."
            value={searchQuery}
            onChangeText={handleSearch}
            className="mb-4"
          />
        </View>

        {/* Filtres par catégorie */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          className="mb-6"
        >
          <View className="flex-row gap-2 px-1">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onPress={() => setSelectedCategory(category.id)}
                className="flex-row items-center"
              >
                <Text className="mr-1">{category.icon}</Text>
                <Text>{category.name}</Text>
              </Button>
            ))}
          </View>
        </ScrollView>

        {/* Option "Créer depuis zéro" */}
        <Card className="mb-6 border-dashed border-2 border-primary/30">
          <CardContent className="items-center p-6">
            <Text className="text-4xl mb-3">✨</Text>
            <Text className="text-lg font-semibold mb-2">
              Créer depuis zéro
            </Text>
            <Text className="text-muted-foreground text-center mb-4">
              Commencez avec un événement vierge et personnalisez tout
            </Text>
            <Button onPress={onCreateFromScratch}>
              <Text>🚀 Commencer</Text>
            </Button>
          </CardContent>
        </Card>

        {/* Liste des templates */}
        {filteredTemplates.length === 0 ? (
          <Card>
            <CardContent className="items-center p-6">
              <Text className="text-4xl mb-3">🔍</Text>
              <Text className="text-lg font-semibold mb-2">
                Aucun template trouvé
              </Text>
              <Text className="text-muted-foreground text-center">
                {searchQuery 
                  ? "Essayez avec d'autres mots-clés"
                  : "Aucun template disponible dans cette catégorie"
                }
              </Text>
            </CardContent>
          </Card>
        ) : (
          <View className="gap-4">
            {filteredTemplates.map((template) => (
              <Card key={template.id} className="border border-border">
                <CardHeader>
                  <View className="flex-row items-start justify-between">
                    <View className="flex-1">
                      <View className="flex-row items-center gap-2 mb-2">
                        <Text className="text-2xl">{template.icon}</Text>
                        <Text className="text-lg font-semibold flex-1">
                          {template.name}
                        </Text>
                        <Badge className={getCategoryColor(template.category)}>
                          <Text className="text-xs">
                            {template.category === "predefined" ? "Prédéfini" :
                             template.category === "custom" ? "Personnel" :
                             template.category === "community" ? "Communauté" : template.category}
                          </Text>
                        </Badge>
                      </View>
                      
                      {template.description && (
                        <Text className="text-muted-foreground text-sm mb-3">
                          {template.description}
                        </Text>
                      )}

                      <View className="flex-row items-center gap-4 mb-3">
                        <View className="flex-row items-center">
                          <Text className="text-xs text-muted-foreground mr-1">⏰</Text>
                          <Text className="text-xs text-muted-foreground">
                            {template.template_data.default_time}
                          </Text>
                        </View>
                        
                        {template.template_data.default_duration && (
                          <View className="flex-row items-center">
                            <Text className="text-xs text-muted-foreground mr-1">⌛</Text>
                            <Text className="text-xs text-muted-foreground">
                              {template.template_data.default_duration}h
                            </Text>
                          </View>
                        )}
                        
                        {template.template_items && (
                          <View className="flex-row items-center">
                            <Text className="text-xs text-muted-foreground mr-1">📝</Text>
                            <Text className="text-xs text-muted-foreground">
                              {template.template_items.length} items
                            </Text>
                          </View>
                        )}

                        {template.usage_count > 0 && (
                          <View className="flex-row items-center">
                            <Text className="text-xs text-muted-foreground mr-1">👥</Text>
                            <Text className="text-xs text-muted-foreground">
                              {template.usage_count} utilisations
                            </Text>
                          </View>
                        )}
                      </View>

                      {/* Aperçu des items essentiels */}
                      {template.template_items && template.template_items.length > 0 && (
                        <View className="mb-3">
                          <Text className="text-xs text-muted-foreground mb-1">
                            Items inclus :
                          </Text>
                          <View className="flex-row flex-wrap gap-1">
                            {template.template_items
                              .filter(item => item.is_essential)
                              .slice(0, 3)
                              .map((item, index) => (
                                <Badge key={index} variant="secondary" className="px-2 py-1">
                                  <Text className="text-xs">{item.name}</Text>
                                </Badge>
                              ))}
                            {template.template_items.filter(item => item.is_essential).length > 3 && (
                              <Badge variant="outline" className="px-2 py-1">
                                <Text className="text-xs">
                                  +{template.template_items.filter(item => item.is_essential).length - 3}
                                </Text>
                              </Badge>
                            )}
                          </View>
                        </View>
                      )}
                    </View>
                  </View>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <Button
                    onPress={() => onSelectTemplate(template)}
                    className="w-full"
                  >
                    <Text className="text-primary-foreground font-medium">
                      🎯 Utiliser ce template
                    </Text>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </View>
        )}
      </View>
    </ScrollView>
  );
}
