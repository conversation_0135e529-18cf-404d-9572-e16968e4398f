# Configuration Supabase

Ce dossier contient les fichiers de configuration pour Supabase.

## Structure

- `sql/` : Scripts SQL pour configurer la base de données
  - `schema.sql` : Définition du schéma de base de données
  - `functions.sql` : Définition des fonctions SQL
  - `policies.sql` : Définition des politiques RLS
  - `fixes.sql` : Corrections pour les problèmes connus

## Utilisation

Pour appliquer ces scripts à votre base de données Supabase :

1. Connectez-vous à votre projet Supabase
2. Allez dans la section "SQL Editor"
3. Exécutez les scripts dans l'ordre recommandé (voir le README dans le dossier `sql/`)

## Variables d'environnement

Les informations de connexion à Supabase doivent être stockées dans un fichier `.env` à la racine du projet :

```
EXPO_PUBLIC_SUPABASE_URL=votre_url_supabase
EXPO_PUBLIC_SUPABASE_ANON_KEY=votre_clé_anon
EXPO_PUBLIC_SUPABASE_SERVICE_KEY=votre_clé_service
```

N'incluez jamais ces informations directement dans le code source.
