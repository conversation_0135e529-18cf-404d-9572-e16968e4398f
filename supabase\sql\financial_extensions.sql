-- Extension financière pour Party Organizer
-- Ajoute la gestion des coûts réels et des transactions financières

-- 1. Ajouter les colonnes financières à la table items
ALTER TABLE items 
ADD COLUMN IF NOT EXISTS actual_cost DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS paid_by_participant_id BIGINT REFERENCES participants(id) ON DELETE SET NULL;

-- 2. Créer la table des transactions financières
CREATE TABLE IF NOT EXISTS financial_transactions (
    id BIGSERIAL PRIMARY KEY,
    event_id BIGINT NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    from_participant_id BIGINT NOT NULL REFERENCES participants(id) ON DELETE CASCADE,
    to_participant_id BIGINT NOT NULL REFERENCES participants(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    description TEXT,
    is_settled BOOLEAN NOT NULL DEFAULT FALSE,
    settled_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 3. Créer la table des résumés financiers (cache des calculs)
CREATE TABLE IF NOT EXISTS financial_summaries (
    id BIGSERIAL PRIMARY KEY,
    event_id BIGINT NOT NULL REFERENCES events(id) ON DELETE CASCADE,
    participant_id BIGINT NOT NULL REFERENCES participants(id) ON DELETE CASCADE,
    total_spent DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_owed DECIMAL(10,2) NOT NULL DEFAULT 0,
    net_balance DECIMAL(10,2) NOT NULL DEFAULT 0, -- positif = doit recevoir, négatif = doit payer
    last_calculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(event_id, participant_id)
);

-- 4. Index pour les performances
CREATE INDEX IF NOT EXISTS idx_financial_transactions_event_id ON financial_transactions(event_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_from_participant ON financial_transactions(from_participant_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_to_participant ON financial_transactions(to_participant_id);
CREATE INDEX IF NOT EXISTS idx_financial_summaries_event_id ON financial_summaries(event_id);
CREATE INDEX IF NOT EXISTS idx_items_paid_by ON items(paid_by_participant_id);

-- 5. Fonction pour recalculer automatiquement les résumés financiers
CREATE OR REPLACE FUNCTION recalculate_financial_summary(p_event_id BIGINT, p_participant_id BIGINT)
RETURNS void AS $$
DECLARE
    v_total_spent DECIMAL(10,2) := 0;
    v_total_owed DECIMAL(10,2) := 0;
    v_participant_count INTEGER;
BEGIN
    -- Calculer le total dépensé par ce participant
    SELECT COALESCE(SUM(actual_cost), 0)
    INTO v_total_spent
    FROM items
    WHERE event_id = p_event_id 
    AND paid_by_participant_id = p_participant_id
    AND actual_cost IS NOT NULL;
    
    -- Calculer le nombre de participants acceptés
    SELECT COUNT(*)
    INTO v_participant_count
    FROM participants
    WHERE event_id = p_event_id 
    AND status = 'accepted';
    
    -- Calculer ce que ce participant doit (part équitable du total)
    IF v_participant_count > 0 THEN
        SELECT COALESCE(SUM(actual_cost), 0) / v_participant_count
        INTO v_total_owed
        FROM items
        WHERE event_id = p_event_id 
        AND actual_cost IS NOT NULL;
    END IF;
    
    -- Mettre à jour ou insérer le résumé
    INSERT INTO financial_summaries (event_id, participant_id, total_spent, total_owed, net_balance, last_calculated_at)
    VALUES (p_event_id, p_participant_id, v_total_spent, v_total_owed, v_total_spent - v_total_owed, NOW())
    ON CONFLICT (event_id, participant_id)
    DO UPDATE SET
        total_spent = EXCLUDED.total_spent,
        total_owed = EXCLUDED.total_owed,
        net_balance = EXCLUDED.net_balance,
        last_calculated_at = EXCLUDED.last_calculated_at;
END;
$$ LANGUAGE plpgsql;

-- 6. Fonction pour recalculer tous les résumés d'un événement
CREATE OR REPLACE FUNCTION recalculate_event_financial_summaries(p_event_id BIGINT)
RETURNS void AS $$
DECLARE
    participant_record RECORD;
BEGIN
    -- Recalculer pour tous les participants de l'événement
    FOR participant_record IN 
        SELECT id FROM participants WHERE event_id = p_event_id AND status = 'accepted'
    LOOP
        PERFORM recalculate_financial_summary(p_event_id, participant_record.id);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 7. Trigger pour recalculer automatiquement quand un item est modifié
CREATE OR REPLACE FUNCTION trigger_recalculate_financial_summary()
RETURNS TRIGGER AS $$
BEGIN
    -- Recalculer pour l'événement concerné
    PERFORM recalculate_event_financial_summaries(COALESCE(NEW.event_id, OLD.event_id));
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger
DROP TRIGGER IF EXISTS items_financial_update ON items;
CREATE TRIGGER items_financial_update
    AFTER INSERT OR UPDATE OR DELETE ON items
    FOR EACH ROW
    EXECUTE FUNCTION trigger_recalculate_financial_summary();

-- 8. Politique de sécurité RLS
ALTER TABLE financial_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_summaries ENABLE ROW LEVEL SECURITY;

-- Les utilisateurs peuvent voir les transactions de leurs événements
CREATE POLICY "Users can view financial transactions for their events" ON financial_transactions
    FOR SELECT USING (
        event_id IN (
            SELECT e.id FROM events e
            JOIN participants p ON e.id = p.event_id
            WHERE p.user_id = auth.uid() AND p.status = 'accepted'
        )
    );

-- Les organisateurs peuvent gérer les transactions
CREATE POLICY "Organizers can manage financial transactions" ON financial_transactions
    FOR ALL USING (
        event_id IN (
            SELECT id FROM events WHERE organizer_id = auth.uid()
        )
    );

-- Politique similaire pour les résumés financiers
CREATE POLICY "Users can view financial summaries for their events" ON financial_summaries
    FOR SELECT USING (
        event_id IN (
            SELECT e.id FROM events e
            JOIN participants p ON e.id = p.event_id
            WHERE p.user_id = auth.uid() AND p.status = 'accepted'
        )
    );

-- 9. Créer la table des tokens de notification
CREATE TABLE IF NOT EXISTS notification_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    token TEXT NOT NULL,
    device_id TEXT NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, device_id)
);

-- 10. Index pour les tokens de notification
CREATE INDEX IF NOT EXISTS idx_notification_tokens_user_id ON notification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_tokens_active ON notification_tokens(is_active);

-- 11. Politique de sécurité pour les tokens
ALTER TABLE notification_tokens ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own notification tokens" ON notification_tokens
    FOR ALL USING (user_id = auth.uid());

-- 12. Créer la table des notifications envoyées (historique)
CREATE TABLE IF NOT EXISTS notification_history (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    event_id BIGINT REFERENCES events(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    data JSONB,
    sent_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    read_at TIMESTAMPTZ,
    clicked_at TIMESTAMPTZ
);

-- 13. Index pour l'historique des notifications
CREATE INDEX IF NOT EXISTS idx_notification_history_user_id ON notification_history(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_history_event_id ON notification_history(event_id);
CREATE INDEX IF NOT EXISTS idx_notification_history_sent_at ON notification_history(sent_at);

-- 14. Politique de sécurité pour l'historique
ALTER TABLE notification_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notification history" ON notification_history
    FOR SELECT USING (user_id = auth.uid());

-- Commentaires pour la documentation
COMMENT ON TABLE financial_transactions IS 'Transactions financières entre participants pour le remboursement des dépenses';
COMMENT ON TABLE financial_summaries IS 'Résumés financiers calculés automatiquement pour chaque participant par événement';
-- 15. Créer la table des templates d'événements
CREATE TABLE IF NOT EXISTS event_templates (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT DEFAULT '🎉',
    category TEXT NOT NULL, -- 'predefined', 'custom', 'community'
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    usage_count INTEGER NOT NULL DEFAULT 0,

    -- Données du template
    template_data JSONB NOT NULL, -- Structure: { title_template, description_template, default_time, default_duration }

    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 16. Créer la table des items de template
CREATE TABLE IF NOT EXISTS template_items (
    id BIGSERIAL PRIMARY KEY,
    template_id BIGINT NOT NULL REFERENCES event_templates(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    category TEXT,
    estimated_cost TEXT, -- '€', '€€', '€€€'
    estimated_effort TEXT, -- 'easy', 'medium', 'hard'
    is_essential BOOLEAN NOT NULL DEFAULT FALSE, -- Item obligatoire ou optionnel
    suggested_quantity INTEGER DEFAULT 1,
    description TEXT,
    order_index INTEGER NOT NULL DEFAULT 0,

    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 17. Index pour les templates
CREATE INDEX IF NOT EXISTS idx_event_templates_category ON event_templates(category);
CREATE INDEX IF NOT EXISTS idx_event_templates_public ON event_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_event_templates_created_by ON event_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_template_items_template_id ON template_items(template_id);
CREATE INDEX IF NOT EXISTS idx_template_items_order ON template_items(template_id, order_index);

-- 18. Politique de sécurité pour les templates
ALTER TABLE event_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE template_items ENABLE ROW LEVEL SECURITY;

-- Tout le monde peut voir les templates publics et prédéfinis
CREATE POLICY "Public templates are viewable by everyone" ON event_templates
    FOR SELECT USING (is_public = TRUE OR category = 'predefined');

-- Les utilisateurs peuvent voir leurs propres templates
CREATE POLICY "Users can view their own templates" ON event_templates
    FOR SELECT USING (created_by = auth.uid());

-- Les utilisateurs peuvent créer leurs propres templates
CREATE POLICY "Users can create their own templates" ON event_templates
    FOR INSERT WITH CHECK (created_by = auth.uid() AND category = 'custom');

-- Les utilisateurs peuvent modifier leurs propres templates
CREATE POLICY "Users can update their own templates" ON event_templates
    FOR UPDATE USING (created_by = auth.uid());

-- Politique pour les items de template
CREATE POLICY "Template items are viewable with their templates" ON template_items
    FOR SELECT USING (
        template_id IN (
            SELECT id FROM event_templates
            WHERE is_public = TRUE
            OR category = 'predefined'
            OR created_by = auth.uid()
        )
    );

CREATE POLICY "Users can manage items of their own templates" ON template_items
    FOR ALL USING (
        template_id IN (
            SELECT id FROM event_templates WHERE created_by = auth.uid()
        )
    );

COMMENT ON TABLE event_templates IS 'Templates d''événements prédéfinis et personnalisés';
COMMENT ON TABLE template_items IS 'Items suggérés pour chaque template d''événement';
COMMENT ON TABLE notification_tokens IS 'Tokens de notification push pour chaque appareil utilisateur';
COMMENT ON TABLE notification_history IS 'Historique des notifications envoyées aux utilisateurs';
COMMENT ON FUNCTION recalculate_financial_summary IS 'Recalcule le résumé financier pour un participant donné dans un événement';
COMMENT ON FUNCTION recalculate_event_financial_summaries IS 'Recalcule tous les résumés financiers pour un événement donné';
