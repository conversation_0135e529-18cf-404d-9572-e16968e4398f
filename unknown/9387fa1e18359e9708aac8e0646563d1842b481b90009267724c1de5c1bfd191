#!/usr/bin/env node

/**
 * Script de migration pour unifier les composants Button
 * 
 * Ce script remplace tous les usages de CustomButton par Button
 * et supprime le fichier CustomButton devenu obsolète.
 */

const fs = require('fs');
const path = require('path');

// Fonction pour parcourir récursivement les fichiers
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      walkDir(filePath, callback);
    } else if (stat.isFile() && (file.endsWith('.tsx') || file.endsWith('.ts'))) {
      callback(filePath);
    }
  });
}

// Fonction pour migrer un fichier
function migrateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  
  // Remplacer les imports de CustomButton
  if (content.includes('CustomButton')) {
    console.log(`📝 Migration de ${path.relative(process.cwd(), filePath)}`);
    
    // Remplacer l'import
    content = content.replace(
      /import\s*{\s*CustomButton(?:\s+as\s+Button)?\s*}\s*from\s*["']~\/components\/ui\/custom-button["'];?/g,
      'import { Button } from "~/components/ui/button";'
    );
    
    // Remplacer les usages de CustomButton par Button
    content = content.replace(/CustomButton/g, 'Button');
    
    // Remplacer les imports avec alias
    content = content.replace(
      /import\s*{\s*CustomButton\s+as\s+Button\s*}\s*from\s*["']~\/components\/ui\/custom-button["'];?/g,
      'import { Button } from "~/components/ui/button";'
    );
    
    hasChanges = true;
  }
  
  if (hasChanges) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ ${path.relative(process.cwd(), filePath)} migré`);
  }
  
  return hasChanges;
}

console.log('🔄 Migration des composants Button\n');

// Parcourir tous les fichiers TypeScript/React
let migratedFiles = 0;
const appDir = path.join(process.cwd(), 'app');
const componentsDir = path.join(process.cwd(), 'components');

console.log('🔍 Recherche des fichiers à migrer...\n');

// Migrer les fichiers dans app/
if (fs.existsSync(appDir)) {
  walkDir(appDir, (filePath) => {
    if (migrateFile(filePath)) {
      migratedFiles++;
    }
  });
}

// Migrer les fichiers dans components/
if (fs.existsSync(componentsDir)) {
  walkDir(componentsDir, (filePath) => {
    // Ignorer le fichier custom-button.tsx lui-même
    if (!filePath.includes('custom-button.tsx')) {
      if (migrateFile(filePath)) {
        migratedFiles++;
      }
    }
  });
}

console.log(`\n📊 Résumé de la migration:`);
console.log(`   ✅ ${migratedFiles} fichier(s) migré(s)`);

// Vérifier si CustomButton est encore utilisé
let stillUsed = false;
walkDir(appDir, (filePath) => {
  const content = fs.readFileSync(filePath, 'utf8');
  if (content.includes('CustomButton') && !filePath.includes('custom-button.tsx')) {
    console.log(`⚠️  CustomButton encore utilisé dans: ${path.relative(process.cwd(), filePath)}`);
    stillUsed = true;
  }
});

walkDir(componentsDir, (filePath) => {
  const content = fs.readFileSync(filePath, 'utf8');
  if (content.includes('CustomButton') && !filePath.includes('custom-button.tsx')) {
    console.log(`⚠️  CustomButton encore utilisé dans: ${path.relative(process.cwd(), filePath)}`);
    stillUsed = true;
  }
});

if (!stillUsed) {
  console.log('\n🗑️  CustomButton n\'est plus utilisé nulle part');
  
  const customButtonPath = path.join(process.cwd(), 'components', 'ui', 'custom-button.tsx');
  if (fs.existsSync(customButtonPath)) {
    // Créer une sauvegarde avant suppression
    const backupPath = `${customButtonPath}.backup.${Date.now()}`;
    fs.copyFileSync(customButtonPath, backupPath);
    console.log(`💾 Sauvegarde créée: ${path.basename(backupPath)}`);
    
    // Supprimer le fichier
    fs.unlinkSync(customButtonPath);
    console.log('✅ components/ui/custom-button.tsx supprimé');
  }
} else {
  console.log('\n⚠️  CustomButton est encore utilisé, fichier conservé');
}

console.log('\n🎯 PROCHAINES ÉTAPES:');
console.log('1. Vérifiez que l\'application compile sans erreurs');
console.log('2. Testez les boutons avec état de chargement');
console.log('3. Vérifiez la compatibilité cross-platform');
console.log('\n🚀 Migration terminée!');
