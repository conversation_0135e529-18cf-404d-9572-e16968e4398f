# System Patterns

## System Architecture

The application is an Expo-based React Native project targeting iOS, Android, and Web. It utilizes Supabase for backend services. The UI is built with ShadCN UI components adapted for React Native, styled using NativeWind (Tailwind CSS). Dark mode is a core feature, managed through CSS variables and NativeWind's color scheme utilities. Routing is handled by Expo Router.

```mermaid
graph TD
    User --> App[Expo React Native App]
    App --> ExpoRouter[Expo Router]
    App --> ShadCN_UI[ShadCN UI Components]
    App --> NativeWind[NativeWind/Tailwind CSS]
    App --> DarkMode[Dark Mode System]
    App --> Supabase[Supabase BaaS]
    App --> Zustand[Zustand State Mgmt]
    App --> AuthContext[Auth Context]

    ShadCN_UI -- uses --> RNPrimitives[@rn-primitives]
    ShadCN_UI -- styled by --> NativeWind
    NativeWind -- configured by --> tailwind.config.js
    NativeWind -- uses styles from --> global.css
    DarkMode -- uses hook --> useColorScheme.tsx
    DarkMode -- toggled by --> ThemeToggle.tsx
    DarkMode -- applies class via --> app/_layout.tsx & NativeWind
    DarkMode -- defines themes in --> global.css (CSS Variables)
    Supabase -- handles --> Authentication
    Supabase -- handles --> Data CRUD
```

## Key Technical Decisions

- **ShadCN UI for React Native with NativeWind**: Chosen for a consistent, utility-first styling approach and a rich set of pre-built, customizable components. This promotes rapid UI development and maintainability.
- **CSS Variables for Theming**: Adopted for dynamic theming (light/dark modes), allowing styles to adapt based on a root class (`.dark`). This is a standard ShadCN UI pattern.
- **Expo Router**: Selected for its file-system based routing, simplifying navigation setup.
- **Supabase**: Leveraged for its comprehensive BaaS features, reducing backend development overhead.

## Design Patterns in Use

- **Provider Pattern (React Context API)**: Used for `AuthContext` to manage authentication state globally.
- **Custom Hooks**: `useColorScheme` encapsulates theme logic. `useAuth` provides access to auth state.
- **Utility Functions**: `cn` function in `lib/utils.ts` for class name composition.
- **Global State (Zustand)**: Used for `ToastService`, allowing toast notifications to be triggered from anywhere in the app.
- **File-System Routing**: Implemented by Expo Router.
- **Component-Based Architecture**: Standard React pattern, with UI elements broken down into reusable ShadCN components.

## Component Relationships

- **`app/_layout.tsx` (Root Layout)**:
  - Imports `global.css` (styles and CSS variables).
  - Manages React Navigation `ThemeProvider` based on `useColorScheme`.
  - Applies `.dark` class to `document.documentElement` for web.
  - Hosts `PortalHost` for ShadCN overlay components (Dialogs, Popovers).
  - Wraps content with `AuthProvider`.
- **ShadCN UI Components (`components/ui/*`)**:
  - Depend on `@rn-primitives/*`, `lucide-react-native`.
  - Styled using Tailwind classes via NativeWind.
  - Utilize the `cn` utility from `lib/utils.ts`.
  - Adapt to dark/light mode via CSS variables defined in `global.css`.
- **Theme Management**:
  - `lib/useColorScheme.tsx` wraps NativeWind's `useColorScheme`.
  - `components/ThemeToggle.tsx` uses `useColorScheme` to change themes.
  - NativeWind and `app/_layout.tsx` apply the theme change, triggering CSS variable swaps.
- **Authentication**:
  - `components/Auth.tsx` likely handles UI for login/signup.
  - `lib/AuthContext.tsx` provides auth state and functions (`session`, `signOut`).
  - Supabase client (`lib/supabase.ts`) interacts with the Supabase backend.

## Critical Implementation Paths

- **Theme Switching**:
  1. User interacts with `ThemeToggle.tsx`.
  2. `setColorScheme` (from `useColorScheme.tsx` -> NativeWind) is called.
  3. NativeWind updates its internal state and applies/removes the `.dark` class (or equivalent for native).
  4. CSS variables in `global.css` for the active theme take effect.
  5. Components re-render with new theme styles.
  6. `app/_layout.tsx` updates React Navigation theme and status bar.
- **User Authentication**:
  1. User interacts with `Auth.tsx` component.
  2. Supabase client methods are called for login/signup.
  3. `AuthContext` updates with session information.
  4. UI re-renders based on authentication state (e.g., `ProfileScreen`).
- **Component Rendering with ShadCN UI**:
  1. Component (e.g., `Button.tsx`) is used in a screen.
  2. It uses `cn()` to merge base, variant, and passed-in Tailwind classes.
  3. NativeWind processes these classes.
  4. Styles are applied, respecting current theme (light/dark) via CSS variables.

_(Cline's Note: This file documents the "how" of the system's design and construction, building on `projectbrief.md`.)_
