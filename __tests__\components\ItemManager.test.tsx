import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { ItemManager } from '../../components/ItemManager';
import { Item, CostEnum, EffortEnum } from '../../lib/types';

// Mock des dépendances
jest.mock('../../lib/toastService', () => ({
  showToast: jest.fn(),
}));

describe('ItemManager', () => {
  const mockItems: Item[] = [
    {
      id: 1,
      event_id: 1,
      suggester_id: null,
      name: 'Salade de fruits',
      category: 'food',
      estimated_cost: CostEnum.Medium,
      estimated_effort: EffortEnum.Low,
      is_suggestion: false,
      is_personal: false,
      assigned_participant_id: null,
      fixed_by_participant_id: null,
      completed: false,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 2,
      event_id: 1,
      suggester_id: null,
      name: 'Enceinte Bluetooth',
      category: 'equipment',
      estimated_cost: CostEnum.Expensive,
      estimated_effort: EffortEnum.Medium,
      is_suggestion: false,
      is_personal: false,
      assigned_participant_id: 1,
      fixed_by_participant_id: null,
      completed: false,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
  ];

  const mockProps = {
    items: mockItems,
    onItemsChange: jest.fn(),
    eventId: 1,
    isOrganizer: true,
    className: '',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render items correctly', () => {
    const { getByText } = render(<ItemManager {...mockProps} />);
    
    expect(getByText('Salade de fruits')).toBeTruthy();
    expect(getByText('Enceinte Bluetooth')).toBeTruthy();
    expect(getByText('2 items')).toBeTruthy();
  });

  it('should group items by category', () => {
    const { getByText } = render(<ItemManager {...mockProps} />);
    
    // Vérifier que les catégories sont affichées
    expect(getByText('Nourriture')).toBeTruthy();
    expect(getByText('Matériel')).toBeTruthy();
  });

  it('should show add form when button is pressed', () => {
    const { getByText, getByPlaceholderText } = render(
      <ItemManager {...mockProps} />
    );
    
    const addButton = getByText('➕ Ajouter un item');
    fireEvent.press(addButton);
    
    expect(getByPlaceholderText('Ex: Salade de fruits')).toBeTruthy();
  });

  it('should add new item', async () => {
    const { getByText, getByPlaceholderText } = render(
      <ItemManager {...mockProps} />
    );
    
    // Ouvrir le formulaire
    const addButton = getByText('➕ Ajouter un item');
    fireEvent.press(addButton);
    
    // Remplir le formulaire
    const nameInput = getByPlaceholderText('Ex: Salade de fruits');
    fireEvent.changeText(nameInput, 'Gâteau au chocolat');
    
    // Soumettre
    const submitButton = getByText('Ajouter');
    fireEvent.press(submitButton);
    
    await waitFor(() => {
      expect(mockProps.onItemsChange).toHaveBeenCalled();
      const newItems = mockProps.onItemsChange.mock.calls[0][0];
      expect(newItems).toHaveLength(3);
      expect(newItems[2].name).toBe('Gâteau au chocolat');
    });
  });

  it('should not add item with empty name', async () => {
    const { getByText, getByPlaceholderText } = render(
      <ItemManager {...mockProps} />
    );
    
    // Ouvrir le formulaire
    const addButton = getByText('➕ Ajouter un item');
    fireEvent.press(addButton);
    
    // Essayer de soumettre sans nom
    const submitButton = getByText('Ajouter');
    fireEvent.press(submitButton);
    
    await waitFor(() => {
      expect(mockProps.onItemsChange).not.toHaveBeenCalled();
    });
  });

  it('should remove item', async () => {
    const { getAllByText } = render(<ItemManager {...mockProps} />);
    
    const removeButtons = getAllByText('×');
    fireEvent.press(removeButtons[0]);
    
    await waitFor(() => {
      expect(mockProps.onItemsChange).toHaveBeenCalled();
      const newItems = mockProps.onItemsChange.mock.calls[0][0];
      expect(newItems).toHaveLength(1);
      expect(newItems[0].name).toBe('Enceinte Bluetooth');
    });
  });

  it('should display cost and effort indicators', () => {
    const { getByText } = render(<ItemManager {...mockProps} />);
    
    expect(getByText('€€ Moyen')).toBeTruthy(); // Coût de la salade
    expect(getByText('🟢 Facile')).toBeTruthy(); // Effort de la salade
    expect(getByText('€€€ Cher')).toBeTruthy(); // Coût de l'enceinte
    expect(getByText('🟡 Moyen')).toBeTruthy(); // Effort de l'enceinte
  });

  it('should show assignment status', () => {
    const { getByText } = render(<ItemManager {...mockProps} />);
    
    expect(getByText('✓ Assigné')).toBeTruthy(); // Enceinte assignée
  });

  it('should show empty state when no items', () => {
    const emptyProps = { ...mockProps, items: [] };
    const { getByText } = render(<ItemManager {...emptyProps} />);
    
    expect(getByText('Aucun item ajouté')).toBeTruthy();
    expect(getByText('Commencez par ajouter les items nécessaires pour votre événement')).toBeTruthy();
  });

  it('should cancel add form', () => {
    const { getByText, queryByPlaceholderText } = render(
      <ItemManager {...mockProps} />
    );
    
    // Ouvrir le formulaire
    const addButton = getByText('➕ Ajouter un item');
    fireEvent.press(addButton);
    
    // Annuler
    const cancelButton = getByText('Annuler');
    fireEvent.press(cancelButton);
    
    // Vérifier que le formulaire est fermé
    expect(queryByPlaceholderText('Ex: Salade de fruits')).toBeNull();
  });
});
