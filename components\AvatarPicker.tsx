import React, { useState } from "react";
import {
  View,
  Text,
  Pressable,
  Modal,
  Platform,
  Alert,
  ActivityIndicator,
} from "react-native";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Dialog, DialogContent } from "~/components/ui/dialog";
import { EmojiPicker } from "~/components/EmojiPicker";
import {
  avatarService,
  AvatarData,
  AvatarUploadResult,
} from "~/lib/avatarService";
import { avatarServiceBase64 } from "~/lib/avatarServiceBase64";
import { showToast } from "~/lib/toastService";
import { cn } from "~/lib/utils";

interface AvatarPickerProps {
  currentAvatar?: string | null;
  onAvatarSelected: (avatarData: AvatarData) => void;
  onClose: () => void;
  userId: string;
  isVisible: boolean;
}

export function AvatarPicker({
  currentAvatar,
  onAvatarSelected,
  onClose,
  userId,
  isVisible,
}: AvatarPickerProps) {
  const [loading, setLoading] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showActionSheet, setShowActionSheet] = useState(false);

  const handleRandomEmoji = () => {
    const randomEmoji = avatarServiceBase64.generateRandomFaceEmoji();
    console.log("🎲 Emoji aléatoire généré:", randomEmoji);
    onAvatarSelected({
      type: "emoji",
      value: randomEmoji,
      isRandom: true,
    });
    onClose();
  };

  const handleCustomEmoji = () => {
    setShowActionSheet(false);
    setShowEmojiPicker(true);
  };

  const handleEmojiSelected = (emoji: any) => {
    onAvatarSelected({
      type: "emoji",
      value: emoji.native,
      isRandom: false,
    });
    setShowEmojiPicker(false);
    onClose();
  };

  const handlePhotoFromGallery = async () => {
    setShowActionSheet(false);
    setLoading(true);

    try {
      const result = await avatarService.pickImageFromGallery();

      if (result && !result.canceled && result.assets?.[0]) {
        const imageUri = result.assets[0].uri;
        console.log("🖼️ Upload via base64 (galerie):", imageUri);

        const uploadResult = await avatarServiceBase64.uploadImage(
          imageUri,
          userId
        );

        if (uploadResult.success && uploadResult.avatarUrl) {
          onAvatarSelected({
            type: "photo",
            value: uploadResult.avatarUrl,
          });
          showToast("Photo de profil mise à jour !", { type: "success" });
          onClose();
        } else {
          showToast(uploadResult.error || "Erreur lors de l'upload", {
            type: "error",
          });
        }
      }
    } catch (error) {
      console.error("Erreur lors de la sélection de photo:", error);
      showToast("Erreur lors de la sélection de photo", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const handlePhotoFromCamera = async () => {
    setShowActionSheet(false);
    setLoading(true);

    try {
      const result = await avatarService.takePhotoWithCamera();

      if (result && !result.canceled && result.assets?.[0]) {
        const imageUri = result.assets[0].uri;
        console.log("📷 Upload via base64 (caméra):", imageUri);

        const uploadResult = await avatarServiceBase64.uploadImage(
          imageUri,
          userId
        );

        if (uploadResult.success && uploadResult.avatarUrl) {
          onAvatarSelected({
            type: "photo",
            value: uploadResult.avatarUrl,
          });
          showToast("Photo de profil mise à jour !", { type: "success" });
          onClose();
        } else {
          showToast(uploadResult.error || "Erreur lors de l'upload", {
            type: "error",
          });
        }
      }
    } catch (error) {
      console.error("Erreur lors de la prise de photo:", error);
      showToast("Erreur lors de la prise de photo", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const showPhotoOptions = () => {
    if (Platform.OS === "web") {
      // Sur web, ouvrir directement la galerie
      handlePhotoFromGallery();
    } else {
      // Sur mobile, afficher les options caméra/galerie
      Alert.alert(
        "Choisir une photo",
        "Sélectionnez la source de votre photo de profil",
        [
          { text: "Annuler", style: "cancel" },
          { text: "Galerie", onPress: handlePhotoFromGallery },
          { text: "Caméra", onPress: handlePhotoFromCamera },
        ]
      );
    }
  };

  const currentAvatarData = avatarService.getAvatarType(currentAvatar);

  const renderContent = () => (
    <View className="p-6">
      <Text className="text-xl font-bold mb-4 text-center">
        Choisir un avatar
      </Text>

      {/* Avatar actuel */}
      {currentAvatar && (
        <View className="items-center mb-6">
          <Text className="text-sm text-muted-foreground mb-2">
            Avatar actuel
          </Text>
          <View className="w-16 h-16 rounded-full bg-muted items-center justify-center">
            {currentAvatarData.type === "emoji" ? (
              <Text className="text-3xl">{currentAvatarData.value}</Text>
            ) : (
              <Text className="text-2xl">🖼️</Text>
            )}
          </View>
        </View>
      )}

      {/* Options */}
      <View className="gap-3">
        <Card>
          <CardContent className="p-4">
            <Button
              variant="ghost"
              className="w-full flex-row items-center justify-start gap-3 h-auto p-3"
              onPress={handleRandomEmoji}
              disabled={loading}
            >
              <Text className="text-2xl">🎲</Text>
              <View className="flex-1">
                <Text className="font-medium">Emoji aléatoire</Text>
                <Text className="text-sm text-muted-foreground">
                  Générer un emoji de visage au hasard
                </Text>
              </View>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <Button
              variant="ghost"
              className="w-full flex-row items-center justify-start gap-3 h-auto p-3"
              onPress={handleCustomEmoji}
              disabled={loading}
            >
              <Text className="text-2xl">😊</Text>
              <View className="flex-1">
                <Text className="font-medium">Choisir un emoji</Text>
                <Text className="text-sm text-muted-foreground">
                  Sélectionner un emoji personnalisé
                </Text>
              </View>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <Button
              variant="ghost"
              className="w-full flex-row items-center justify-start gap-3 h-auto p-3"
              onPress={showPhotoOptions}
              disabled={loading}
            >
              <Text className="text-2xl">📷</Text>
              <View className="flex-1">
                <Text className="font-medium">Photo de profil</Text>
                <Text className="text-sm text-muted-foreground">
                  {Platform.OS === "web"
                    ? "Choisir une image depuis votre ordinateur"
                    : "Prendre une photo ou choisir depuis la galerie"}
                </Text>
              </View>
              {loading && <ActivityIndicator size="small" className="ml-2" />}
            </Button>
          </CardContent>
        </Card>
      </View>

      {/* Bouton Annuler */}
      <Button
        variant="outline"
        className="w-full mt-6"
        onPress={onClose}
        disabled={loading}
      >
        <Text>Annuler</Text>
      </Button>
    </View>
  );

  // EmojiPicker Modal
  const renderEmojiPicker = () => {
    if (Platform.OS === "web") {
      return (
        <Dialog open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
          <DialogContent className="p-0 w-auto max-w-md bg-transparent border-none shadow-none z-[999999999]">
            <EmojiPicker
              onEmojiSelected={handleEmojiSelected}
              faceEmojisOnly={true}
            />
          </DialogContent>
        </Dialog>
      );
    }

    return (
      <Modal
        visible={showEmojiPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowEmojiPicker(false)}
      >
        <View className="flex-1 justify-center items-center bg-black/50 p-4">
          <View className="bg-background rounded-lg w-full max-w-md max-h-[85%]">
            <View className="flex-row justify-between items-center p-4 border-b border-border">
              <Text className="text-lg font-semibold">Choisir un emoji</Text>
              <Button variant="ghost" onPress={() => setShowEmojiPicker(false)}>
                <Text>✕</Text>
              </Button>
            </View>
            <EmojiPicker
              onEmojiSelected={handleEmojiSelected}
              faceEmojisOnly={true}
            />
          </View>
        </View>
      </Modal>
    );
  };

  // Main Modal
  if (Platform.OS === "web") {
    return (
      <>
        <Dialog open={isVisible} onOpenChange={onClose}>
          <DialogContent className="max-w-md">{renderContent()}</DialogContent>
        </Dialog>
        {renderEmojiPicker()}
      </>
    );
  }

  return (
    <>
      <Modal
        visible={isVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={onClose}
      >
        <View className="flex-1 justify-center items-center bg-black/50 p-4">
          <View className="bg-background rounded-lg w-full max-w-md">
            {renderContent()}
          </View>
        </View>
      </Modal>
      {renderEmojiPicker()}
    </>
  );
}
