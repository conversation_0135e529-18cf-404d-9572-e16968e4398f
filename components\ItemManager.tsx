import React, { useState } from "react";
import { View, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { showToast } from "~/lib/toastService";
import { Item, CostEnum, EffortEnum } from "~/lib/types";

interface ItemManagerProps {
  items: Item[];
  onItemsChange: (items: Item[]) => void;
  eventId: number;
  isOrganizer?: boolean;
  className?: string;
}

const CATEGORIES = [
  { value: "food", label: "🍕 Nourriture", emoji: "🍕" },
  { value: "drinks", label: "🥤 Boissons", emoji: "🥤" },
  { value: "equipment", label: "🎵 Matériel", emoji: "🎵" },
  { value: "decoration", label: "🎈 Décoration", emoji: "🎈" },
  { value: "other", label: "📦 Autre", emoji: "📦" },
];

const COST_OPTIONS = [
  { value: CostEnum.Cheap, label: "€ Pas cher" },
  { value: CostEnum.Medium, label: "€€ Moyen" },
  { value: CostEnum.Expensive, label: "€€€ Cher" },
];

const EFFORT_OPTIONS = [
  { value: EffortEnum.Low, label: "🟢 Facile" },
  { value: EffortEnum.Medium, label: "🟡 Moyen" },
  { value: EffortEnum.High, label: "🔴 Difficile" },
];

export function ItemManager({
  items,
  onItemsChange,
  eventId,
  isOrganizer = false,
  className = "",
}: ItemManagerProps) {
  const [newItemName, setNewItemName] = useState("");
  const [newItemCategory, setNewItemCategory] = useState("other");
  const [newItemDescription, setNewItemDescription] = useState("");
  const [newItemCost, setNewItemCost] = useState<CostEnum | null>(null);
  const [newItemEffort, setNewItemEffort] = useState<EffortEnum | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  const addItem = () => {
    const name = newItemName.trim();
    if (!name) {
      showToast("Veuillez entrer un nom pour l'item.", { type: "error" });
      return;
    }

    // Créer un nouvel item temporaire (l'ID sera assigné par la base de données)
    const newItem: Item = {
      id: Date.now(), // ID temporaire
      event_id: eventId,
      suggester_id: null,
      name,
      category: newItemCategory,
      estimated_cost: newItemCost,
      estimated_effort: newItemEffort,
      is_suggestion: !isOrganizer,
      is_personal: false,
      assigned_participant_id: null,
      fixed_by_participant_id: null,
      completed: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    try {
      onItemsChange([...items, newItem]);
      
      // Reset du formulaire
      setNewItemName("");
      setNewItemCategory("other");
      setNewItemDescription("");
      setNewItemCost(null);
      setNewItemEffort(null);
      setShowAddForm(false);
      
      showToast("Item ajouté !", { type: "success" });
    } catch (error) {
      console.error("Error adding item:", error);
      showToast("Erreur lors de l'ajout de l'item.", { type: "error" });
    }
  };

  const removeItem = (itemId: number) => {
    try {
      onItemsChange(items.filter((item) => item.id !== itemId));
      showToast("Item supprimé !", { type: "success" });
    } catch (error) {
      console.error("Error removing item:", error);
      showToast("Erreur lors de la suppression.", { type: "error" });
    }
  };

  const getCategoryEmoji = (category: string | null) => {
    const cat = CATEGORIES.find(c => c.value === category);
    return cat?.emoji || "📦";
  };

  const getCostDisplay = (cost: CostEnum | null) => {
    if (!cost) return "";
    const option = COST_OPTIONS.find(o => o.value === cost);
    return option?.label || "";
  };

  const getEffortDisplay = (effort: EffortEnum | null) => {
    if (!effort) return "";
    const option = EFFORT_OPTIONS.find(o => o.value === effort);
    return option?.label || "";
  };

  const groupedItems = CATEGORIES.reduce((acc, category) => {
    acc[category.value] = items.filter(item => item.category === category.value);
    return acc;
  }, {} as Record<string, Item[]>);

  return (
    <View className={`gap-4 ${className}`}>
      {/* Header avec statistiques */}
      <Card>
        <CardHeader>
          <CardTitle className="flex-row items-center justify-between">
            <Text className="text-lg font-semibold">Items à apporter</Text>
            <Text className="text-sm text-muted-foreground">
              {items.length} item{items.length > 1 ? "s" : ""}
            </Text>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Text className="text-muted-foreground mb-4">
            Gérez la liste des items nécessaires pour votre événement
          </Text>
          
          {!showAddForm ? (
            <Button
              onPress={() => setShowAddForm(true)}
              className="w-full"
            >
              <Text className="text-primary-foreground">
                ➕ Ajouter un item
              </Text>
            </Button>
          ) : (
            <View className="gap-4 p-4 bg-muted/50 rounded-lg">
              <View>
                <Label className="mb-2 font-medium">
                  <Text className="text-destructive mr-1">*</Text>
                  <Text>Nom de l'item</Text>
                </Label>
                <Input
                  placeholder="Ex: Salade de fruits"
                  value={newItemName}
                  onChangeText={setNewItemName}
                  className="h-11"
                />
              </View>

              <View>
                <Label className="mb-2 font-medium">Catégorie</Label>
                <Select value={newItemCategory} onValueChange={setNewItemCategory}>
                  <SelectTrigger className="h-11">
                    <SelectValue placeholder="Choisir une catégorie" />
                  </SelectTrigger>
                  <SelectContent>
                    {CATEGORIES.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </View>

              <View className="flex-row gap-3">
                <View className="flex-1">
                  <Label className="mb-2 font-medium">Coût estimé</Label>
                  <Select value={newItemCost || ""} onValueChange={(value) => setNewItemCost(value as CostEnum)}>
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder="Coût" />
                    </SelectTrigger>
                    <SelectContent>
                      {COST_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </View>

                <View className="flex-1">
                  <Label className="mb-2 font-medium">Difficulté</Label>
                  <Select value={newItemEffort || ""} onValueChange={(value) => setNewItemEffort(value as EffortEnum)}>
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder="Effort" />
                    </SelectTrigger>
                    <SelectContent>
                      {EFFORT_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </View>
              </View>

              <View className="flex-row gap-3">
                <Button
                  onPress={() => setShowAddForm(false)}
                  variant="outline"
                  className="flex-1"
                >
                  <Text>Annuler</Text>
                </Button>
                <Button
                  onPress={addItem}
                  className="flex-1"
                >
                  <Text className="text-primary-foreground">Ajouter</Text>
                </Button>
              </View>
            </View>
          )}
        </CardContent>
      </Card>

      {/* Liste des items par catégorie */}
      {CATEGORIES.map((category) => {
        const categoryItems = groupedItems[category.value];
        if (categoryItems.length === 0) return null;

        return (
          <Card key={category.value}>
            <CardHeader>
              <CardTitle className="flex-row items-center">
                <Text className="text-lg mr-2">{category.emoji}</Text>
                <Text className="text-lg font-medium">{category.label.split(' ').slice(1).join(' ')}</Text>
                <Text className="text-sm text-muted-foreground ml-2">
                  ({categoryItems.length})
                </Text>
              </CardTitle>
            </CardHeader>
            <CardContent className="gap-3">
              {categoryItems.map((item) => (
                <View
                  key={item.id}
                  className="flex-row items-center justify-between p-3 bg-background border border-border rounded-lg"
                >
                  <View className="flex-1 mr-3">
                    <Text className="font-medium text-foreground">{item.name}</Text>
                    <View className="flex-row items-center gap-2 mt-1">
                      {item.estimated_cost && (
                        <Text className="text-xs text-muted-foreground">
                          {getCostDisplay(item.estimated_cost)}
                        </Text>
                      )}
                      {item.estimated_effort && (
                        <Text className="text-xs text-muted-foreground">
                          {getEffortDisplay(item.estimated_effort)}
                        </Text>
                      )}
                      {item.assigned_participant_id && (
                        <Text className="text-xs text-green-600">✓ Assigné</Text>
                      )}
                    </View>
                  </View>
                  
                  <Button
                    onPress={() => removeItem(item.id)}
                    variant="ghost"
                    size="sm"
                    className="w-8 h-8 p-0"
                  >
                    <Text className="text-destructive text-lg">×</Text>
                  </Button>
                </View>
              ))}
            </CardContent>
          </Card>
        );
      })}

      {items.length === 0 && (
        <Card>
          <CardContent className="py-8">
            <View className="items-center gap-3">
              <Text className="text-4xl">📝</Text>
              <Text className="text-lg font-medium text-foreground">
                Aucun item ajouté
              </Text>
              <Text className="text-muted-foreground text-center">
                Commencez par ajouter les items nécessaires pour votre événement
              </Text>
            </View>
          </CardContent>
        </Card>
      )}
    </View>
  );
}
