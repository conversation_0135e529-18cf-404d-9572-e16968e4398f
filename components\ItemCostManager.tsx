import React, { useState } from "react";
import { View, Alert, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Badge } from "~/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Item, Participant } from "~/lib/types";
import { updateItemCost } from "~/lib/supabaseCrud";
import { formatCurrency, validateAmount, estimatedCostToNumber } from "~/lib/financialCalculator";
import { showToast } from "~/lib/toastService";

interface ItemCostManagerProps {
  item: Item;
  participants: Participant[];
  onUpdate: (updatedItem: Item) => void;
  isOrganizer?: boolean;
}

export function ItemCostManager({ 
  item, 
  participants, 
  onUpdate, 
  isOrganizer = false 
}: ItemCostManagerProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [actualCost, setActualCost] = useState(
    item.actual_cost?.toString() || ""
  );
  const [paidByParticipantId, setPaidByParticipantId] = useState<string>(
    item.paid_by_participant_id?.toString() || ""
  );
  const [loading, setLoading] = useState(false);

  const acceptedParticipants = participants.filter(p => p.status === 'accepted');
  
  const estimatedCostValue = estimatedCostToNumber(item.estimated_cost);
  const hasActualCost = item.actual_cost !== null && item.actual_cost > 0;
  const paidByParticipant = participants.find(p => p.id === item.paid_by_participant_id);

  const handleSave = async () => {
    const costValue = parseFloat(actualCost);
    
    // Validation
    const validation = validateAmount(costValue);
    if (!validation.isValid) {
      Alert.alert("Erreur", validation.error);
      return;
    }

    if (!paidByParticipantId) {
      Alert.alert("Erreur", "Veuillez sélectionner qui a payé cet item");
      return;
    }

    setLoading(true);
    try {
      const updatedItem = await updateItemCost(
        item.id,
        costValue,
        parseInt(paidByParticipantId)
      );

      if (updatedItem) {
        onUpdate(updatedItem);
        setIsEditing(false);
        showToast("Coût mis à jour avec succès", { type: "success" });
      } else {
        showToast("Erreur lors de la mise à jour", { type: "error" });
      }
    } catch (error) {
      console.error("Error updating item cost:", error);
      showToast("Erreur lors de la mise à jour", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setActualCost(item.actual_cost?.toString() || "");
    setPaidByParticipantId(item.paid_by_participant_id?.toString() || "");
    setIsEditing(false);
  };

  const handleStartEdit = () => {
    if (!isOrganizer) {
      showToast("Seul l'organisateur peut modifier les coûts", { type: "error" });
      return;
    }
    setIsEditing(true);
  };

  const getCostDisplay = () => {
    if (hasActualCost) {
      return formatCurrency(item.actual_cost!);
    }
    if (estimatedCostValue > 0) {
      return `~${formatCurrency(estimatedCostValue)} (estimé)`;
    }
    return "Non défini";
  };

  const getCostBadgeVariant = () => {
    if (hasActualCost) return "default";
    if (estimatedCostValue > 0) return "secondary";
    return "outline";
  };

  if (isEditing) {
    return (
      <Card className="mb-4">
        <CardHeader>
          <CardTitle className="text-base">
            Modifier le coût - {item.name}
          </CardTitle>
        </CardHeader>
        <CardContent className="gap-4">
          {/* Coût réel */}
          <View>
            <Label htmlFor={`cost-${item.id}`}>
              Coût réel (€)
            </Label>
            <Input
              id={`cost-${item.id}`}
              placeholder="Ex: 15.50"
              value={actualCost}
              onChangeText={setActualCost}
              keyboardType="numeric"
              className="mt-1"
            />
            {item.estimated_cost && (
              <Text className="text-xs text-muted-foreground mt-1">
                Estimation initiale : {item.estimated_cost} 
                (~{formatCurrency(estimatedCostValue)})
              </Text>
            )}
          </View>

          {/* Qui a payé */}
          <View>
            <Label>Payé par</Label>
            <Select
              value={paidByParticipantId}
              onValueChange={setPaidByParticipantId}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Sélectionner un participant" />
              </SelectTrigger>
              <SelectContent>
                {acceptedParticipants.map((participant) => (
                  <SelectItem 
                    key={participant.id} 
                    value={participant.id.toString()}
                  >
                    {participant.name_display}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </View>

          {/* Boutons d'action */}
          <View className="flex-row gap-3 mt-2">
            <Button
              onPress={handleSave}
              disabled={loading}
              className="flex-1"
            >
              <Text className="text-primary-foreground">
                {loading ? "Sauvegarde..." : "💾 Sauvegarder"}
              </Text>
            </Button>
            
            <Button
              variant="outline"
              onPress={handleCancel}
              disabled={loading}
              className="flex-1"
            >
              <Text>❌ Annuler</Text>
            </Button>
          </View>
        </CardContent>
      </Card>
    );
  }

  return (
    <View className="flex-row items-center justify-between p-3 bg-background border border-border rounded-lg mb-2">
      <View className="flex-1 mr-3">
        <Text className="font-medium text-foreground">{item.name}</Text>
        
        <View className="flex-row items-center gap-2 mt-1">
          <Badge variant={getCostBadgeVariant()}>
            <Text className="text-xs">
              {getCostDisplay()}
            </Text>
          </Badge>
          
          {item.category && (
            <Text className="text-xs text-muted-foreground">
              {item.category}
            </Text>
          )}
        </View>

        {paidByParticipant && (
          <Text className="text-xs text-blue-600 dark:text-blue-400 mt-1">
            Payé par {paidByParticipant.name_display}
          </Text>
        )}
      </View>

      {isOrganizer && (
        <Button
          variant="outline"
          size="sm"
          onPress={handleStartEdit}
          className="ml-2"
        >
          <Text className="text-xs">
            {hasActualCost ? "✏️ Modifier" : "💰 Ajouter coût"}
          </Text>
        </Button>
      )}
    </View>
  );
}

// Composant pour afficher la liste des items avec gestion des coûts
interface ItemCostListProps {
  items: Item[];
  participants: Participant[];
  onItemUpdate: (updatedItem: Item) => void;
  isOrganizer?: boolean;
}

export function ItemCostList({ 
  items, 
  participants, 
  onItemUpdate, 
  isOrganizer = false 
}: ItemCostListProps) {
  const itemsWithCosts = items.filter(item => 
    item.actual_cost !== null || item.estimated_cost !== null
  );
  
  const totalActualCost = items.reduce((sum, item) => 
    sum + (item.actual_cost || 0), 0
  );
  
  const totalEstimatedCost = items.reduce((sum, item) => 
    sum + (item.actual_cost || estimatedCostToNumber(item.estimated_cost)), 0
  );

  if (items.length === 0) {
    return (
      <Card>
        <CardContent className="items-center p-6">
          <Text className="text-muted-foreground">
            Aucun item à afficher
          </Text>
        </CardContent>
      </Card>
    );
  }

  return (
    <View>
      {/* Résumé des coûts */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle className="text-base">Résumé des coûts</CardTitle>
        </CardHeader>
        <CardContent className="gap-2">
          <View className="flex-row justify-between">
            <Text className="text-sm text-muted-foreground">
              Coûts confirmés :
            </Text>
            <Text className="text-sm font-medium">
              {formatCurrency(totalActualCost)}
            </Text>
          </View>
          
          <View className="flex-row justify-between">
            <Text className="text-sm text-muted-foreground">
              Total estimé :
            </Text>
            <Text className="text-sm text-muted-foreground">
              {formatCurrency(totalEstimatedCost)}
            </Text>
          </View>
          
          <View className="flex-row justify-between">
            <Text className="text-sm text-muted-foreground">
              Items avec coût :
            </Text>
            <Text className="text-sm">
              {items.filter(i => i.actual_cost).length} / {items.length}
            </Text>
          </View>
        </CardContent>
      </Card>

      {/* Liste des items */}
      <View>
        {items.map((item) => (
          <ItemCostManager
            key={item.id}
            item={item}
            participants={participants}
            onUpdate={onItemUpdate}
            isOrganizer={isOrganizer}
          />
        ))}
      </View>
    </View>
  );
}
