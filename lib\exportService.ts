/**
 * Service d'export de données pour Party Organizer
 * Génère des rapports et exports pour les organisateurs
 */

import { Platform, Share } from 'react-native';
import { OrganizerDashboard } from './analyticsService';
import { Event, Participant, Item } from './types';
import { supabase } from './supabase';

export interface ExportOptions {
  format: 'csv' | 'json' | 'text';
  includeFinancials: boolean;
  includeParticipants: boolean;
  includeItems: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface ExportResult {
  content: string;
  filename: string;
  mimeType: string;
  size: number;
}

class ExportService {
  /**
   * Exporte le tableau de bord complet
   */
  async exportDashboard(
    dashboard: OrganizerDashboard,
    options: ExportOptions = {
      format: 'text',
      includeFinancials: true,
      includeParticipants: true,
      includeItems: true,
    }
  ): Promise<ExportResult> {
    try {
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `dashboard_${timestamp}.${options.format}`;

      let content: string;
      let mimeType: string;

      switch (options.format) {
        case 'csv':
          content = this.generateDashboardCSV(dashboard, options);
          mimeType = 'text/csv';
          break;
        case 'json':
          content = this.generateDashboardJSON(dashboard, options);
          mimeType = 'application/json';
          break;
        case 'text':
        default:
          content = this.generateDashboardText(dashboard, options);
          mimeType = 'text/plain';
          break;
      }

      return {
        content,
        filename,
        mimeType,
        size: new Blob([content]).size,
      };
    } catch (error) {
      console.error('Error exporting dashboard:', error);
      throw error;
    }
  }

  /**
   * Exporte les données d'un événement spécifique
   */
  async exportEvent(
    eventId: number,
    options: ExportOptions = {
      format: 'text',
      includeFinancials: true,
      includeParticipants: true,
      includeItems: true,
    }
  ): Promise<ExportResult> {
    try {
      // Récupérer les données de l'événement
      const { data: event, error: eventError } = await supabase
        .from('events')
        .select(`
          *,
          participants(*),
          items(*),
          financial_summaries(*)
        `)
        .eq('id', eventId)
        .single();

      if (eventError || !event) {
        throw new Error('Événement non trouvé');
      }

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `event_${event.title.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.${options.format}`;

      let content: string;
      let mimeType: string;

      switch (options.format) {
        case 'csv':
          content = this.generateEventCSV(event, options);
          mimeType = 'text/csv';
          break;
        case 'json':
          content = this.generateEventJSON(event, options);
          mimeType = 'application/json';
          break;
        case 'text':
        default:
          content = this.generateEventText(event, options);
          mimeType = 'text/plain';
          break;
      }

      return {
        content,
        filename,
        mimeType,
        size: new Blob([content]).size,
      };
    } catch (error) {
      console.error('Error exporting event:', error);
      throw error;
    }
  }

  /**
   * Génère un rapport de dashboard en format texte
   */
  private generateDashboardText(dashboard: OrganizerDashboard, options: ExportOptions): string {
    const lines: string[] = [];
    
    lines.push('📊 RAPPORT TABLEAU DE BORD PARTY ORGANIZER');
    lines.push('=' .repeat(50));
    lines.push(`Généré le : ${new Date().toLocaleString('fr-FR')}`);
    lines.push(`Dernière mise à jour : ${dashboard.lastUpdated.toLocaleString('fr-FR')}`);
    lines.push('');

    // Statistiques d'événements
    lines.push('🎉 STATISTIQUES D\'ÉVÉNEMENTS');
    lines.push('-'.repeat(30));
    lines.push(`Total événements : ${dashboard.eventStats.totalEvents}`);
    lines.push(`Événements à venir : ${dashboard.eventStats.upcomingEvents}`);
    lines.push(`Événements passés : ${dashboard.eventStats.pastEvents}`);
    lines.push(`Participants moyens : ${dashboard.eventStats.averageParticipants.toFixed(1)}`);
    lines.push(`Taux de présence : ${dashboard.eventStats.participationRate}%`);
    lines.push('');

    // Statistiques financières
    if (options.includeFinancials) {
      lines.push('💰 STATISTIQUES FINANCIÈRES');
      lines.push('-'.repeat(30));
      lines.push(`Budget total : ${dashboard.financialStats.totalBudget.toFixed(2)}€`);
      lines.push(`Total dépensé : ${dashboard.financialStats.totalSpent.toFixed(2)}€`);
      lines.push(`Coût moyen/événement : ${dashboard.financialStats.averageCostPerEvent.toFixed(2)}€`);
      lines.push(`Coût moyen/participant : ${dashboard.financialStats.averageCostPerParticipant.toFixed(2)}€`);
      lines.push(`Taux d'économies : ${dashboard.financialStats.savingsRate.toFixed(1)}%`);
      lines.push(`Catégorie la plus coûteuse : ${dashboard.financialStats.mostExpensiveCategory}`);
      lines.push('');
    }

    // Statistiques participants
    if (options.includeParticipants) {
      lines.push('👥 STATISTIQUES PARTICIPANTS');
      lines.push('-'.repeat(30));
      lines.push(`Participants uniques : ${dashboard.participantStats.totalUniqueParticipants}`);
      lines.push(`Participants moyens/événement : ${dashboard.participantStats.averageParticipantsPerEvent.toFixed(1)}`);
      lines.push(`Taux de fidélité : ${dashboard.participantStats.participantRetentionRate}%`);
      lines.push('');
    }

    // Statistiques items
    if (options.includeItems) {
      lines.push('📝 STATISTIQUES ITEMS');
      lines.push('-'.repeat(30));
      lines.push(`Total items : ${dashboard.itemStats.totalItems}`);
      lines.push(`Items complétés : ${dashboard.itemStats.completedItems}`);
      lines.push(`Taux de completion : ${dashboard.itemStats.completionRate.toFixed(1)}%`);
      lines.push('');

      if (dashboard.itemStats.mostPopularCategories.length > 0) {
        lines.push('Catégories populaires :');
        dashboard.itemStats.mostPopularCategories.forEach((cat, index) => {
          lines.push(`  ${index + 1}. ${cat.category} (${cat.count} items, ${cat.averageCost.toFixed(2)}€ moy.)`);
        });
        lines.push('');
      }
    }

    // Insights
    if (dashboard.insights.length > 0) {
      lines.push('💡 INSIGHTS');
      lines.push('-'.repeat(30));
      dashboard.insights.forEach((insight, index) => {
        lines.push(`${index + 1}. ${insight}`);
      });
      lines.push('');
    }

    // Recommandations
    if (dashboard.recommendations.length > 0) {
      lines.push('🎯 RECOMMANDATIONS');
      lines.push('-'.repeat(30));
      dashboard.recommendations.forEach((rec, index) => {
        lines.push(`${index + 1}. ${rec}`);
      });
      lines.push('');
    }

    // Alertes
    if (dashboard.alerts.length > 0) {
      lines.push('🚨 ALERTES');
      lines.push('-'.repeat(30));
      dashboard.alerts.forEach((alert, index) => {
        lines.push(`${index + 1}. ${alert}`);
      });
      lines.push('');
    }

    lines.push('');
    lines.push('Généré par Party Organizer');
    lines.push('https://party-organizer.app');

    return lines.join('\n');
  }

  /**
   * Génère un rapport de dashboard en format JSON
   */
  private generateDashboardJSON(dashboard: OrganizerDashboard, options: ExportOptions): string {
    const exportData: any = {
      metadata: {
        exportDate: new Date().toISOString(),
        lastUpdated: dashboard.lastUpdated.toISOString(),
        options,
      },
      eventStats: dashboard.eventStats,
    };

    if (options.includeFinancials) {
      exportData.financialStats = dashboard.financialStats;
    }

    if (options.includeParticipants) {
      exportData.participantStats = dashboard.participantStats;
    }

    if (options.includeItems) {
      exportData.itemStats = dashboard.itemStats;
    }

    exportData.timeStats = dashboard.timeStats;
    exportData.insights = dashboard.insights;
    exportData.recommendations = dashboard.recommendations;
    exportData.alerts = dashboard.alerts;

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Génère un rapport de dashboard en format CSV
   */
  private generateDashboardCSV(dashboard: OrganizerDashboard, options: ExportOptions): string {
    const lines: string[] = [];
    
    // En-tête
    lines.push('Type,Métrique,Valeur,Unité');
    
    // Statistiques d'événements
    lines.push(`Événements,Total,${dashboard.eventStats.totalEvents},nombre`);
    lines.push(`Événements,À venir,${dashboard.eventStats.upcomingEvents},nombre`);
    lines.push(`Événements,Passés,${dashboard.eventStats.pastEvents},nombre`);
    lines.push(`Événements,Participants moyens,${dashboard.eventStats.averageParticipants.toFixed(1)},nombre`);
    lines.push(`Événements,Taux présence,${dashboard.eventStats.participationRate},pourcentage`);

    // Statistiques financières
    if (options.includeFinancials) {
      lines.push(`Finances,Budget total,${dashboard.financialStats.totalBudget.toFixed(2)},euros`);
      lines.push(`Finances,Total dépensé,${dashboard.financialStats.totalSpent.toFixed(2)},euros`);
      lines.push(`Finances,Coût moyen événement,${dashboard.financialStats.averageCostPerEvent.toFixed(2)},euros`);
      lines.push(`Finances,Coût moyen participant,${dashboard.financialStats.averageCostPerParticipant.toFixed(2)},euros`);
      lines.push(`Finances,Taux économies,${dashboard.financialStats.savingsRate.toFixed(1)},pourcentage`);
    }

    // Statistiques participants
    if (options.includeParticipants) {
      lines.push(`Participants,Uniques,${dashboard.participantStats.totalUniqueParticipants},nombre`);
      lines.push(`Participants,Moyens par événement,${dashboard.participantStats.averageParticipantsPerEvent.toFixed(1)},nombre`);
      lines.push(`Participants,Taux fidélité,${dashboard.participantStats.participantRetentionRate},pourcentage`);
    }

    // Statistiques items
    if (options.includeItems) {
      lines.push(`Items,Total,${dashboard.itemStats.totalItems},nombre`);
      lines.push(`Items,Complétés,${dashboard.itemStats.completedItems},nombre`);
      lines.push(`Items,Taux completion,${dashboard.itemStats.completionRate.toFixed(1)},pourcentage`);
    }

    return lines.join('\n');
  }

  /**
   * Génère un rapport d'événement en format texte
   */
  private generateEventText(event: any, options: ExportOptions): string {
    const lines: string[] = [];
    
    lines.push(`🎉 RAPPORT ÉVÉNEMENT : ${event.title}`);
    lines.push('=' .repeat(50));
    lines.push(`Généré le : ${new Date().toLocaleString('fr-FR')}`);
    lines.push('');

    // Informations générales
    lines.push('📋 INFORMATIONS GÉNÉRALES');
    lines.push('-'.repeat(30));
    lines.push(`Titre : ${event.title}`);
    lines.push(`Description : ${event.description || 'Aucune'}`);
    lines.push(`Date : ${new Date(event.date_time).toLocaleString('fr-FR')}`);
    lines.push(`Lieu : ${event.location || 'Non spécifié'}`);
    lines.push(`Icône : ${event.icon}`);
    lines.push('');

    // Participants
    if (options.includeParticipants && event.participants) {
      lines.push('👥 PARTICIPANTS');
      lines.push('-'.repeat(30));
      lines.push(`Total : ${event.participants.length}`);
      event.participants.forEach((participant: any, index: number) => {
        lines.push(`${index + 1}. ${participant.name_display || participant.anonymous_name} (${participant.role})`);
      });
      lines.push('');
    }

    // Items
    if (options.includeItems && event.items) {
      lines.push('📝 ITEMS');
      lines.push('-'.repeat(30));
      lines.push(`Total : ${event.items.length}`);
      const completed = event.items.filter((item: any) => item.completed).length;
      lines.push(`Complétés : ${completed} (${((completed / event.items.length) * 100).toFixed(1)}%)`);
      lines.push('');
      
      event.items.forEach((item: any, index: number) => {
        const status = item.completed ? '✅' : '⏳';
        const cost = item.actual_cost ? ` - ${item.actual_cost}€` : '';
        lines.push(`${status} ${index + 1}. ${item.name} (${item.category || 'Autre'})${cost}`);
      });
      lines.push('');
    }

    // Finances
    if (options.includeFinancials && event.financial_summaries) {
      lines.push('💰 RÉSUMÉ FINANCIER');
      lines.push('-'.repeat(30));
      event.financial_summaries.forEach((summary: any) => {
        lines.push(`Participant : ${summary.participant_name || 'Inconnu'}`);
        lines.push(`  Total payé : ${summary.total_paid || 0}€`);
        lines.push(`  Doit recevoir : ${summary.total_owed || 0}€`);
        lines.push(`  Balance : ${(summary.total_paid || 0) - (summary.total_owed || 0)}€`);
        lines.push('');
      });
    }

    lines.push('Généré par Party Organizer');
    lines.push('https://party-organizer.app');

    return lines.join('\n');
  }

  /**
   * Génère un rapport d'événement en format JSON
   */
  private generateEventJSON(event: any, options: ExportOptions): string {
    const exportData: any = {
      metadata: {
        exportDate: new Date().toISOString(),
        options,
      },
      event: {
        id: event.id,
        title: event.title,
        description: event.description,
        date_time: event.date_time,
        location: event.location,
        icon: event.icon,
      },
    };

    if (options.includeParticipants && event.participants) {
      exportData.participants = event.participants;
    }

    if (options.includeItems && event.items) {
      exportData.items = event.items;
    }

    if (options.includeFinancials && event.financial_summaries) {
      exportData.financialSummaries = event.financial_summaries;
    }

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Génère un rapport d'événement en format CSV
   */
  private generateEventCSV(event: any, options: ExportOptions): string {
    const lines: string[] = [];
    
    // En-tête pour les items
    if (options.includeItems && event.items) {
      lines.push('Type,Nom,Catégorie,Statut,Coût,Assigné à');
      event.items.forEach((item: any) => {
        const status = item.completed ? 'Complété' : 'En cours';
        const cost = item.actual_cost || '';
        const assignee = item.assigned_participant_name || '';
        lines.push(`Item,"${item.name}","${item.category || ''}","${status}","${cost}","${assignee}"`);
      });
    }

    return lines.join('\n');
  }

  /**
   * Partage le rapport via les options natives
   */
  async shareReport(exportResult: ExportResult): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Sur web, télécharger le fichier
        this.downloadFile(exportResult);
      } else {
        // Sur mobile, utiliser l'API de partage native
        await Share.share({
          message: exportResult.content,
          title: `Rapport Party Organizer - ${exportResult.filename}`,
        });
      }
    } catch (error) {
      console.error('Error sharing report:', error);
      throw error;
    }
  }

  /**
   * Télécharge un fichier sur web
   */
  private downloadFile(exportResult: ExportResult): void {
    if (Platform.OS !== 'web') return;

    const blob = new Blob([exportResult.content], { type: exportResult.mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = exportResult.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }
}

// Instance singleton
export const exportService = new ExportService();

// Fonctions utilitaires
export const exportData = {
  dashboard: (dashboard: OrganizerDashboard, options?: ExportOptions) => 
    exportService.exportDashboard(dashboard, options),
  event: (eventId: number, options?: ExportOptions) => 
    exportService.exportEvent(eventId, options),
  share: (exportResult: ExportResult) => 
    exportService.shareReport(exportResult),
};
