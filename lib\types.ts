// --- Begin lib/types.ts ---

// Enum definitions based on SQL ENUM types
export enum CostEnum {
  Cheap = "€",
  Medium = "€€",
  Expensive = "€€€",
}

export enum EffortEnum {
  Low = "1",
  Medium = "2",
  High = "3",
}

// Interface for the 'profiles' table
export interface Profile {
  id: string; // UUID PRIMARY KEY REFERENCES auth.users(id)
  name: string | null;
  avatar_url: string | null;
  is_discoverable?: boolean; // Added
  updated_at: string; // TIMESTAMPTZ
  created_at: string; // TIMESTAMPTZ
}

// Interface for the 'events' table
export interface Event {
  id: number; // BIGINT PRIMARY KEY
  organizer_id: string; // UUID NOT NULL REFERENCES profiles(id)
  icon: string | null;
  title: string; // NOT NULL
  description: string | null;
  date_time: string; // TIMESTAMPTZ NOT NULL
  location: string | null;
  allow_suggestions: boolean; // DEFAULT FALSE NOT NULL
  allow_pre_assignment: boolean; // DEFAULT FALSE NOT NULL ('Fixer')
  organizer_delegated: boolean; // DEFAULT FALSE NOT NULL
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ
}

// Interface for the 'participants' table
export interface Participant {
  id: number; // BIGINT PRIMARY KEY
  event_id: number; // BIGINT NOT NULL REFERENCES events(id)
  user_id: string | null; // UUID NULL REFERENCES profiles(id)
  anonymous_name: string | null; // TEXT
  anonymous_email: string | null; // TEXT
  anonymous_phone: string | null; // TEXT
  role: "organizer" | "guest"; // participant_role NOT NULL DEFAULT 'guest'
  status: "pending" | "accepted" | "declined" | "maybe"; // participant_status NOT NULL DEFAULT 'pending'
  invitation_token: string | null; // TEXT UNIQUE
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ

  // Potentially add related data when fetching (like profile name/avatar)
  profiles?: Pick<Profile, "name" | "avatar_url"> | null; // For joined data
  name_display?: string; // Helper to display name (either profile name or anonymous name)
}

// Interface for the 'items' table
export interface Item {
  id: number; // BIGINT PRIMARY KEY
  event_id: number; // BIGINT NOT NULL REFERENCES events(id)
  suggester_id: string | null; // UUID NULL REFERENCES profiles(id)
  name: string; // TEXT NOT NULL
  category: string | null;
  estimated_cost: CostEnum | null; // cost_enum
  estimated_effort: EffortEnum | null; // effort_enum
  is_suggestion: boolean; // DEFAULT FALSE NOT NULL
  is_personal: boolean; // DEFAULT FALSE NOT NULL
  assigned_participant_id: number | null; // BIGINT NULL REFERENCES participants(id)
  fixed_by_participant_id: number | null; // BIGINT NULL REFERENCES participants(id)
  completed: boolean; // DEFAULT FALSE NOT NULL
  actual_cost: number | null; // DECIMAL(10,2) - Coût réel en euros
  paid_by_participant_id: number | null; // BIGINT NULL REFERENCES participants(id)
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ

  // Potentially add related data when fetching
  assigned_participant?: Participant | null; // For joined data
  fixed_by_participant?: Participant | null; // For joined data
  suggester?: Profile | null; // For joined data
  event?: Event | null; // Correction: Ajout de l'événement lié possiblement joint
}

// Interface for the 'contacts' table
export interface Contact {
  id: number; // BIGINT PRIMARY KEY
  user_id: string; // UUID NOT NULL REFERENCES profiles(id)
  contact_profile_id: string | null; // UUID NULL REFERENCES profiles(id)
  name: string; // TEXT NOT NULL
  email: string | null;
  phone: string | null;
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ

  // Potentially add related data when fetching
  contact_profile?: Pick<Profile, "name" | "avatar_url"> | null; // For joined data
}

// Interface for the 'contact_groups' table
export interface ContactGroup {
  id: number; // BIGINT PRIMARY KEY
  user_id: string; // UUID NOT NULL REFERENCES profiles(id)
  name: string; // TEXT NOT NULL
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ
}

// Interface for the 'contact_group_members' table
export interface ContactGroupMember {
  group_id: number; // BIGINT NOT NULL REFERENCES contact_groups(id)
  contact_id: number; // BIGINT NOT NULL REFERENCES contacts(id)
}

// Interface for the 'messages' table
export interface Message {
  id: number; // BIGINT PRIMARY KEY
  event_id: number; // BIGINT NOT NULL REFERENCES events(id)
  sender_id: string; // UUID NOT NULL REFERENCES profiles(id)
  content: string; // TEXT NOT NULL
  created_at: string; // TIMESTAMPTZ

  // Potentially add related data when fetching
  sender?: Pick<Profile, "name" | "avatar_url"> | null; // For joined data
}

// Helper type for combined Event data with participant info
export interface EventWithParticipantStatus extends Event {
  participant_status?: "pending" | "accepted" | "declined" | "maybe" | null;
  participant_role?: "organizer" | "guest" | null;
  is_organizer: boolean;
}

// Interface for the 'financial_transactions' table
export interface FinancialTransaction {
  id: number; // BIGINT PRIMARY KEY
  event_id: number; // BIGINT NOT NULL REFERENCES events(id)
  from_participant_id: number; // BIGINT NOT NULL REFERENCES participants(id)
  to_participant_id: number; // BIGINT NOT NULL REFERENCES participants(id)
  amount: number; // DECIMAL(10,2) NOT NULL
  description: string | null; // TEXT
  is_settled: boolean; // BOOLEAN DEFAULT FALSE
  settled_at: string | null; // TIMESTAMPTZ
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ

  // Related data when fetching
  from_participant?: Participant | null;
  to_participant?: Participant | null;
}

// Interface for the 'financial_summaries' table
export interface FinancialSummary {
  id: number; // BIGINT PRIMARY KEY
  event_id: number; // BIGINT NOT NULL REFERENCES events(id)
  participant_id: number; // BIGINT NOT NULL REFERENCES participants(id)
  total_spent: number; // DECIMAL(10,2) - Total dépensé par ce participant
  total_owed: number; // DECIMAL(10,2) - Part équitable que ce participant doit
  net_balance: number; // DECIMAL(10,2) - Solde net (positif = doit recevoir, négatif = doit payer)
  last_calculated_at: string; // TIMESTAMPTZ

  // Related data when fetching
  participant?: Participant | null;
}

// Interface pour les calculs de remboursement optimisés
export interface SettlementSuggestion {
  from_participant: Participant;
  to_participant: Participant;
  amount: number;
  description: string;
}

// Interface pour le résumé financier d'un événement
export interface EventFinancialSummary {
  event_id: number;
  total_cost: number; // Coût total de l'événement
  cost_per_person: number; // Coût moyen par personne
  participants_count: number; // Nombre de participants
  items_with_cost: number; // Nombre d'items avec coût réel
  settlement_suggestions: SettlementSuggestion[]; // Suggestions de remboursement
  participant_summaries: FinancialSummary[]; // Résumés par participant
}

// Types pour les insertions
export type FinancialTransactionInsert = Omit<
  FinancialTransaction,
  "id" | "created_at" | "updated_at"
>;
export type FinancialSummaryInsert = Omit<
  FinancialSummary,
  "id" | "last_calculated_at"
>;

// Enum pour les types de transactions
export enum TransactionType {
  EXPENSE = "expense", // Dépense initiale
  SETTLEMENT = "settlement", // Remboursement entre participants
}

// Interface pour les statistiques financières
export interface FinancialStats {
  total_events_with_costs: number;
  total_amount_managed: number;
  average_cost_per_event: number;
  most_expensive_category: string;
  settlement_completion_rate: number; // Pourcentage de remboursements effectués
}

// Interface for the 'event_templates' table
export interface EventTemplate {
  id: number; // BIGINT PRIMARY KEY
  name: string; // TEXT NOT NULL
  description: string | null; // TEXT
  icon: string; // TEXT DEFAULT '🎉'
  category: "predefined" | "custom" | "community"; // TEXT NOT NULL
  created_by: string | null; // UUID REFERENCES auth.users(id)
  is_public: boolean; // BOOLEAN DEFAULT FALSE
  usage_count: number; // INTEGER DEFAULT 0
  template_data: EventTemplateData; // JSONB NOT NULL
  created_at: string; // TIMESTAMPTZ
  updated_at: string; // TIMESTAMPTZ

  // Related data when fetching
  template_items?: TemplateItem[];
}

// Interface for the 'template_items' table
export interface TemplateItem {
  id: number; // BIGINT PRIMARY KEY
  template_id: number; // BIGINT NOT NULL REFERENCES event_templates(id)
  name: string; // TEXT NOT NULL
  category: string | null; // TEXT
  estimated_cost: CostEnum | null; // TEXT ('€', '€€', '€€€')
  estimated_effort: EffortEnum | null; // TEXT ('easy', 'medium', 'hard')
  is_essential: boolean; // BOOLEAN DEFAULT FALSE
  suggested_quantity: number; // INTEGER DEFAULT 1
  description: string | null; // TEXT
  order_index: number; // INTEGER DEFAULT 0
  created_at: string; // TIMESTAMPTZ
}

// Structure des données du template
export interface EventTemplateData {
  title_template: string; // Template du titre (ex: "Soirée {theme}")
  description_template?: string; // Template de description
  default_time: string; // Heure par défaut (ex: "19:00")
  default_duration?: number; // Durée en heures
  suggested_location_types?: string[]; // Types de lieux suggérés
  min_participants?: number; // Nombre minimum de participants
  max_participants?: number; // Nombre maximum de participants
  preparation_time?: number; // Temps de préparation en jours
  tags?: string[]; // Tags pour la recherche
}

// Types pour les insertions de templates
export type EventTemplateInsert = Omit<
  EventTemplate,
  "id" | "created_at" | "updated_at" | "usage_count"
>;
export type TemplateItemInsert = Omit<TemplateItem, "id" | "created_at">;

// Interface pour la création d'événement depuis un template
export interface EventFromTemplate {
  template: EventTemplate;
  customizations: {
    title?: string;
    description?: string;
    date?: string;
    time?: string;
    location?: string;
    selected_items?: number[]; // IDs des items sélectionnés
    custom_items?: Omit<TemplateItem, "id" | "template_id" | "created_at">[]; // Items personnalisés
  };
}

// --- End lib/types.ts ---
