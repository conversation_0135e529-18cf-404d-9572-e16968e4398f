import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform, RefreshControl } from "react-native";
import { useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { useAuth } from "~/lib/AuthContext";
import { analytics, OrganizerDashboard } from "~/lib/analyticsService";
import { showToast } from "~/lib/toastService";
import { 
  EventStatsWidget,
  FinancialStatsWidget,
  ParticipantStatsWidget,
  ItemStatsWidget,
  TimeStatsWidget
} from "~/components/dashboard/DashboardWidgets";
import { InsightsPanel } from "~/components/dashboard/InsightsPanel";
import { NetworkStatusIndicator } from "~/components/NetworkStatusIndicator";

export default function DashboardScreen() {
  const { session } = useAuth();
  const router = useRouter();
  const [dashboard, setDashboard] = useState<OrganizerDashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'insights' | 'trends'>('overview');

  useEffect(() => {
    if (session?.user?.id) {
      loadDashboard();
    }
  }, [session?.user?.id]);

  const loadDashboard = async () => {
    if (!session?.user?.id) return;

    try {
      setLoading(true);
      const dashboardData = await analytics.generateDashboard(session.user.id);
      setDashboard(dashboardData);
    } catch (error) {
      console.error('Error loading dashboard:', error);
      showToast('Erreur lors du chargement du tableau de bord', { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    if (!session?.user?.id) return;

    try {
      setRefreshing(true);
      // Invalider le cache et recharger
      await analytics.invalidateDashboard(session.user.id);
      await loadDashboard();
      showToast('Tableau de bord actualisé', { type: 'success' });
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
      showToast('Erreur lors de l\'actualisation', { type: 'error' });
    } finally {
      setRefreshing(false);
    }
  };

  const handleInsightAction = (action: string) => {
    switch (action) {
      case 'create_event':
        router.push('/create-event-with-template');
        break;
      case 'view_templates':
        router.push('/create-event-with-template');
        break;
      case 'view_upcoming_events':
        router.push('/(tabs)/');
        break;
      case 'export_data':
        handleExportData();
        break;
      case 'share_achievement':
        handleShareAchievement();
        break;
      default:
        showToast(`Action "${action}" en cours de développement`, { type: 'info' });
    }
  };

  const handleExportData = () => {
    // Simuler l'export de données
    showToast('Export des données en cours...', { type: 'info' });
    // TODO: Implémenter l'export réel
  };

  const handleShareAchievement = () => {
    // Simuler le partage d'achievement
    showToast('Partage en cours...', { type: 'info' });
    // TODO: Implémenter le partage réel
  };

  if (!session?.user?.id) {
    return (
      <View className="flex-1 justify-center items-center bg-background p-6">
        <Text className="text-lg font-semibold mb-2">
          Connexion requise
        </Text>
        <Text className="text-muted-foreground text-center mb-4">
          Connectez-vous pour accéder à votre tableau de bord organisateur
        </Text>
        <Button onPress={() => router.push('/auth')}>
          <Text className="text-primary-foreground">Se connecter</Text>
        </Button>
      </View>
    );
  }

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-muted-foreground">
          Génération du tableau de bord...
        </Text>
      </View>
    );
  }

  if (!dashboard) {
    return (
      <View className="flex-1 justify-center items-center bg-background p-6">
        <Text className="text-4xl mb-4">📊</Text>
        <Text className="text-lg font-semibold mb-2">
          Erreur de chargement
        </Text>
        <Text className="text-muted-foreground text-center mb-4">
          Impossible de charger votre tableau de bord
        </Text>
        <Button onPress={loadDashboard}>
          <Text className="text-primary-foreground">Réessayer</Text>
        </Button>
      </View>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: '📊' },
    { id: 'insights', label: 'Insights', icon: '💡' },
    { id: 'trends', label: 'Tendances', icon: '📈' },
  ] as const;

  return (
    <ScrollView 
      className="flex-1 bg-background"
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      <View className={Platform.OS === "web" ? "max-w-6xl mx-auto p-4" : "p-4"}>
        
        {/* Header */}
        <View className="mb-6">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="text-2xl font-bold">
              📊 Tableau de bord
            </Text>
            <NetworkStatusIndicator showDetails={false} />
          </View>
          <Text className="text-muted-foreground">
            Vue d'ensemble de vos événements et performances
          </Text>
          <Text className="text-xs text-muted-foreground mt-1">
            Dernière mise à jour : {dashboard.lastUpdated.toLocaleString('fr-FR')}
          </Text>
        </View>

        {/* Onglets */}
        <View className="flex-row mb-6 bg-muted rounded-lg p-1">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={selectedTab === tab.id ? "default" : "ghost"}
              size="sm"
              onPress={() => setSelectedTab(tab.id)}
              className="flex-1"
            >
              <Text className={selectedTab === tab.id ? "text-primary-foreground" : "text-foreground"}>
                {tab.icon} {tab.label}
              </Text>
            </Button>
          ))}
        </View>

        {/* Contenu selon l'onglet sélectionné */}
        {selectedTab === 'overview' && (
          <View className="gap-6">
            {/* Résumé rapide */}
            <Card>
              <CardHeader>
                <CardTitle>🎯 Résumé rapide</CardTitle>
              </CardHeader>
              <CardContent>
                <View className="flex-row flex-wrap gap-2">
                  <Badge variant="secondary">
                    <Text className="text-xs">
                      {dashboard.eventStats.totalEvents} événements
                    </Text>
                  </Badge>
                  <Badge variant="secondary">
                    <Text className="text-xs">
                      {dashboard.participantStats.totalUniqueParticipants} participants
                    </Text>
                  </Badge>
                  <Badge variant="secondary">
                    <Text className="text-xs">
                      {dashboard.financialStats.totalSpent.toFixed(0)}€ dépensés
                    </Text>
                  </Badge>
                  <Badge variant="secondary">
                    <Text className="text-xs">
                      {dashboard.itemStats.completionRate.toFixed(0)}% completion
                    </Text>
                  </Badge>
                </View>
              </CardContent>
            </Card>

            {/* Widgets statistiques */}
            <EventStatsWidget stats={dashboard.eventStats} />
            <FinancialStatsWidget stats={dashboard.financialStats} />
            <ParticipantStatsWidget stats={dashboard.participantStats} />
            <ItemStatsWidget stats={dashboard.itemStats} />
          </View>
        )}

        {selectedTab === 'insights' && (
          <InsightsPanel
            insights={dashboard.insights}
            recommendations={dashboard.recommendations}
            alerts={dashboard.alerts}
            onActionTaken={handleInsightAction}
          />
        )}

        {selectedTab === 'trends' && (
          <View className="gap-6">
            <TimeStatsWidget stats={dashboard.timeStats} />
            
            {/* Graphiques de tendances (placeholder) */}
            <Card>
              <CardHeader>
                <CardTitle>📈 Évolution dans le temps</CardTitle>
              </CardHeader>
              <CardContent className="items-center p-6">
                <Text className="text-4xl mb-3">📊</Text>
                <Text className="text-lg font-semibold mb-2">
                  Graphiques en développement
                </Text>
                <Text className="text-muted-foreground text-center">
                  Les graphiques de tendances seront bientôt disponibles pour visualiser l'évolution de vos métriques.
                </Text>
              </CardContent>
            </Card>
          </View>
        )}

        {/* Actions rapides en bas */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>🚀 Actions rapides</CardTitle>
          </CardHeader>
          <CardContent>
            <View className="flex-row flex-wrap gap-3">
              <Button
                variant="outline"
                onPress={() => router.push('/create-event-with-template')}
                className="flex-1 min-w-[120px]"
              >
                <Text>➕ Nouvel événement</Text>
              </Button>
              
              <Button
                variant="outline"
                onPress={() => router.push('/(tabs)/')}
                className="flex-1 min-w-[120px]"
              >
                <Text>📅 Mes événements</Text>
              </Button>
              
              <Button
                variant="outline"
                onPress={handleRefresh}
                disabled={refreshing}
                className="flex-1 min-w-[120px]"
              >
                <Text>
                  {refreshing ? '🔄 Actualisation...' : '🔄 Actualiser'}
                </Text>
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Footer avec conseils */}
        <Card className="mt-6 border-dashed border-2 border-muted">
          <CardContent className="p-4">
            <View className="flex-row items-start">
              <Text className="text-2xl mr-3">💡</Text>
              <View className="flex-1">
                <Text className="font-semibold mb-1">
                  Optimisez vos événements
                </Text>
                <Text className="text-sm text-muted-foreground">
                  Utilisez les insights et recommandations pour améliorer l'organisation de vos prochains événements.
                </Text>
              </View>
            </View>
          </CardContent>
        </Card>
      </View>
    </ScrollView>
  );
}
