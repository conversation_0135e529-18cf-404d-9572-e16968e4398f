import React, { useState } from "react";
import { View } from "react-native";
import { useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { EventTemplate, EventFromTemplate } from "~/lib/types";
import { TemplateSelector } from "~/components/TemplateSelector";
import { TemplateCustomizer } from "~/components/TemplateCustomizer";
import {
  generateEventFromTemplate,
  generateItemsFromTemplate,
  incrementTemplateUsage,
} from "~/lib/templateService";
import { createEvent, createItem } from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";

type Step = "select" | "customize";

export default function CreateEventWithTemplateScreen() {
  const router = useRouter();
  const { session } = useAuth();
  const [currentStep, setCurrentStep] = useState<Step>("select");
  const [selectedTemplate, setSelectedTemplate] =
    useState<EventTemplate | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSelectTemplate = (template: EventTemplate) => {
    setSelectedTemplate(template);
    setCurrentStep("customize");
  };

  const handleCreateFromScratch = () => {
    // Rediriger vers la page de création classique
    router.replace("/create-event");
  };

  const handleBackToSelection = () => {
    setCurrentStep("select");
    setSelectedTemplate(null);
  };

  const handleConfirmTemplate = async (
    eventFromTemplate: EventFromTemplate
  ) => {
    if (!session?.user?.id || !selectedTemplate) {
      showToast("Erreur d'authentification", { type: "error" });
      return;
    }

    setLoading(true);
    try {
      // Générer les données de l'événement
      const eventData = generateEventFromTemplate(
        selectedTemplate,
        eventFromTemplate.customizations
      );

      // Créer l'événement
      const newEvent = await createEvent({
        ...eventData,
        organizer_id: session.user.id,
        icon: eventFromTemplate.customizations.title
          ? selectedTemplate.icon
          : eventData.icon || "🎉",
      });

      if (!newEvent) {
        throw new Error("Impossible de créer l'événement");
      }

      // Générer et créer les items
      const items = generateItemsFromTemplate(
        selectedTemplate,
        eventFromTemplate.customizations.selected_items,
        eventFromTemplate.customizations.custom_items
      );

      // Créer les items en parallèle
      const itemPromises = items.map((item) =>
        createItem({
          ...item,
          event_id: newEvent.id,
          suggester_id: session.user.id,
        })
      );

      await Promise.all(itemPromises);

      // Incrémenter le compteur d'utilisation du template
      await incrementTemplateUsage(selectedTemplate.id);

      showToast(`Événement "${newEvent.title}" créé avec succès !`, {
        type: "success",
      });

      // Rediriger vers l'événement créé
      router.replace(`/event/${newEvent.id}`);
    } catch (error) {
      console.error("Error creating event from template:", error);
      showToast("Erreur lors de la création de l'événement", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <Text className="text-lg font-semibold mb-2">
          Création de votre événement...
        </Text>
        <Text className="text-muted-foreground text-center">
          {selectedTemplate
            ? `Utilisation du template "${selectedTemplate.name}"`
            : "Préparation de l'événement"}
        </Text>
      </View>
    );
  }

  switch (currentStep) {
    case "select":
      return (
        <TemplateSelector
          onSelectTemplate={handleSelectTemplate}
          onCreateFromScratch={handleCreateFromScratch}
        />
      );

    case "customize":
      if (!selectedTemplate) {
        // Fallback au cas où il y aurait un problème
        setCurrentStep("select");
        return null;
      }

      return (
        <TemplateCustomizer
          template={selectedTemplate}
          onConfirm={handleConfirmTemplate}
          onBack={handleBackToSelection}
        />
      );

    default:
      return null;
  }
}
