/**
 * Service de gestion des avatars pour Party Organizer
 * Gère les photos de profil, emojis aléatoires et stockage Supabase
 */

import { Platform } from "react-native";
import * as ImagePicker from "expo-image-picker";
import * as FileSystem from "expo-file-system";
import { supabase, supabaseAdmin } from "./supabase";
import { showToast } from "./toastService";

// Types pour les avatars
export interface AvatarData {
  type: "emoji" | "photo";
  value: string; // URL pour photo, emoji pour emoji
  isRandom?: boolean; // Si c'est un emoji généré aléatoirement
}

export interface AvatarUploadResult {
  success: boolean;
  avatarUrl?: string;
  error?: string;
}

// Emojis de visages pour génération aléatoire
const FACE_EMOJIS = [
  "😀",
  "😃",
  "😄",
  "😁",
  "😆",
  "😅",
  "😂",
  "🤣",
  "😊",
  "😇",
  "🙂",
  "🙃",
  "😉",
  "😌",
  "😍",
  "🥰",
  "😘",
  "😗",
  "😙",
  "😚",
  "😋",
  "😛",
  "😝",
  "😜",
  "🤪",
  "🤨",
  "🧐",
  "🤓",
  "😎",
  "🤩",
  "🥳",
  "😏",
  "😒",
  "😞",
  "😔",
  "😟",
  "😕",
  "🙁",
  "☹️",
  "😣",
  "😖",
  "😫",
  "😩",
  "🥺",
  "😢",
  "😭",
  "😤",
  "😠",
  "😡",
  "🤬",
  "🤯",
  "😳",
  "🥵",
  "🥶",
  "😱",
  "😨",
  "😰",
  "😥",
  "😓",
  "🤗",
  "🤔",
  "🤭",
  "🤫",
  "🤥",
  "😶",
  "😐",
  "😑",
  "😬",
  "🙄",
  "😯",
  "😦",
  "😧",
  "😮",
  "😲",
  "🥱",
  "😴",
  "🤤",
  "😪",
  "😵",
  "🤐",
  "🥴",
  "🤢",
  "🤮",
  "🤧",
  "😷",
  "🤒",
  "🤕",
  "🤑",
  "🤠",
  "😈",
  "👿",
  "👹",
  "👺",
  "🤡",
  "💩",
  "👻",
  "💀",
  "☠️",
  "👽",
  "👾",
  "🤖",
  "🎃",
  "😺",
  "😸",
  "😹",
  "😻",
  "😼",
  "😽",
  "🙀",
  "😿",
  "😾",
  "👶",
  "👧",
  "🧒",
  "👦",
  "👩",
  "🧑",
  "👨",
  "👵",
  "🧓",
  "👴",
  "👲",
  "👳‍♀️",
  "👳‍♂️",
  "🧕",
  "👮‍♀️",
  "👮‍♂️",
  "👷‍♀️",
  "👷‍♂️",
  "💂‍♀️",
  "💂‍♂️",
  "🕵️‍♀️",
  "🕵️‍♂️",
  "👩‍⚕️",
  "👨‍⚕️",
  "👩‍🌾",
  "👨‍🌾",
  "👩‍🍳",
  "👨‍🍳",
  "👩‍🎓",
  "👨‍🎓",
  "👩‍🎤",
  "👨‍🎤",
  "👩‍🏫",
  "👨‍🏫",
  "👩‍🏭",
  "👨‍🏭",
  "👩‍💻",
  "👨‍💻",
  "👩‍💼",
  "👨‍💼",
  "👩‍🔧",
  "👨‍🔧",
  "👩‍🔬",
  "👨‍🔬",
  "👩‍🎨",
  "👨‍🎨",
  "👩‍🚒",
  "👨‍🚒",
  "👩‍✈️",
  "👨‍✈️",
  "👩‍🚀",
  "👨‍🚀",
  "👩‍⚖️",
  "👨‍⚖️",
  "👰",
  "🤵",
  "👸",
  "🤴",
  "🦸‍♀️",
  "🦸‍♂️",
  "🦹‍♀️",
  "🦹‍♂️",
  "🧙‍♀️",
  "🧙‍♂️",
  "🧚‍♀️",
  "🧚‍♂️",
  "🧛‍♀️",
  "🧛‍♂️",
  "🧜‍♀️",
  "🧜‍♂️",
  "🧝‍♀️",
  "🧝‍♂️",
  "🧞‍♀️",
  "🧞‍♂️",
  "🧟‍♀️",
  "🧟‍♂️",
  "💆‍♀️",
  "💆‍♂️",
  "💇‍♀️",
  "💇‍♂️",
  "🚶‍♀️",
  "🚶‍♂️",
  "🏃‍♀️",
  "🏃‍♂️",
];

class AvatarService {
  private static instance: AvatarService;
  private bucketName = "public-avatars";

  static getInstance(): AvatarService {
    if (!AvatarService.instance) {
      AvatarService.instance = new AvatarService();
    }
    return AvatarService.instance;
  }

  /**
   * Initialise le bucket avatars si nécessaire
   */
  async initializeBucket(): Promise<void> {
    try {
      // Vérifier si le bucket existe
      const { data: buckets, error: listError } =
        await supabaseAdmin.storage.listBuckets();

      if (listError) {
        console.warn("Erreur lors de la vérification des buckets:", listError);
        return;
      }

      const bucketExists = buckets?.some(
        (bucket) => bucket.name === this.bucketName
      );

      if (!bucketExists) {
        // Créer le bucket
        const { error: createError } = await supabaseAdmin.storage.createBucket(
          this.bucketName,
          {
            public: true,
            allowedMimeTypes: ["image/jpeg", "image/png", "image/webp"],
            fileSizeLimit: 5 * 1024 * 1024, // 5MB
          }
        );

        if (createError) {
          console.error("Erreur lors de la création du bucket:", createError);
          return;
        }

        console.log("Bucket avatars créé avec succès");
      }
    } catch (error) {
      console.error("Erreur lors de l'initialisation du bucket:", error);
    }
  }

  /**
   * Génère un emoji de visage aléatoire
   */
  generateRandomFaceEmoji(): string {
    const randomIndex = Math.floor(Math.random() * FACE_EMOJIS.length);
    return FACE_EMOJIS[randomIndex];
  }

  /**
   * Demande les permissions pour accéder à la galerie/caméra
   */
  async requestPermissions(): Promise<boolean> {
    try {
      if (Platform.OS !== "web") {
        const { status: cameraStatus } =
          await ImagePicker.requestCameraPermissionsAsync();
        const { status: mediaStatus } =
          await ImagePicker.requestMediaLibraryPermissionsAsync();

        if (cameraStatus !== "granted" || mediaStatus !== "granted") {
          showToast(
            "Permissions requises pour accéder à la caméra et à la galerie",
            {
              type: "error",
            }
          );
          return false;
        }
      }
      return true;
    } catch (error) {
      console.error("Erreur lors de la demande de permissions:", error);
      return false;
    }
  }

  /**
   * Ouvre la galerie pour sélectionner une image
   */
  async pickImageFromGallery(): Promise<ImagePicker.ImagePickerResult | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) return null;

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1], // Carré
        quality: 0.8,
        base64: false,
      });

      return result;
    } catch (error) {
      console.error("Erreur lors de la sélection d'image:", error);
      showToast("Erreur lors de la sélection de l'image", { type: "error" });
      return null;
    }
  }

  /**
   * Ouvre la caméra pour prendre une photo
   */
  async takePhotoWithCamera(): Promise<ImagePicker.ImagePickerResult | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) return null;

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1], // Carré
        quality: 0.8,
        base64: false,
      });

      return result;
    } catch (error) {
      console.error("Erreur lors de la prise de photo:", error);
      showToast("Erreur lors de la prise de photo", { type: "error" });
      return null;
    }
  }

  /**
   * Upload une image vers Supabase Storage
   */
  async uploadImage(
    imageUri: string,
    userId: string
  ): Promise<AvatarUploadResult> {
    try {
      // Vérification de sécurité : s'assurer que l'utilisateur est authentifié
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return {
          success: false,
          error: "Utilisateur non authentifié",
        };
      }

      // Vérifier que l'utilisateur correspond à l'ID fourni
      if (user.id !== userId) {
        console.warn(
          `Tentative d'upload non autorisée: ${user.id} vs ${userId}`
        );
        return {
          success: false,
          error: "Utilisateur non autorisé pour cet upload",
        };
      }

      console.log(`Upload autorisé pour l'utilisateur: ${userId}`);

      // Ne pas essayer de créer le bucket à chaque upload
      // await this.initializeBucket();

      // Générer un nom de fichier unique avec structure de dossier
      // Format attendu par les politiques RLS: userId/filename
      const timestamp = Date.now();
      let fileExtension = "jpg";
      let fileName = `${userId}/avatar_${timestamp}.${fileExtension}`;

      let fileData: any;
      let contentType = "image/jpeg";

      if (Platform.OS === "web") {
        // Sur web, convertir l'URI en blob
        try {
          const response = await fetch(imageUri);
          fileData = await response.blob();
          contentType = fileData.type || "image/jpeg";

          // Extraire l'extension du type MIME
          if (contentType.includes("png")) {
            fileExtension = "png";
          } else if (contentType.includes("webp")) {
            fileExtension = "webp";
          } else {
            fileExtension = "jpg";
          }
          fileName = `${userId}/avatar_${timestamp}.${fileExtension}`;
        } catch (fetchError) {
          console.error(
            "Erreur lors de la récupération du fichier:",
            fetchError
          );
          return {
            success: false,
            error: "Impossible de récupérer le fichier sélectionné",
          };
        }
      } else {
        // Sur mobile, lire le fichier
        try {
          const fileInfo = await FileSystem.getInfoAsync(imageUri);
          if (!fileInfo.exists) {
            return {
              success: false,
              error: "Le fichier sélectionné n'existe pas",
            };
          }

          const base64 = await FileSystem.readAsStringAsync(imageUri, {
            encoding: FileSystem.EncodingType.Base64,
          });

          // Convertir base64 en ArrayBuffer
          const byteCharacters = atob(base64);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          fileData = new Uint8Array(byteNumbers);

          // Déterminer le type de contenu basé sur l'URI
          if (imageUri.toLowerCase().includes(".png")) {
            contentType = "image/png";
            fileExtension = "png";
          } else if (imageUri.toLowerCase().includes(".webp")) {
            contentType = "image/webp";
            fileExtension = "webp";
          } else {
            contentType = "image/jpeg";
            fileExtension = "jpg";
          }
          fileName = `${userId}/avatar_${timestamp}.${fileExtension}`;
        } catch (fileError) {
          console.error("Erreur lors de la lecture du fichier:", fileError);
          return {
            success: false,
            error: "Impossible de lire le fichier sélectionné",
          };
        }
      }

      // Vérifier la taille du fichier (limite à 5MB)
      if (fileData.size && fileData.size > 5 * 1024 * 1024) {
        return {
          success: false,
          error: "Le fichier est trop volumineux (maximum 5MB)",
        };
      }

      // Supprimer l'ancien avatar s'il existe
      await this.deleteUserAvatar(userId);

      // Upload du nouveau fichier dans le bucket public (sans RLS)
      console.log(
        `Upload vers bucket public: ${fileName}, taille: ${
          fileData.size || "inconnue"
        }, type: ${contentType}`
      );

      const { data, error } = await supabaseAdmin.storage
        .from(this.bucketName)
        .upload(fileName, fileData, {
          contentType: contentType,
          upsert: true,
        });

      console.log("Résultat upload:", { data, error, fileName });

      if (error) {
        console.error("Erreur lors de l'upload:", error);

        // Message d'erreur plus informatif
        let errorMessage = "Erreur lors de l'upload de l'image";
        if (error.statusCode === "413") {
          errorMessage = "Fichier trop volumineux (maximum 5MB).";
        } else if (error.message) {
          errorMessage = `Erreur: ${error.message}`;
        }

        return {
          success: false,
          error: errorMessage,
        };
      }

      // Obtenir l'URL publique
      const { data: urlData } = supabaseAdmin.storage
        .from(this.bucketName)
        .getPublicUrl(fileName);

      return {
        success: true,
        avatarUrl: urlData.publicUrl,
      };
    } catch (error) {
      console.error("Erreur lors de l'upload d'image:", error);
      return {
        success: false,
        error: "Erreur lors de l'upload de l'image",
      };
    }
  }

  /**
   * Supprime l'avatar d'un utilisateur du storage
   */
  async deleteUserAvatar(userId: string): Promise<void> {
    try {
      // Lister tous les fichiers dans le dossier de l'utilisateur
      const { data: files, error } = await supabaseAdmin.storage
        .from(this.bucketName)
        .list(userId, {
          limit: 100,
        });

      if (error || !files) {
        console.warn("Aucun fichier à supprimer ou erreur:", error);
        return;
      }

      // Supprimer tous les fichiers de l'utilisateur
      const filesToDelete = files.map((file) => `${userId}/${file.name}`);

      if (filesToDelete.length > 0) {
        const { error: deleteError } = await supabaseAdmin.storage
          .from(this.bucketName)
          .remove(filesToDelete);

        if (deleteError) {
          console.warn(
            "Erreur lors de la suppression des anciens avatars:",
            deleteError
          );
        } else {
          console.log(
            `${filesToDelete.length} ancien(s) avatar(s) supprimé(s)`
          );
        }
      }
    } catch (error) {
      console.warn("Erreur lors de la suppression de l'avatar:", error);
    }
  }

  /**
   * Détermine le type d'avatar basé sur l'URL
   */
  getAvatarType(avatarUrl: string | null): AvatarData {
    if (!avatarUrl) {
      return {
        type: "emoji",
        value: this.generateRandomFaceEmoji(),
        isRandom: true,
      };
    }

    // Si c'est une URL (contient http), c'est une photo
    if (avatarUrl.startsWith("http")) {
      return {
        type: "photo",
        value: avatarUrl,
      };
    }

    // Sinon, c'est un emoji
    return {
      type: "emoji",
      value: avatarUrl,
      isRandom: FACE_EMOJIS.includes(avatarUrl),
    };
  }

  /**
   * Filtre les emojis pour ne garder que les visages
   */
  getFaceEmojisFromPicker(allEmojis: any[]): any[] {
    return allEmojis.filter(
      (emoji) =>
        FACE_EMOJIS.includes(emoji.native) ||
        emoji.keywords?.some((keyword: string) =>
          [
            "face",
            "person",
            "people",
            "man",
            "woman",
            "smile",
            "happy",
            "sad",
          ].includes(keyword.toLowerCase())
        )
    );
  }
}

// Export de l'instance singleton
export const avatarService = AvatarService.getInstance();
