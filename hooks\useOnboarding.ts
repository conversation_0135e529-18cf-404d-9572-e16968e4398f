/**
 * Hook personnalisé pour gérer l'onboarding
 * Fournit l'état et les actions pour le processus d'onboarding
 */

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'expo-router';
import { 
  onboarding, 
  OnboardingProfile, 
  OnboardingStep, 
  OnboardingProgress 
} from '~/lib/onboardingService';
import { useAuth } from '~/lib/AuthContext';
import { showToast } from '~/lib/toastService';

export interface OnboardingState {
  profile: OnboardingProfile | null;
  steps: OnboardingStep[];
  progress: OnboardingProgress;
  currentStep: OnboardingStep | null;
  loading: boolean;
  shouldShow: boolean;
  isCompleted: boolean;
}

export interface OnboardingActions {
  initializeOnboarding: () => Promise<void>;
  completeStep: (stepId: string) => Promise<void>;
  skipStep: (stepId: string) => Promise<void>;
  skipOnboarding: () => Promise<void>;
  updateProfile: (updates: Partial<OnboardingProfile>) => Promise<void>;
  goToStep: (stepId: string) => void;
  resetOnboarding: () => Promise<void>;
  refreshState: () => Promise<void>;
}

export function useOnboarding(): OnboardingState & OnboardingActions {
  const { session } = useAuth();
  const router = useRouter();
  
  const [state, setState] = useState<OnboardingState>({
    profile: null,
    steps: [],
    progress: {
      totalSteps: 0,
      completedSteps: 0,
      currentStepIndex: 0,
      progressPercentage: 0,
      isCompleted: false,
    },
    currentStep: null,
    loading: true,
    shouldShow: false,
    isCompleted: false,
  });

  /**
   * Charge l'état complet de l'onboarding
   */
  const loadOnboardingState = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      const [profile, steps, progress, shouldShow] = await Promise.all([
        onboarding.getProfile(),
        onboarding.getSteps(),
        onboarding.getProgress(),
        onboarding.shouldShow(session?.user?.id),
      ]);

      const currentStep = progress.nextStep || null;
      const isCompleted = progress.isCompleted;

      setState({
        profile,
        steps,
        progress,
        currentStep,
        loading: false,
        shouldShow,
        isCompleted,
      });
    } catch (error) {
      console.error('Error loading onboarding state:', error);
      setState(prev => ({ 
        ...prev, 
        loading: false,
        shouldShow: true, // Par défaut, montrer l'onboarding en cas d'erreur
      }));
    }
  }, [session?.user?.id]);

  /**
   * Initialise l'onboarding pour un nouvel utilisateur
   */
  const initializeOnboarding = useCallback(async () => {
    try {
      await onboarding.initialize(session?.user?.id);
      await loadOnboardingState();
      showToast('Onboarding initialisé', { type: 'success' });
    } catch (error) {
      console.error('Error initializing onboarding:', error);
      showToast('Erreur lors de l\'initialisation', { type: 'error' });
    }
  }, [session?.user?.id, loadOnboardingState]);

  /**
   * Marque une étape comme complétée
   */
  const completeStep = useCallback(async (stepId: string) => {
    try {
      await onboarding.completeStep(stepId);
      await loadOnboardingState();
      
      const step = state.steps.find(s => s.id === stepId);
      if (step) {
        showToast(`✅ ${step.title} terminé !`, { type: 'success' });
      }

      // Navigation automatique selon l'étape
      handleStepNavigation(stepId);
    } catch (error) {
      console.error('Error completing step:', error);
      showToast('Erreur lors de la completion', { type: 'error' });
    }
  }, [state.steps, loadOnboardingState]);

  /**
   * Ignore une étape optionnelle
   */
  const skipStep = useCallback(async (stepId: string) => {
    try {
      await onboarding.completeStep(stepId); // Marquer comme complété même si ignoré
      await loadOnboardingState();
      
      showToast('Étape ignorée', { type: 'info' });
    } catch (error) {
      console.error('Error skipping step:', error);
      showToast('Erreur lors de l\'ignorage', { type: 'error' });
    }
  }, [loadOnboardingState]);

  /**
   * Ignore complètement l'onboarding
   */
  const skipOnboarding = useCallback(async () => {
    try {
      await onboarding.skip();
      await loadOnboardingState();
      showToast('Onboarding ignoré', { type: 'info' });
      router.replace('/(tabs)/');
    } catch (error) {
      console.error('Error skipping onboarding:', error);
      showToast('Erreur lors de l\'ignorage', { type: 'error' });
    }
  }, [loadOnboardingState, router]);

  /**
   * Met à jour le profil utilisateur
   */
  const updateProfile = useCallback(async (updates: Partial<OnboardingProfile>) => {
    try {
      await onboarding.updateProfile(updates);
      
      // Personnaliser les étapes selon le type d'utilisateur
      if (updates.userType) {
        await onboarding.personalize(updates.userType);
      }
      
      await loadOnboardingState();
      showToast('Profil mis à jour', { type: 'success' });
    } catch (error) {
      console.error('Error updating profile:', error);
      showToast('Erreur lors de la mise à jour', { type: 'error' });
    }
  }, [loadOnboardingState]);

  /**
   * Navigue vers une étape spécifique
   */
  const goToStep = useCallback((stepId: string) => {
    const step = state.steps.find(s => s.id === stepId);
    if (!step) return;

    handleStepNavigation(stepId);
  }, [state.steps]);

  /**
   * Gère la navigation selon l'étape
   */
  const handleStepNavigation = (stepId: string) => {
    switch (stepId) {
      case 'welcome':
        router.push('/onboarding/welcome');
        break;
      case 'profile_setup':
        router.push('/onboarding/profile');
        break;
      case 'first_event':
        router.push('/create-event-with-template');
        break;
      case 'invite_participants':
        // Naviguer vers le dernier événement créé
        router.push('/(tabs)/');
        break;
      case 'manage_items':
        router.push('/(tabs)/');
        break;
      case 'financial_tracking':
        router.push('/(tabs)/dashboard');
        break;
      case 'notifications':
        router.push('/notifications');
        break;
      case 'dashboard':
        router.push('/(tabs)/dashboard');
        break;
      case 'completion':
        router.push('/onboarding/completion');
        break;
      default:
        router.push('/(tabs)/');
    }
  };

  /**
   * Remet à zéro l'onboarding
   */
  const resetOnboarding = useCallback(async () => {
    try {
      await onboarding.reset();
      await loadOnboardingState();
      showToast('Onboarding remis à zéro', { type: 'info' });
    } catch (error) {
      console.error('Error resetting onboarding:', error);
      showToast('Erreur lors de la remise à zéro', { type: 'error' });
    }
  }, [loadOnboardingState]);

  /**
   * Actualise l'état
   */
  const refreshState = useCallback(async () => {
    await loadOnboardingState();
  }, [loadOnboardingState]);

  // Charger l'état au montage et quand l'utilisateur change
  useEffect(() => {
    loadOnboardingState();
  }, [loadOnboardingState]);

  return {
    ...state,
    initializeOnboarding,
    completeStep,
    skipStep,
    skipOnboarding,
    updateProfile,
    goToStep,
    resetOnboarding,
    refreshState,
  };
}

/**
 * Hook simplifié pour vérifier si l'onboarding doit être affiché
 */
export function useOnboardingCheck() {
  const { session } = useAuth();
  const [shouldShow, setShouldShow] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkOnboarding = async () => {
      try {
        const should = await onboarding.shouldShow(session?.user?.id);
        setShouldShow(should);
      } catch (error) {
        console.error('Error checking onboarding:', error);
        setShouldShow(true); // Par défaut, montrer l'onboarding
      } finally {
        setLoading(false);
      }
    };

    checkOnboarding();
  }, [session?.user?.id]);

  return { shouldShow, loading };
}

/**
 * Hook pour obtenir la progression de l'onboarding
 */
export function useOnboardingProgress() {
  const [progress, setProgress] = useState<OnboardingProgress>({
    totalSteps: 0,
    completedSteps: 0,
    currentStepIndex: 0,
    progressPercentage: 0,
    isCompleted: false,
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProgress = async () => {
      try {
        const prog = await onboarding.getProgress();
        setProgress(prog);
      } catch (error) {
        console.error('Error loading progress:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProgress();
  }, []);

  const refresh = async () => {
    setLoading(true);
    try {
      const prog = await onboarding.getProgress();
      setProgress(prog);
    } catch (error) {
      console.error('Error refreshing progress:', error);
    } finally {
      setLoading(false);
    }
  };

  return { progress, loading, refresh };
}

/**
 * Hook pour les actions rapides d'onboarding
 */
export function useOnboardingActions() {
  const completeStepAction = async (stepId: string) => {
    try {
      await onboarding.completeStep(stepId);
      showToast('Étape complétée !', { type: 'success' });
    } catch (error) {
      console.error('Error completing step:', error);
      showToast('Erreur lors de la completion', { type: 'error' });
    }
  };

  const skipOnboardingAction = async () => {
    try {
      await onboarding.skip();
      showToast('Onboarding ignoré', { type: 'info' });
    } catch (error) {
      console.error('Error skipping onboarding:', error);
      showToast('Erreur lors de l\'ignorage', { type: 'error' });
    }
  };

  return {
    completeStep: completeStepAction,
    skipOnboarding: skipOnboardingAction,
  };
}
