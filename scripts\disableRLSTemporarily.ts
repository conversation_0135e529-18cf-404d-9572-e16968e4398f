/**
 * Script pour désactiver temporairement RLS sur storage.objects
 * ATTENTION: À utiliser uniquement pour les tests !
 */

require('dotenv').config();
const { createClient } = require("@supabase/supabase-js");

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("❌ Variables d'environnement manquantes");
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function disableRLSTemporarily() {
  try {
    console.log("⚠️  DÉSACTIVATION TEMPORAIRE DE RLS POUR LES TESTS");
    console.log("🔧 Cette action est temporaire et pour les tests uniquement !");

    // SQL à exécuter dans Supabase Dashboard
    const sqlCommands = `
-- ATTENTION: Désactivation temporaire de RLS pour les tests
-- À exécuter dans Supabase Dashboard > SQL Editor

-- 1. Supprimer toutes les politiques existantes
DROP POLICY IF EXISTS "Public read access for avatars" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatars" ON storage.objects;

-- 2. Désactiver RLS temporairement sur storage.objects
ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;

-- 3. Vérifier le statut
SELECT 
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE schemaname = 'storage' 
AND tablename = 'objects';

-- 4. Pour réactiver RLS plus tard (NE PAS EXÉCUTER MAINTENANT):
-- ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
`;

    console.log("\n📋 SQL à exécuter dans Supabase Dashboard:");
    console.log("=====================================");
    console.log(sqlCommands);
    console.log("=====================================");

    // Test d'upload pour vérifier
    console.log("\n🧪 Test d'upload après désactivation RLS...");
    
    const testUserId = "test-user-123";
    const testFileName = `${testUserId}/avatar_test.jpg`;
    const testContent = new Uint8Array([255, 216, 255, 224]); // Header JPEG minimal
    
    try {
      const { error: uploadError } = await supabaseAdmin.storage
        .from('avatars')
        .upload(testFileName, testContent, { 
          contentType: 'image/jpeg',
          upsert: true 
        });
        
      if (uploadError) {
        console.log("❌ Erreur d'upload de test:", uploadError);
        console.log("💡 Exécutez le SQL ci-dessus dans Supabase Dashboard");
      } else {
        console.log("✅ Upload de test réussi - RLS semble désactivé");
        
        // Nettoyer
        await supabaseAdmin.storage.from('avatars').remove([testFileName]);
        console.log("✅ Fichier de test nettoyé");
      }
    } catch (error) {
      console.log("❌ Erreur lors du test:", error);
    }

    console.log("\n🎯 Instructions:");
    console.log("1. Copiez le SQL ci-dessus");
    console.log("2. Allez sur: https://supabase.com/dashboard");
    console.log("3. SQL Editor → Nouvelle requête");
    console.log("4. Collez et exécutez SEULEMENT les 3 premières commandes");
    console.log("5. Testez l'upload d'avatar dans l'application");
    console.log("6. Une fois les tests terminés, réactivez RLS avec:");
    console.log("   ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;");

    console.log("\n⚠️  IMPORTANT:");
    console.log("- Cette désactivation est TEMPORAIRE");
    console.log("- À utiliser UNIQUEMENT pour les tests");
    console.log("- Réactivez RLS dès que possible");
    console.log("- En production, utilisez les politiques RLS appropriées");

  } catch (error) {
    console.error("❌ Erreur:", error);
  }
}

// Exécuter le script
if (require.main === module) {
  disableRLSTemporarily()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { disableRLSTemporarily };
