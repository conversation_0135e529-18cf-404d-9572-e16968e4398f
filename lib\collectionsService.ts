/**
 * Service de gestion des collections de badges pour Party Organizer
 * Gère les collections thématiques et les récompenses de completion
 */

import { supabase } from './supabase';
import { cache } from './cacheService';
import { showToast } from './toastService';
import { Badge } from './achievementsService';

export interface BadgeCollection {
  id: number;
  name: string;
  title: string;
  description: string;
  icon: string;
  badge_ids: number[];
  completion_reward_points: number;
  completion_reward_badge_id?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  badges?: Badge[];
}

export interface UserCollectionProgress {
  id: number;
  user_id: string;
  collection_id: number;
  completed_badges: number[];
  is_completed: boolean;
  completed_at?: string;
  created_at: string;
  updated_at: string;
  collection?: BadgeCollection;
}

export interface CollectionStats {
  totalCollections: number;
  completedCollections: number;
  inProgressCollections: number;
  completionRate: number;
  totalBadgesInCollections: number;
  completedBadgesInCollections: number;
  nextCompletableCollection?: {
    collection: BadgeCollection;
    progress: number;
    missing: number;
  };
}

class CollectionsService {
  private readonly CACHE_TTL = 10 * 60 * 1000; // 10 minutes

  /**
   * Collections prédéfinies
   */
  private getDefaultCollections(): Omit<BadgeCollection, 'id' | 'created_at' | 'updated_at' | 'badges'>[] {
    return [
      {
        name: 'first_steps',
        title: 'Premiers Pas',
        description: 'Vos premiers achievements dans Party Organizer',
        icon: '🚀',
        badge_ids: [1, 2, 3, 4, 5], // first_event, first_participant, welcome_aboard, first_item, first_invite
        completion_reward_points: 100,
        is_active: true,
      },
      {
        name: 'organizer_master',
        title: 'Maître Organisateur',
        description: 'Tous les badges d\'organisateur expert',
        icon: '👑',
        badge_ids: [6, 7, 8, 9, 10], // party_starter, event_master, party_legend, perfectionist, efficiency_expert
        completion_reward_points: 500,
        is_active: true,
      },
      {
        name: 'social_butterfly',
        title: 'Papillon Social',
        description: 'Tous les badges de participation sociale',
        icon: '🦋',
        badge_ids: [11, 12, 13], // social_butterfly, party_animal, community_pillar
        completion_reward_points: 300,
        is_active: true,
      },
      {
        name: 'financial_wizard',
        title: 'Magicien des Finances',
        description: 'Maîtrisez la gestion financière',
        icon: '🧙‍♂️',
        badge_ids: [14, 15, 16], // penny_saver, budget_master, financial_wizard
        completion_reward_points: 250,
        is_active: true,
      },
      {
        name: 'level_climber',
        title: 'Grimpeur de Niveaux',
        description: 'Atteignez tous les paliers de niveau',
        icon: '🏔️',
        badge_ids: [17, 18, 19, 20], // rising_star, experienced_organizer, party_guru, legendary_host
        completion_reward_points: 750,
        is_active: true,
      },
      {
        name: 'point_collector',
        title: 'Collectionneur de Points',
        description: 'Accumulez des points comme un pro',
        icon: '💎',
        badge_ids: [21, 22, 23], // point_collector, high_achiever, point_master
        completion_reward_points: 400,
        is_active: true,
      },
      {
        name: 'special_forces',
        title: 'Forces Spéciales',
        description: 'Badges spéciaux et exclusifs',
        icon: '⭐',
        badge_ids: [24, 25, 26, 27], // early_adopter, beta_tester, feedback_hero, bug_hunter
        completion_reward_points: 600,
        is_active: true,
      },
      {
        name: 'team_builder',
        title: 'Bâtisseur d\'Équipe',
        description: 'Excellence en collaboration',
        icon: '🤝',
        badge_ids: [28, 29, 30], // team_player, network_builder, community_leader
        completion_reward_points: 350,
        is_active: true,
      },
      {
        name: 'creative_genius',
        title: 'Génie Créatif',
        description: 'Innovation et créativité',
        icon: '🎨',
        badge_ids: [31, 32, 33], // creative_mind, innovation_master, theme_master
        completion_reward_points: 450,
        is_active: true,
      },
      {
        name: 'consistency_king',
        title: 'Roi de la Régularité',
        description: 'Constance et dévouement',
        icon: '👑',
        badge_ids: [34, 35, 36], // consistent_organizer, dedicated_host, unstoppable_force
        completion_reward_points: 500,
        is_active: true,
      },
      {
        name: 'quality_master',
        title: 'Maître de la Qualité',
        description: 'Excellence et perfection',
        icon: '💯',
        badge_ids: [37, 38, 39], // quality_assurance, excellence_standard, perfection_achieved
        completion_reward_points: 800,
        is_active: true,
      },
      {
        name: 'hidden_treasures',
        title: 'Trésors Cachés',
        description: 'Badges secrets et easter eggs',
        icon: '🗝️',
        badge_ids: [40, 41, 42, 43, 44], // night_owl, party_on_birthday, millennium_organizer, speed_demon, multitasker
        completion_reward_points: 1000,
        is_active: true,
      },
    ];
  }

  /**
   * Initialise les collections par défaut
   */
  async initializeDefaultCollections(): Promise<void> {
    try {
      const defaultCollections = this.getDefaultCollections();

      for (const collection of defaultCollections) {
        // Vérifier si la collection existe déjà
        const { data: existing } = await supabase
          .from('badge_collections')
          .select('id')
          .eq('name', collection.name)
          .single();

        if (!existing) {
          await supabase
            .from('badge_collections')
            .insert(collection);
        }
      }

      // Invalider le cache
      await cache.invalidate('badge_collections');
    } catch (error) {
      console.error('Error initializing default collections:', error);
    }
  }

  /**
   * Récupère toutes les collections actives
   */
  async getCollections(): Promise<BadgeCollection[]> {
    try {
      const cacheKey = 'badge_collections';
      const cached = await cache.get<BadgeCollection[]>(cacheKey);
      
      if (cached) {
        return cached;
      }

      const { data, error } = await supabase
        .from('badge_collections')
        .select('*')
        .eq('is_active', true)
        .order('completion_reward_points', { ascending: true });

      if (error) throw error;

      const collections = data || [];
      await cache.set(cacheKey, collections, {}, this.CACHE_TTL);
      
      return collections;
    } catch (error) {
      console.error('Error fetching collections:', error);
      return [];
    }
  }

  /**
   * Récupère les collections avec les badges détaillés
   */
  async getCollectionsWithBadges(): Promise<BadgeCollection[]> {
    try {
      const collections = await this.getCollections();
      
      // Récupérer tous les badges
      const { data: badges } = await supabase
        .from('badges')
        .select('*')
        .eq('is_active', true);

      const badgesMap = new Map(badges?.map(b => [b.id, b]) || []);

      // Associer les badges aux collections
      return collections.map(collection => ({
        ...collection,
        badges: collection.badge_ids
          .map(id => badgesMap.get(id))
          .filter(Boolean) as Badge[],
      }));
    } catch (error) {
      console.error('Error fetching collections with badges:', error);
      return [];
    }
  }

  /**
   * Récupère la progression des collections d'un utilisateur
   */
  async getUserCollectionProgress(userId: string): Promise<UserCollectionProgress[]> {
    try {
      const cacheKey = 'user_collection_progress';
      const cached = await cache.get<UserCollectionProgress[]>(cacheKey, { userId });
      
      if (cached) {
        return cached;
      }

      const { data, error } = await supabase
        .from('user_collection_progress')
        .select(`
          *,
          collection:badge_collections(*)
        `)
        .eq('user_id', userId)
        .order('completed_at', { ascending: false, nullsLast: true });

      if (error) throw error;

      const progress = data || [];
      await cache.set(cacheKey, progress, { userId }, this.CACHE_TTL);
      
      return progress;
    } catch (error) {
      console.error('Error fetching user collection progress:', error);
      return [];
    }
  }

  /**
   * Vérifie et met à jour la progression des collections
   */
  async checkCollectionCompletion(userId: string): Promise<BadgeCollection[]> {
    try {
      const { data, error } = await supabase.rpc('check_collection_completion', {
        p_user_id: userId,
      });

      if (error) throw error;

      const completedCollections: BadgeCollection[] = [];

      // Récupérer les détails des collections complétées
      for (const completion of data || []) {
        const { data: collection } = await supabase
          .from('badge_collections')
          .select('*')
          .eq('id', completion.collection_id)
          .single();

        if (collection) {
          completedCollections.push(collection);
          await this.rewardCollectionCompletion(userId, collection);
        }
      }

      // Invalider le cache
      await cache.delete('user_collection_progress', { userId });

      return completedCollections;
    } catch (error) {
      console.error('Error checking collection completion:', error);
      return [];
    }
  }

  /**
   * Récompense la completion d'une collection
   */
  private async rewardCollectionCompletion(userId: string, collection: BadgeCollection): Promise<void> {
    try {
      // Ajouter les points de récompense
      if (collection.completion_reward_points > 0) {
        await supabase
          .from('user_stats')
          .update({
            total_points: supabase.raw(`total_points + ${collection.completion_reward_points}`),
          })
          .eq('user_id', userId);
      }

      // Attribuer le badge de récompense si applicable
      if (collection.completion_reward_badge_id) {
        await supabase
          .from('user_achievements')
          .upsert({
            user_id: userId,
            badge_id: collection.completion_reward_badge_id,
            is_completed: true,
            completion_data: {
              collection_id: collection.id,
              collection_name: collection.name,
              completed_at: new Date().toISOString(),
            },
          });
      }

      showToast(
        `🎉 Collection "${collection.title}" complétée ! +${collection.completion_reward_points} points`,
        { type: 'success', duration: 5000 }
      );
    } catch (error) {
      console.error('Error rewarding collection completion:', error);
    }
  }

  /**
   * Calcule les statistiques de collections d'un utilisateur
   */
  async calculateCollectionStats(userId: string): Promise<CollectionStats> {
    try {
      const [collections, userProgress, userBadges] = await Promise.all([
        this.getCollections(),
        this.getUserCollectionProgress(userId),
        supabase
          .from('user_achievements')
          .select('badge_id')
          .eq('user_id', userId)
          .eq('is_completed', true),
      ]);

      const userBadgeIds = new Set(userBadges.data?.map(a => a.badge_id) || []);
      const progressMap = new Map(userProgress.map(p => [p.collection_id, p]));

      const totalCollections = collections.length;
      const completedCollections = userProgress.filter(p => p.is_completed).length;
      const inProgressCollections = collections.filter(c => {
        const progress = progressMap.get(c.id);
        return !progress?.is_completed && c.badge_ids.some(id => userBadgeIds.has(id));
      }).length;

      const completionRate = totalCollections > 0 ? (completedCollections / totalCollections) * 100 : 0;

      const totalBadgesInCollections = collections.reduce((sum, c) => sum + c.badge_ids.length, 0);
      const completedBadgesInCollections = collections.reduce((sum, c) => {
        return sum + c.badge_ids.filter(id => userBadgeIds.has(id)).length;
      }, 0);

      // Trouver la collection la plus proche de la completion
      let nextCompletableCollection: CollectionStats['nextCompletableCollection'];
      let bestProgress = 0;

      for (const collection of collections) {
        const progress = progressMap.get(collection.id);
        if (!progress?.is_completed) {
          const completed = collection.badge_ids.filter(id => userBadgeIds.has(id)).length;
          const progressPercent = (completed / collection.badge_ids.length) * 100;
          
          if (progressPercent > bestProgress && progressPercent > 0) {
            bestProgress = progressPercent;
            nextCompletableCollection = {
              collection,
              progress: progressPercent,
              missing: collection.badge_ids.length - completed,
            };
          }
        }
      }

      return {
        totalCollections,
        completedCollections,
        inProgressCollections,
        completionRate,
        totalBadgesInCollections,
        completedBadgesInCollections,
        nextCompletableCollection,
      };
    } catch (error) {
      console.error('Error calculating collection stats:', error);
      return {
        totalCollections: 0,
        completedCollections: 0,
        inProgressCollections: 0,
        completionRate: 0,
        totalBadgesInCollections: 0,
        completedBadgesInCollections: 0,
      };
    }
  }

  /**
   * Récupère une collection avec la progression utilisateur
   */
  async getCollectionWithProgress(
    collectionId: number, 
    userId: string
  ): Promise<{ collection: BadgeCollection; progress: UserCollectionProgress | null; badges: Badge[] }> {
    try {
      const [collection, progress, badges] = await Promise.all([
        supabase
          .from('badge_collections')
          .select('*')
          .eq('id', collectionId)
          .single(),
        supabase
          .from('user_collection_progress')
          .select('*')
          .eq('collection_id', collectionId)
          .eq('user_id', userId)
          .single(),
        supabase
          .from('badges')
          .select('*')
          .in('id', []), // Will be filled with actual badge IDs
      ]);

      if (collection.error) throw collection.error;

      // Récupérer les badges de la collection
      const { data: collectionBadges } = await supabase
        .from('badges')
        .select('*')
        .in('id', collection.data.badge_ids);

      return {
        collection: collection.data,
        progress: progress.data || null,
        badges: collectionBadges || [],
      };
    } catch (error) {
      console.error('Error fetching collection with progress:', error);
      throw error;
    }
  }
}

// Instance singleton
export const collectionsService = new CollectionsService();

// Fonctions utilitaires
export const collections = {
  initialize: () => collectionsService.initializeDefaultCollections(),
  getAll: () => collectionsService.getCollections(),
  getAllWithBadges: () => collectionsService.getCollectionsWithBadges(),
  getUserProgress: (userId: string) => collectionsService.getUserCollectionProgress(userId),
  checkCompletion: (userId: string) => collectionsService.checkCollectionCompletion(userId),
  calculateStats: (userId: string) => collectionsService.calculateCollectionStats(userId),
  getWithProgress: (collectionId: number, userId: string) => 
    collectionsService.getCollectionWithProgress(collectionId, userId),
};
