# Guide de Style - Party Organizer

Ce document définit les standards visuels et les conventions de style pour l'application Party Organizer.

## 1. Couleurs

La palette de couleurs est définie dans `global.css` en utilisant des variables HSL pour faciliter le theming.

### 1.1. Ajout de la couleur "Warning"

Pour améliorer la différenciation visuelle, nous ajoutons une couleur "warning", principalement utilisée pour attirer l'attention sur des éléments importants comme le badge "Organisateur".

**Action :** Ajouter les variables suivantes dans `global.css`.

Dans la section `:root` :

```css
--warning: 38 92% 50%;
--warning-foreground: 48 96% 89%;
```

Dans la section `.dark:root` :

```css
--warning: 45 93% 47%;
--warning-foreground: 48 96% 89%;
```

### 1.2. <PERSON><PERSON>

| Nom           | Thème <PERSON> (`:root`) | Thème Sombre (`.dark:root`) | Usage                                |
| ------------- | --------------------- | --------------------------- | ------------------------------------ |
| `background`  | `0 0% 100%`           | `20 14.3% 4.1%`             | Fond principal de l'application      |
| `foreground`  | `20 14.3% 4.1%`       | `60 9.1% 97.8%`             | Texte principal                      |
| `primary`     | `47.9 95.8% 53.1%`    | `47.9 95.8% 53.1%`          | Couleur d'accentuation principale    |
| `destructive` | `0 84.2% 60.2%`       | `0 62.8% 30.6%`             | Actions de suppression, erreurs      |
| `warning`     | `38 92% 50%`          | `45 93% 47%`                | Avertissements, badges importants    |
| `muted`       | `60 4.8% 95.9%`       | `12 6.5% 15.1%`             | Éléments discrets, fonds secondaires |

## 2. Composants

### 2.1. Badge

Le composant `Badge` est utilisé pour afficher des informations courtes et contextuelles, comme des statuts ou des rôles.

#### 2.1.1. Nouvelles Variantes

Pour standardiser l'affichage des rôles, nous ajoutons les variantes `organizer` et `guest`.

**Action :** Mettre à jour `components/ui/badge.tsx`.

1.  Ajouter les nouvelles variantes à l'interface `BadgeProps`:
    ```typescript
    variant?: "default" | "secondary" | "destructive" | "outline" | "organizer" | "guest";
    ```
2.  Ajouter les classes correspondantes dans l'objet `variantClasses`:
    ```typescript
    organizer: "bg-warning text-warning-foreground border border-yellow-500/50",
    guest: "bg-muted text-muted-foreground border border-border",
    ```

#### 2.1.2. Utilisation

**Action :** Mettre à jour `components/EventCard.tsx` pour utiliser les nouvelles variantes.

Remplacer le code actuel du badge organisateur par :

```typescript
<Badge variant="organizer">
  <Text>Organisateur</Text>
</Badge>
```

Ajouter un badge pour les invités (si nécessaire) :

```typescript
<Badge variant="guest">
  <Text>Invité</Text>
</Badge>
```

## 3. Typographie

(À définir)

## 4. Espacements

(À définir)
