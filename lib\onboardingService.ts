/**
 * Service d'onboarding pour Party Organizer
 * Gère les étapes d'introduction et la progression utilisateur
 */

import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";
import { supabase } from "./supabase";

// Wrapper pour le stockage cross-platform
const storage = {
  async getItem(key: string): Promise<string | null> {
    if (Platform.OS === "web") {
      try {
        return localStorage.getItem(key);
      } catch {
        return null;
      }
    }
    return AsyncStorage.getItem(key);
  },

  async setItem(key: string, value: string): Promise<void> {
    if (Platform.OS === "web") {
      try {
        localStorage.setItem(key, value);
      } catch {
        // Ignore errors on web
      }
      return;
    }
    return AsyncStorage.setItem(key, value);
  },

  async removeItem(key: string): Promise<void> {
    if (Platform.OS === "web") {
      try {
        localStorage.removeItem(key);
      } catch {
        // Ignore errors on web
      }
      return;
    }
    return AsyncStorage.removeItem(key);
  },
};

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  screen?: string;
  action?: string;
  completed: boolean;
  optional: boolean;
  order: number;
}

export interface OnboardingProfile {
  userId?: string;
  userType: "organizer" | "participant" | "both" | "unknown";
  interests: string[];
  experience: "beginner" | "intermediate" | "expert";
  goals: string[];
  completedSteps: string[];
  currentStep?: string;
  startedAt: Date;
  completedAt?: Date;
  skipped: boolean;
}

export interface OnboardingProgress {
  totalSteps: number;
  completedSteps: number;
  currentStepIndex: number;
  progressPercentage: number;
  isCompleted: boolean;
  nextStep?: OnboardingStep;
}

class OnboardingService {
  private readonly STORAGE_KEY = "onboarding_profile";
  private readonly STEPS_KEY = "onboarding_steps";

  /**
   * Étapes d'onboarding prédéfinies
   */
  private getDefaultSteps(): OnboardingStep[] {
    return [
      {
        id: "welcome",
        title: "Bienvenue dans Party Organizer !",
        description:
          "Découvrez comment organiser des événements parfaits en quelques clics",
        icon: "🎉",
        screen: "welcome",
        completed: false,
        optional: false,
        order: 1,
      },
      {
        id: "profile_setup",
        title: "Configurez votre profil",
        description:
          "Dites-nous qui vous êtes pour personnaliser votre expérience",
        icon: "👤",
        screen: "profile_setup",
        completed: false,
        optional: false,
        order: 2,
      },
      {
        id: "first_event",
        title: "Créez votre premier événement",
        description:
          "Utilisez un template pour créer un événement en 30 secondes",
        icon: "✨",
        action: "create_event",
        completed: false,
        optional: false,
        order: 3,
      },
      {
        id: "invite_participants",
        title: "Invitez des participants",
        description: "Partagez votre événement et ajoutez des participants",
        icon: "👥",
        action: "invite_participants",
        completed: false,
        optional: false,
        order: 4,
      },
      {
        id: "manage_items",
        title: "Gérez les items",
        description: "Ajoutez des items à votre liste et assignez-les",
        icon: "📝",
        action: "manage_items",
        completed: false,
        optional: false,
        order: 5,
      },
      {
        id: "financial_tracking",
        title: "Suivez les finances",
        description: "Découvrez la gestion financière automatique",
        icon: "💰",
        action: "view_finances",
        completed: false,
        optional: true,
        order: 6,
      },
      {
        id: "notifications",
        title: "Activez les notifications",
        description: "Restez informé des mises à jour de vos événements",
        icon: "🔔",
        action: "setup_notifications",
        completed: false,
        optional: true,
        order: 7,
      },
      {
        id: "dashboard",
        title: "Explorez votre tableau de bord",
        description: "Analysez vos performances d'organisateur",
        icon: "📊",
        action: "view_dashboard",
        completed: false,
        optional: true,
        order: 8,
      },
      {
        id: "completion",
        title: "Félicitations !",
        description: "Vous maîtrisez maintenant Party Organizer",
        icon: "🏆",
        screen: "completion",
        completed: false,
        optional: false,
        order: 9,
      },
    ];
  }

  /**
   * Initialise l'onboarding pour un nouvel utilisateur
   */
  async initializeOnboarding(userId?: string): Promise<OnboardingProfile> {
    try {
      const profile: OnboardingProfile = {
        userId,
        userType: "unknown",
        interests: [],
        experience: "beginner",
        goals: [],
        completedSteps: [],
        startedAt: new Date(),
        skipped: false,
      };

      await this.saveProfile(profile);
      await this.initializeSteps();

      return profile;
    } catch (error) {
      console.error("Error initializing onboarding:", error);
      throw error;
    }
  }

  /**
   * Initialise les étapes d'onboarding
   */
  private async initializeSteps(): Promise<void> {
    try {
      const steps = this.getDefaultSteps();
      await storage.setItem(this.STEPS_KEY, JSON.stringify(steps));
    } catch (error) {
      console.error("Error initializing steps:", error);
    }
  }

  /**
   * Récupère le profil d'onboarding
   */
  async getProfile(): Promise<OnboardingProfile | null> {
    try {
      const stored = await storage.getItem(this.STORAGE_KEY);
      if (!stored) return null;

      const profile = JSON.parse(stored);
      // Convertir les dates
      profile.startedAt = new Date(profile.startedAt);
      if (profile.completedAt) {
        profile.completedAt = new Date(profile.completedAt);
      }

      return profile;
    } catch (error) {
      console.error("Error getting onboarding profile:", error);
      return null;
    }
  }

  /**
   * Sauvegarde le profil d'onboarding
   */
  async saveProfile(profile: OnboardingProfile): Promise<void> {
    try {
      await storage.setItem(this.STORAGE_KEY, JSON.stringify(profile));
    } catch (error) {
      console.error("Error saving onboarding profile:", error);
    }
  }

  /**
   * Récupère les étapes d'onboarding
   */
  async getSteps(): Promise<OnboardingStep[]> {
    try {
      const stored = await storage.getItem(this.STEPS_KEY);
      if (!stored) {
        const defaultSteps = this.getDefaultSteps();
        await this.initializeSteps();
        return defaultSteps;
      }

      return JSON.parse(stored);
    } catch (error) {
      console.error("Error getting onboarding steps:", error);
      return this.getDefaultSteps();
    }
  }

  /**
   * Met à jour le profil utilisateur
   */
  async updateProfile(updates: Partial<OnboardingProfile>): Promise<void> {
    try {
      const profile = await this.getProfile();
      if (!profile) return;

      const updatedProfile = { ...profile, ...updates };
      await this.saveProfile(updatedProfile);
    } catch (error) {
      console.error("Error updating onboarding profile:", error);
    }
  }

  /**
   * Marque une étape comme complétée
   */
  async completeStep(stepId: string): Promise<void> {
    try {
      const profile = await this.getProfile();
      if (!profile) return;

      if (!profile.completedSteps.includes(stepId)) {
        profile.completedSteps.push(stepId);
        await this.saveProfile(profile);
      }

      // Mettre à jour l'étape
      const steps = await this.getSteps();
      const stepIndex = steps.findIndex((s) => s.id === stepId);
      if (stepIndex > -1) {
        steps[stepIndex].completed = true;
        await storage.setItem(this.STEPS_KEY, JSON.stringify(steps));
      }

      // Vérifier si l'onboarding est terminé
      await this.checkCompletion();
    } catch (error) {
      console.error("Error completing step:", error);
    }
  }

  /**
   * Obtient la progression actuelle
   */
  async getProgress(): Promise<OnboardingProgress> {
    try {
      const steps = await this.getSteps();
      const profile = await this.getProfile();

      const totalSteps = steps.filter((s) => !s.optional).length;
      const completedSteps = steps.filter(
        (s) => s.completed && !s.optional
      ).length;
      const currentStepIndex = steps.findIndex((s) => !s.completed);
      const progressPercentage =
        totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
      const isCompleted = completedSteps === totalSteps;
      const nextStep =
        currentStepIndex > -1 ? steps[currentStepIndex] : undefined;

      return {
        totalSteps,
        completedSteps,
        currentStepIndex,
        progressPercentage,
        isCompleted,
        nextStep,
      };
    } catch (error) {
      console.error("Error getting progress:", error);
      return {
        totalSteps: 0,
        completedSteps: 0,
        currentStepIndex: 0,
        progressPercentage: 0,
        isCompleted: false,
      };
    }
  }

  /**
   * Vérifie si l'onboarding est terminé
   */
  private async checkCompletion(): Promise<void> {
    try {
      const progress = await this.getProgress();
      if (progress.isCompleted) {
        const profile = await this.getProfile();
        if (profile && !profile.completedAt) {
          profile.completedAt = new Date();
          await this.saveProfile(profile);

          // Sauvegarder dans Supabase si utilisateur connecté
          if (profile.userId) {
            await this.saveCompletionToDatabase(profile);
          }
        }
      }
    } catch (error) {
      console.error("Error checking completion:", error);
    }
  }

  /**
   * Sauvegarde la completion dans la base de données
   */
  private async saveCompletionToDatabase(
    profile: OnboardingProfile
  ): Promise<void> {
    try {
      if (!profile.userId) return;

      await supabase.from("profiles").upsert({
        id: profile.userId,
        onboarding_completed: true,
        onboarding_completed_at: profile.completedAt?.toISOString(),
        user_type: profile.userType,
        experience_level: profile.experience,
      });
    } catch (error) {
      console.error("Error saving completion to database:", error);
    }
  }

  /**
   * Ignore l'onboarding
   */
  async skipOnboarding(): Promise<void> {
    try {
      const profile = await this.getProfile();
      if (!profile) return;

      profile.skipped = true;
      profile.completedAt = new Date();
      await this.saveProfile(profile);

      if (profile.userId) {
        await supabase.from("profiles").upsert({
          id: profile.userId,
          onboarding_completed: true,
          onboarding_skipped: true,
          onboarding_completed_at: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error("Error skipping onboarding:", error);
    }
  }

  /**
   * Remet à zéro l'onboarding
   */
  async resetOnboarding(): Promise<void> {
    try {
      await storage.removeItem(this.STORAGE_KEY);
      await storage.removeItem(this.STEPS_KEY);
    } catch (error) {
      console.error("Error resetting onboarding:", error);
    }
  }

  /**
   * Vérifie si l'utilisateur doit voir l'onboarding
   */
  async shouldShowOnboarding(userId?: string): Promise<boolean> {
    try {
      const profile = await this.getProfile();

      // Pas de profil = premier lancement
      if (!profile) return true;

      // Onboarding déjà terminé ou ignoré
      if (profile.completedAt || profile.skipped) return false;

      // Vérifier en base si utilisateur connecté
      if (userId) {
        const { data } = await supabase
          .from("profiles")
          .select("onboarding_completed")
          .eq("id", userId)
          .single();

        if (data?.onboarding_completed) return false;
      }

      return true;
    } catch (error) {
      console.error("Error checking if should show onboarding:", error);
      return true; // Par défaut, montrer l'onboarding en cas d'erreur
    }
  }

  /**
   * Obtient l'étape suivante recommandée
   */
  async getNextRecommendedStep(): Promise<OnboardingStep | null> {
    try {
      const steps = await this.getSteps();
      return steps.find((s) => !s.completed) || null;
    } catch (error) {
      console.error("Error getting next recommended step:", error);
      return null;
    }
  }

  /**
   * Personnalise les étapes selon le profil utilisateur
   */
  async personalizeSteps(
    userType: OnboardingProfile["userType"]
  ): Promise<void> {
    try {
      const steps = await this.getSteps();

      // Adapter les étapes selon le type d'utilisateur
      if (userType === "participant") {
        // Masquer les étapes organisateur
        steps.forEach((step) => {
          if (["dashboard", "financial_tracking"].includes(step.id)) {
            step.optional = true;
          }
        });
      } else if (userType === "organizer") {
        // Toutes les étapes sont importantes pour les organisateurs
        steps.forEach((step) => {
          if (["dashboard", "financial_tracking"].includes(step.id)) {
            step.optional = false;
          }
        });
      }

      await storage.setItem(this.STEPS_KEY, JSON.stringify(steps));
    } catch (error) {
      console.error("Error personalizing steps:", error);
    }
  }
}

// Instance singleton
export const onboardingService = new OnboardingService();

// Fonctions utilitaires
export const onboarding = {
  initialize: (userId?: string) =>
    onboardingService.initializeOnboarding(userId),
  getProfile: () => onboardingService.getProfile(),
  updateProfile: (updates: Partial<OnboardingProfile>) =>
    onboardingService.updateProfile(updates),
  completeStep: (stepId: string) => onboardingService.completeStep(stepId),
  getSteps: () => onboardingService.getSteps(),
  getProgress: () => onboardingService.getProgress(),
  shouldShow: (userId?: string) =>
    onboardingService.shouldShowOnboarding(userId),
  skip: () => onboardingService.skipOnboarding(),
  reset: () => onboardingService.resetOnboarding(),
  getNextStep: () => onboardingService.getNextRecommendedStep(),
  personalize: (userType: OnboardingProfile["userType"]) =>
    onboardingService.personalizeSteps(userType),
};
