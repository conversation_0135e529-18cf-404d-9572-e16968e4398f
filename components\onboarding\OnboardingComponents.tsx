import React, { useState } from "react";
import { View, ScrollView, Pressable, Platform, Dimensions } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { OnboardingStep, OnboardingProgress } from "~/lib/onboardingService";

interface ProgressBarProps {
  progress: OnboardingProgress;
  showDetails?: boolean;
}

export function OnboardingProgressBar({ progress, showDetails = true }: ProgressBarProps) {
  return (
    <View className="mb-4">
      {showDetails && (
        <View className="flex-row items-center justify-between mb-2">
          <Text className="text-sm font-medium">
            Progression de l'onboarding
          </Text>
          <Text className="text-sm text-muted-foreground">
            {progress.completedSteps}/{progress.totalSteps}
          </Text>
        </View>
      )}
      
      <View className="w-full h-2 bg-muted rounded-full overflow-hidden">
        <View 
          className="h-full bg-primary rounded-full transition-all duration-300"
          style={{ width: `${progress.progressPercentage}%` }}
        />
      </View>
      
      {showDetails && (
        <Text className="text-xs text-muted-foreground mt-1">
          {progress.progressPercentage.toFixed(0)}% terminé
        </Text>
      )}
    </View>
  );
}

interface StepCardProps {
  step: OnboardingStep;
  isActive?: boolean;
  onPress?: () => void;
  onComplete?: () => void;
}

export function OnboardingStepCard({ 
  step, 
  isActive = false, 
  onPress, 
  onComplete 
}: StepCardProps) {
  const getStatusIcon = () => {
    if (step.completed) return "✅";
    if (isActive) return "🔄";
    return "⏳";
  };

  const getStatusColor = () => {
    if (step.completed) return "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950";
    if (isActive) return "border-primary bg-primary/5";
    return "border-border bg-card";
  };

  return (
    <Pressable
      onPress={onPress}
      disabled={step.completed}
      {...(Platform.OS === "web" ? { style: { cursor: "pointer" } } : {})}
    >
      <Card className={`${getStatusColor()} ${step.completed ? 'opacity-75' : ''}`}>
        <CardContent className="p-4">
          <View className="flex-row items-start gap-3">
            <View className="items-center">
              <Text className="text-2xl mb-1">{step.icon}</Text>
              <Text className="text-lg">{getStatusIcon()}</Text>
            </View>
            
            <View className="flex-1">
              <View className="flex-row items-center gap-2 mb-1">
                <Text className="font-semibold flex-1">{step.title}</Text>
                {step.optional && (
                  <Badge variant="outline">
                    <Text className="text-xs">Optionnel</Text>
                  </Badge>
                )}
              </View>
              
              <Text className="text-sm text-muted-foreground mb-3">
                {step.description}
              </Text>
              
              {isActive && !step.completed && (
                <View className="flex-row gap-2">
                  <Button size="sm" onPress={onComplete}>
                    <Text className="text-xs text-primary-foreground">
                      Commencer
                    </Text>
                  </Button>
                  {step.optional && (
                    <Button variant="outline" size="sm" onPress={onComplete}>
                      <Text className="text-xs">Ignorer</Text>
                    </Button>
                  )}
                </View>
              )}
            </View>
          </View>
        </CardContent>
      </Card>
    </Pressable>
  );
}

interface OnboardingOverlayProps {
  visible: boolean;
  step: OnboardingStep;
  onNext: () => void;
  onSkip: () => void;
  onClose: () => void;
  targetPosition?: { x: number; y: number; width: number; height: number };
}

export function OnboardingOverlay({
  visible,
  step,
  onNext,
  onSkip,
  onClose,
  targetPosition
}: OnboardingOverlayProps) {
  if (!visible) return null;

  const screenDimensions = Dimensions.get('window');

  return (
    <View className="absolute inset-0 z-50">
      {/* Overlay sombre */}
      <View className="absolute inset-0 bg-black/50" />
      
      {/* Spotlight sur l'élément ciblé */}
      {targetPosition && (
        <View
          className="absolute border-2 border-primary rounded-lg"
          style={{
            left: targetPosition.x - 4,
            top: targetPosition.y - 4,
            width: targetPosition.width + 8,
            height: targetPosition.height + 8,
          }}
        />
      )}
      
      {/* Carte d'explication */}
      <View className="absolute bottom-20 left-4 right-4">
        <Card className="border border-border">
          <CardHeader className="pb-3">
            <View className="flex-row items-center gap-3">
              <Text className="text-2xl">{step.icon}</Text>
              <CardTitle className="flex-1">{step.title}</CardTitle>
              <Button variant="ghost" size="sm" onPress={onClose}>
                <Text>✕</Text>
              </Button>
            </View>
          </CardHeader>
          
          <CardContent className="pt-0">
            <Text className="text-muted-foreground mb-4">
              {step.description}
            </Text>
            
            <View className="flex-row gap-3">
              <Button onPress={onNext} className="flex-1">
                <Text className="text-primary-foreground">
                  Compris !
                </Text>
              </Button>
              
              {step.optional && (
                <Button variant="outline" onPress={onSkip} className="flex-1">
                  <Text>Ignorer</Text>
                </Button>
              )}
            </View>
          </CardContent>
        </Card>
      </View>
    </View>
  );
}

interface WelcomeScreenProps {
  onGetStarted: () => void;
  onSkip: () => void;
}

export function OnboardingWelcomeScreen({ onGetStarted, onSkip }: WelcomeScreenProps) {
  const features = [
    {
      icon: "🎯",
      title: "Templates intelligents",
      description: "Créez des événements parfaits en 30 secondes"
    },
    {
      icon: "💰",
      title: "Gestion financière",
      description: "Suivez automatiquement les coûts et remboursements"
    },
    {
      icon: "👥",
      title: "Collaboration facile",
      description: "Invitez et coordonnez vos participants"
    },
    {
      icon: "📊",
      title: "Analytics avancés",
      description: "Analysez vos performances d'organisateur"
    },
    {
      icon: "📱",
      title: "Mode hors-ligne",
      description: "Fonctionne partout, même sans réseau"
    },
    {
      icon: "🔔",
      title: "Notifications smart",
      description: "Restez informé en temps réel"
    }
  ];

  return (
    <ScrollView className="flex-1 bg-background">
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto p-6" : "p-6"}>
        
        {/* Header */}
        <View className="items-center mb-8">
          <Text className="text-6xl mb-4">🎉</Text>
          <Text className="text-3xl font-bold text-center mb-2">
            Bienvenue dans Party Organizer !
          </Text>
          <Text className="text-lg text-muted-foreground text-center">
            L'app qui transforme l'organisation d'événements en jeu d'enfant
          </Text>
        </View>

        {/* Fonctionnalités */}
        <View className="mb-8">
          <Text className="text-xl font-semibold mb-4 text-center">
            ✨ Ce que vous allez découvrir
          </Text>
          
          <View className="gap-3">
            {features.map((feature, index) => (
              <Card key={index} className="border border-border">
                <CardContent className="p-4">
                  <View className="flex-row items-center gap-3">
                    <Text className="text-2xl">{feature.icon}</Text>
                    <View className="flex-1">
                      <Text className="font-semibold mb-1">{feature.title}</Text>
                      <Text className="text-sm text-muted-foreground">
                        {feature.description}
                      </Text>
                    </View>
                  </View>
                </CardContent>
              </Card>
            ))}
          </View>
        </View>

        {/* Statistiques */}
        <Card className="mb-8 bg-primary/5 border-primary/20">
          <CardContent className="p-4">
            <Text className="text-center font-semibold mb-3">
              🏆 Rejoignez des milliers d'organisateurs
            </Text>
            <View className="flex-row justify-around">
              <View className="items-center">
                <Text className="text-2xl font-bold text-primary">10k+</Text>
                <Text className="text-xs text-muted-foreground">Événements</Text>
              </View>
              <View className="items-center">
                <Text className="text-2xl font-bold text-primary">50k+</Text>
                <Text className="text-xs text-muted-foreground">Participants</Text>
              </View>
              <View className="items-center">
                <Text className="text-2xl font-bold text-primary">95%</Text>
                <Text className="text-xs text-muted-foreground">Satisfaction</Text>
              </View>
            </View>
          </CardContent>
        </Card>

        {/* Actions */}
        <View className="gap-3">
          <Button onPress={onGetStarted} className="h-12">
            <Text className="text-primary-foreground font-semibold">
              🚀 Commencer le tour guidé (2 min)
            </Text>
          </Button>
          
          <Button variant="outline" onPress={onSkip} className="h-12">
            <Text>Ignorer et explorer librement</Text>
          </Button>
        </View>

        {/* Footer */}
        <View className="mt-8 items-center">
          <Text className="text-xs text-muted-foreground text-center">
            Le tour guidé vous aide à maîtriser toutes les fonctionnalités{'\n'}
            Vous pouvez l'interrompre à tout moment
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

interface CompletionScreenProps {
  onFinish: () => void;
  onViewDashboard: () => void;
  completedSteps: number;
  totalSteps: number;
}

export function OnboardingCompletionScreen({ 
  onFinish, 
  onViewDashboard, 
  completedSteps, 
  totalSteps 
}: CompletionScreenProps) {
  const achievements = [
    "🎯 Premier événement créé",
    "👥 Participants invités",
    "📝 Items organisés",
    "💰 Finances configurées",
    "🔔 Notifications activées",
  ];

  return (
    <ScrollView className="flex-1 bg-background">
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto p-6" : "p-6"}>
        
        {/* Header de félicitations */}
        <View className="items-center mb-8">
          <Text className="text-6xl mb-4">🏆</Text>
          <Text className="text-3xl font-bold text-center mb-2">
            Félicitations !
          </Text>
          <Text className="text-lg text-muted-foreground text-center">
            Vous maîtrisez maintenant Party Organizer
          </Text>
        </View>

        {/* Progression */}
        <Card className="mb-6 bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800">
          <CardContent className="p-4">
            <View className="items-center">
              <Text className="text-lg font-semibold mb-2">
                🎉 Onboarding terminé !
              </Text>
              <Text className="text-3xl font-bold text-green-600 dark:text-green-400 mb-1">
                {completedSteps}/{totalSteps}
              </Text>
              <Text className="text-sm text-muted-foreground">
                étapes complétées
              </Text>
            </View>
          </CardContent>
        </Card>

        {/* Achievements */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>🏅 Vos premiers succès</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-2">
              {achievements.slice(0, completedSteps).map((achievement, index) => (
                <View key={index} className="flex-row items-center gap-3">
                  <Text className="text-green-500">✅</Text>
                  <Text className="flex-1">{achievement}</Text>
                </View>
              ))}
            </View>
          </CardContent>
        </Card>

        {/* Prochaines étapes */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>🚀 Prochaines étapes recommandées</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-3">
              <View className="flex-row items-center gap-3">
                <Text className="text-2xl">📊</Text>
                <View className="flex-1">
                  <Text className="font-semibold">Explorez votre tableau de bord</Text>
                  <Text className="text-sm text-muted-foreground">
                    Analysez vos performances et obtenez des insights
                  </Text>
                </View>
              </View>
              
              <View className="flex-row items-center gap-3">
                <Text className="text-2xl">🎯</Text>
                <View className="flex-1">
                  <Text className="font-semibold">Créez plus d'événements</Text>
                  <Text className="text-sm text-muted-foreground">
                    Utilisez les templates pour aller plus vite
                  </Text>
                </View>
              </View>
              
              <View className="flex-row items-center gap-3">
                <Text className="text-2xl">👥</Text>
                <View className="flex-1">
                  <Text className="font-semibold">Invitez vos amis</Text>
                  <Text className="text-sm text-muted-foreground">
                    Partagez Party Organizer avec votre communauté
                  </Text>
                </View>
              </View>
            </View>
          </CardContent>
        </Card>

        {/* Actions */}
        <View className="gap-3">
          <Button onPress={onViewDashboard} className="h-12">
            <Text className="text-primary-foreground font-semibold">
              📊 Voir mon tableau de bord
            </Text>
          </Button>
          
          <Button variant="outline" onPress={onFinish} className="h-12">
            <Text>🎉 Commencer à organiser !</Text>
          </Button>
        </View>

        {/* Message d'encouragement */}
        <Card className="mt-6 border-dashed border-2 border-muted">
          <CardContent className="p-4">
            <View className="items-center">
              <Text className="text-2xl mb-2">💡</Text>
              <Text className="font-semibold mb-1">Besoin d'aide ?</Text>
              <Text className="text-sm text-muted-foreground text-center">
                Consultez les tooltips dans l'app ou relancez le tour guidé depuis les paramètres
              </Text>
            </View>
          </CardContent>
        </Card>
      </View>
    </ScrollView>
  );
}
