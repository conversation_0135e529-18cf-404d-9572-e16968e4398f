import React from "react";
import { View, Platform, Dimensions } from "react-native";
import { Text } from "~/components/ui/text";
import { cn } from "~/lib/utils";

interface FormLayoutProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: "sm" | "md" | "lg" | "xl";
  padding?: "sm" | "md" | "lg";
}

const maxWidthClasses = {
  sm: "max-w-sm", // 384px
  md: "max-w-md", // 448px
  lg: "max-w-lg", // 512px
  xl: "max-w-xl", // 576px
};

const paddingClasses = {
  sm: "p-4",
  md: "p-6",
  lg: "p-8",
};

export function FormLayout({
  children,
  className,
  maxWidth = "md",
  padding = "md",
}: FormLayoutProps) {
  const { width: screenWidth } = Dimensions.get("window");
  const isDesktop = Platform.OS === "web" && screenWidth > 768;

  return (
    <View
      className={cn(
        "w-full",
        isDesktop && "mx-auto",
        isDesktop && maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
    >
      {children}
    </View>
  );
}

interface FormSectionProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
}

export function FormSection({
  children,
  title,
  description,
  className,
}: FormSectionProps) {
  return (
    <View
      className={cn(
        "bg-card rounded-2xl shadow-sm border border-border mb-6",
        Platform.OS === "web" ? "p-6" : "p-4",
        className
      )}
    >
      {(title || description) && (
        <View className="mb-6">
          {title && (
            <Text className="text-lg font-semibold text-foreground mb-2">
              {title}
            </Text>
          )}
          {description && (
            <Text className="text-muted-foreground text-sm">{description}</Text>
          )}
        </View>
      )}
      {children}
    </View>
  );
}

interface FormFieldProps {
  children: React.ReactNode;
  label?: string;
  required?: boolean;
  error?: string;
  className?: string;
}

export function FormField({
  children,
  label,
  required = false,
  error,
  className,
}: FormFieldProps) {
  return (
    <View className={cn("mb-5", className)}>
      {label && (
        <View className="flex-row mb-2">
          {required && <Text className="text-destructive mr-1">*</Text>}
          <Text className="font-medium text-foreground">{label}</Text>
        </View>
      )}
      {children}
      {error && (
        <Text className="text-destructive text-sm mt-1 ml-1">{error}</Text>
      )}
    </View>
  );
}
