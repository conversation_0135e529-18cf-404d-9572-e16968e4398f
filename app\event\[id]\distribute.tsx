import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import {
  fetchEventDetails,
  fetchParticipantsForEvent,
  fetchItemsForEvent,
  updateItem,
} from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Participant } from "~/lib/types";
import { Database } from "~/lib/database.types";

type ItemFromFetch = Awaited<ReturnType<typeof fetchItemsForEvent>>[number];
type CostEnum = Database["public"]["Enums"]["cost_enum"];
type EffortEnum = Database["public"]["Enums"]["effort_enum"];

export default function DistributeScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { session } = useAuth();
  const router = useRouter();

  const [event, setEvent] = useState<Event | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [items, setItems] = useState<ItemFromFetch[]>([]);
  const [loading, setLoading] = useState(true);
  const [distributing, setDistributing] = useState(false);

  const loadData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const eventId = parseInt(id as string, 10);
      const [eventDetails, eventParticipants, eventItems] = await Promise.all([
        fetchEventDetails(eventId),
        fetchParticipantsForEvent(eventId),
        fetchItemsForEvent(eventId),
      ]);

      setEvent(eventDetails);
      setParticipants(
        eventParticipants?.filter((p) => p.status === "accepted") || []
      );
      setItems(eventItems || []);
    } catch (error) {
      console.error("Error loading data:", error);
      showToast("Erreur lors du chargement.", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [id]);

  // Algorithme simple de répartition équitable
  const distributeItems = async () => {
    if (!event || participants.length === 0 || items.length === 0) {
      showToast(
        "Impossible de répartir : pas assez de participants ou d'items.",
        { type: "error" }
      );
      return;
    }

    try {
      setDistributing(true);

      // Filtrer les items non assignés et non fixés
      const unassignedItems = items.filter(
        (item) => !item.assigned_participant_id && !item.fixed_by_participant_id
      );

      if (unassignedItems.length === 0) {
        showToast("Tous les items sont déjà assignés ou fixés.", {
          type: "info",
        });
        return;
      }

      // Calculer le score de chaque item (coût + effort)
      const getItemScore = (item: ItemFromFetch): number => {
        let score = 0;

        // Score basé sur le coût
        switch (item.estimated_cost) {
          case "€":
            score += 1;
            break;
          case "€€":
            score += 2;
            break;
          case "€€€":
            score += 3;
            break;
          default:
            score += 1;
        }

        // Score basé sur l'effort
        switch (item.estimated_effort) {
          case "1":
            score += 1;
            break;
          case "2":
            score += 2;
            break;
          case "3":
            score += 3;
            break;
          default:
            score += 1;
        }

        return score;
      };

      // Trier les items par score décroissant
      const sortedItems = unassignedItems.sort(
        (a, b) => getItemScore(b) - getItemScore(a)
      );

      // Initialiser les scores des participants
      const participantScores = new Map(participants.map((p) => [p.id, 0]));

      // Répartir les items
      const assignments: Array<{ itemId: number; participantId: number }> = [];

      for (const item of sortedItems) {
        // Trouver le participant avec le score le plus bas
        const participantWithLowestScore = participants.reduce(
          (min, current) => {
            const minScore = participantScores.get(min.id) || 0;
            const currentScore = participantScores.get(current.id) || 0;
            return currentScore < minScore ? current : min;
          }
        );

        // Assigner l'item
        assignments.push({
          itemId: item.id,
          participantId: participantWithLowestScore.id,
        });

        // Mettre à jour le score du participant
        const currentScore =
          participantScores.get(participantWithLowestScore.id) || 0;
        participantScores.set(
          participantWithLowestScore.id,
          currentScore + getItemScore(item)
        );
      }

      // Appliquer les assignations
      for (const assignment of assignments) {
        await updateItem(assignment.itemId, {
          assigned_participant_id: assignment.participantId,
        });
      }

      showToast(`${assignments.length} items répartis avec succès !`, {
        type: "success",
      });

      // Recharger les données
      await loadData();
    } catch (error) {
      console.error("Error distributing items:", error);
      showToast("Erreur lors de la répartition.", { type: "error" });
    } finally {
      setDistributing(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!event) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-lg text-destructive">Événement introuvable.</Text>
        <Button className="mt-4" onPress={() => router.back()}>
          <Text>Retour</Text>
        </Button>
      </View>
    );
  }

  const isOrganizer = session?.user?.id === event.organizer_id;

  if (!isOrganizer) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-lg text-destructive">
          Seul l'organisateur peut répartir les tâches.
        </Text>
        <Button className="mt-4" onPress={() => router.back()}>
          <Text>Retour</Text>
        </Button>
      </View>
    );
  }

  const unassignedItems = items.filter(
    (item) => !item.assigned_participant_id && !item.fixed_by_participant_id
  );
  const assignedItems = items.filter((item) => item.assigned_participant_id);
  const fixedItems = items.filter((item) => item.fixed_by_participant_id);

  return (
    <ScrollView
      className="flex-1 bg-background"
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        {/* Header */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-xl">
              Répartition des Tâches - {event.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-muted-foreground mb-4">
              Répartissez automatiquement les items entre les participants
              acceptés.
            </Text>
            <View className="flex-row justify-between mb-4">
              <Text>📝 {items.length} items total</Text>
              <Text>👥 {participants.length} participants</Text>
            </View>
            <View className="flex-row justify-between">
              <Text>⏳ {unassignedItems.length} non assignés</Text>
              <Text>📌 {fixedItems.length} fixés</Text>
            </View>
          </CardContent>
        </Card>

        {/* Bouton de répartition */}
        {unassignedItems.length > 0 && participants.length > 0 && (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <Button
                onPress={distributeItems}
                disabled={distributing}
                className="w-full h-12"
              >
                <Text className="text-primary-foreground font-medium">
                  {distributing
                    ? "Répartition en cours..."
                    : "🎯 Répartir automatiquement"}
                </Text>
              </Button>
              <Text className="text-xs text-muted-foreground mt-2 text-center">
                La répartition se base sur l'équilibrage du coût et de l'effort
              </Text>
            </CardContent>
          </Card>
        )}

        {/* Aperçu des assignations actuelles */}
        {assignedItems.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Items déjà assignés</CardTitle>
            </CardHeader>
            <CardContent className="gap-2">
              {assignedItems.map((item) => (
                <View
                  key={item.id}
                  className="flex-row items-center justify-between p-3 bg-green-100/50 dark:bg-green-900/20 rounded-lg"
                >
                  <Text className="flex-1">{item.name}</Text>
                  <Text className="text-sm text-green-700 dark:text-green-400">
                    ✅ Assigné
                  </Text>
                </View>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Items non assignés */}
        {unassignedItems.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Items à répartir</CardTitle>
            </CardHeader>
            <CardContent className="gap-2">
              {unassignedItems.map((item) => (
                <View
                  key={item.id}
                  className="flex-row items-center justify-between p-3 bg-yellow-100/50 dark:bg-yellow-900/20 rounded-lg"
                >
                  <Text className="flex-1">{item.name}</Text>
                  <View className="flex-row items-center">
                    <Text className="text-xs mr-2">
                      {item.estimated_cost || "€"}
                    </Text>
                    <Text className="text-xs">
                      {item.estimated_effort || "1"}
                    </Text>
                  </View>
                </View>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Bouton retour */}
        <Button
          variant="outline"
          onPress={() => router.back()}
          className="w-full"
        >
          <Text>Revenir aux détails de l'événement</Text>
        </Button>
      </View>
    </ScrollView>
  );
}
