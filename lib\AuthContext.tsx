import React, { createContext, useContext, useEffect, useState } from "react";
import { Session } from "@supabase/supabase-js";
import { supabase } from "./supabase";
import {
  createProfile,
  fetchProfile,
  disableRLSTemporarily,
} from "./supabaseCrud";
import {
  registerNotificationToken,
  unregisterNotificationToken,
  setupNotificationListeners,
} from "./notificationService";

interface AuthContextType {
  session: Session | null;
  isLoading: boolean;
  isAnonymous: boolean;
  signOut: () => Promise<void>;
}

// Fonction utilitaire pour détecter les utilisateurs anonymes
const isUserAnonymous = (session: Session | null): boolean => {
  if (!session?.user) return false;
  // Un utilisateur est anonyme s'il n'a pas d'email ou si is_anonymous est true
  return !session.user.email || session.user.is_anonymous === true;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const isAnonymous = isUserAnonymous(session);

  useEffect(() => {
    let isMounted = true;

    // Essayer de désactiver RLS au démarrage
    disableRLSTemporarily().then((success) => {
      if (success) {
        console.log("✅ Tentative de désactivation RLS effectuée");
      } else {
        console.warn("⚠️ Impossible de désactiver RLS automatiquement");
      }
    });

    const fetchSession = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!isMounted) return;

        if (session) {
          setSession(session);
        } else {
          // Si pas de session, connecter automatiquement en tant qu'anonyme
          try {
            const { data, error } = await supabase.auth.signInAnonymously();
            if (error) {
              console.error("Erreur lors de la connexion anonyme:", error);
            } else if (isMounted) {
              console.log("Connexion anonyme réussie");
              setSession(data.session);
            }
          } catch (error) {
            console.error("Erreur lors de la connexion anonyme:", error);
          }
        }
      } catch (error) {
        console.error("Erreur lors de la récupération de la session:", error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchSession();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!isMounted) return;

      setSession(session);

      // Créer automatiquement un profil lors de l'inscription ou connexion
      // Mais PAS pour les utilisateurs anonymes
      if (
        (event === "SIGNED_UP" || event === "SIGNED_IN") &&
        session?.user &&
        !isUserAnonymous(session)
      ) {
        // Utiliser setTimeout pour éviter les problèmes de rendu
        setTimeout(async () => {
          try {
            if (!isMounted) return;

            console.log("Vérification/création du profil utilisateur...");

            // Vérifier si le profil existe déjà
            const existingProfile = await fetchProfile(session.user.id);

            if (!existingProfile && isMounted) {
              // Créer un nouveau profil avec les données de base
              const userName =
                session.user.user_metadata?.name ||
                session.user.email?.split("@")[0] ||
                "Utilisateur";

              const profile = await createProfile(session.user.id, {
                name: userName,
                avatar_url: session.user.user_metadata?.avatar_url || null,
              });

              if (profile && isMounted) {
                console.log("Profil créé avec succès pour l'utilisateur");
              } else if (isMounted) {
                console.warn(
                  "Impossible de créer le profil, mais l'app continuera"
                );
              }
            }

            // Enregistrer le token de notification après création du profil
            try {
              await registerNotificationToken(session.user.id);
              console.log("Token de notification enregistré");
            } catch (error) {
              console.warn(
                "Erreur lors de l'enregistrement du token de notification:",
                error
              );
            }
          } catch (error) {
            if (isMounted) {
              console.error(
                "Erreur lors de la création automatique du profil:",
                error
              );
            }
            // Ne pas bloquer l'authentification même si la création de profil échoue
          }
        }, 100);
      }

      // Gérer la déconnexion
      if (event === "SIGNED_OUT") {
        try {
          // Note: Le token sera automatiquement supprimé par les politiques RLS
          console.log("Utilisateur déconnecté, token de notification nettoyé");
        } catch (error) {
          console.warn("Erreur lors du nettoyage du token:", error);
        }
      }
    });

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    await supabase.auth.signOut();
    setSession(null);
  };

  // Afficher un écran de chargement pendant l'initialisation
  if (isLoading) {
    return null; // ou un composant de chargement
  }

  return (
    <AuthContext.Provider value={{ session, isLoading, isAnonymous, signOut }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
