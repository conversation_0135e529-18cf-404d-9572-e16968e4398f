import React, { useState } from "react";
import { View, Platform } from "react-native"; // Added Platform
import { supabase } from "~/lib/supabase";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { Label } from "~/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { showToast } from "~/lib/toastService"; // Assuming toastService is set up

export default function CreateAccountForm({
  onSuccess,
}: {
  onSuccess?: () => void;
}) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);

  async function signUpWithEmail() {
    if (!email || !password) {
      showToast("Veuillez remplir tous les champs.", {
        // Corrected showToast
        type: "error",
      });
      return;
    }
    setLoading(true);
    const {
      data: { session },
      error,
    } = await supabase.auth.signUp({
      email: email,
      password: password,
    });

    setLoading(false);
    if (error) {
      showToast(error.message, {
        // Corrected showToast
        type: "error",
      });
    } else if (!session) {
      showToast(
        "Veuillez consulter votre boîte de réception pour vérifier votre email.", // Corrected showToast
        {
          type: "info",
        }
      );
      if (onSuccess) onSuccess(); // Call onSuccess even if verification is needed
    } else {
      showToast("Votre compte a été créé avec succès.", {
        // Corrected showToast
        type: "success",
      });
      if (onSuccess) onSuccess();
    }
  }

  // Copied from components/Auth.tsx for platform-specific press handling
  const handlePress = (callback: () => void) => {
    return Platform.OS === "web"
      ? { onClick: callback }
      : { onPress: callback };
  };

  return (
    <Card className="w-full border border-border">
      <CardHeader>
        <CardTitle className="text-2xl">Créer un compte</CardTitle>
        <CardDescription>
          Entrez votre email et mot de passe pour commencer.
        </CardDescription>
      </CardHeader>
      <CardContent className="gap-4">
        <View className="gap-1.5">
          <Label nativeID="emailLabelCreate">Email</Label>
          <Input
            nativeID="emailLabelCreate"
            placeholder="<EMAIL>"
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
          />
        </View>
        <View className="gap-1.5">
          <Label nativeID="passwordLabelCreate">Mot de passe</Label>
          <Input
            nativeID="passwordLabelCreate"
            placeholder="********"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
        </View>
      </CardContent>
      <CardFooter>
        <Button
          className="w-full"
          disabled={loading}
          {...handlePress(signUpWithEmail)} // Used handlePress
        >
          {loading ? (
            <Text>Création en cours...</Text>
          ) : (
            <Text>Créer mon compte</Text>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
