import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Separator } from "~/components/ui/separator";
import { 
  FinancialSummary as FinancialSummaryType,
  SettlementSuggestion,
  Item,
  Participant 
} from "~/lib/types";
import { 
  calculateEventFinancialSummary,
  formatCurrency,
  generateSettlementSummary 
} from "~/lib/financialCalculator";
import { 
  fetchEventFinancialSummary,
  recalculateEventFinancials,
  createFinancialTransaction 
} from "~/lib/supabaseCrud";
import { showToast } from "~/lib/toastService";

interface FinancialSummaryProps {
  eventId: number;
  items: Item[];
  participants: Participant[];
  currentParticipantId?: number;
  isOrganizer?: boolean;
  onRefresh?: () => void;
}

export function FinancialSummary({
  eventId,
  items,
  participants,
  currentParticipantId,
  isOrganizer = false,
  onRefresh
}: FinancialSummaryProps) {
  const [loading, setLoading] = useState(false);
  const [summaries, setSummaries] = useState<FinancialSummaryType[]>([]);
  const [settlementSuggestions, setSettlementSuggestions] = useState<SettlementSuggestion[]>([]);
  const [totalCost, setTotalCost] = useState(0);
  const [costPerPerson, setCostPerPerson] = useState(0);

  useEffect(() => {
    loadFinancialData();
  }, [eventId, items, participants]);

  const loadFinancialData = async () => {
    setLoading(true);
    try {
      // Calculer le résumé financier complet
      const eventSummary = calculateEventFinancialSummary(items, participants);
      
      setTotalCost(eventSummary.total_cost);
      setCostPerPerson(eventSummary.cost_per_person);
      setSummaries(eventSummary.participant_summaries);
      setSettlementSuggestions(eventSummary.settlement_suggestions);
      
    } catch (error) {
      console.error("Error loading financial data:", error);
      showToast("Erreur lors du chargement des données financières", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const handleRecalculate = async () => {
    setLoading(true);
    try {
      const success = await recalculateEventFinancials(eventId);
      if (success) {
        await loadFinancialData();
        showToast("Calculs financiers mis à jour", { type: "success" });
        onRefresh?.();
      } else {
        showToast("Erreur lors du recalcul", { type: "error" });
      }
    } catch (error) {
      console.error("Error recalculating:", error);
      showToast("Erreur lors du recalcul", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSettlement = async (suggestion: SettlementSuggestion) => {
    try {
      const transaction = await createFinancialTransaction(
        eventId,
        suggestion.from_participant.id,
        suggestion.to_participant.id,
        suggestion.amount,
        suggestion.description
      );
      
      if (transaction) {
        showToast("Transaction créée avec succès", { type: "success" });
        await loadFinancialData();
      } else {
        showToast("Erreur lors de la création de la transaction", { type: "error" });
      }
    } catch (error) {
      console.error("Error creating settlement:", error);
      showToast("Erreur lors de la création de la transaction", { type: "error" });
    }
  };

  const getBalanceColor = (balance: number) => {
    if (balance > 0.01) return "text-green-600 dark:text-green-400";
    if (balance < -0.01) return "text-red-600 dark:text-red-400";
    return "text-muted-foreground";
  };

  const getBalanceText = (balance: number) => {
    if (balance > 0.01) return "À recevoir";
    if (balance < -0.01) return "À payer";
    return "Équilibré";
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center p-6">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-muted-foreground">
          Calcul des finances en cours...
        </Text>
      </View>
    );
  }

  const itemsWithCost = items.filter(item => item.actual_cost && item.actual_cost > 0);

  if (itemsWithCost.length === 0) {
    return (
      <Card className="m-4">
        <CardContent className="items-center p-6">
          <Text className="text-6xl mb-4">💰</Text>
          <Text className="text-xl font-semibold mb-2 text-center">
            Aucun coût enregistré
          </Text>
          <Text className="text-muted-foreground text-center">
            Ajoutez les coûts réels des items pour voir le résumé financier.
          </Text>
        </CardContent>
      </Card>
    );
  }

  return (
    <ScrollView className="flex-1">
      <View className={Platform.OS === "web" ? "max-w-4xl mx-auto p-4" : "p-4"}>
        
        {/* Résumé global */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex-row items-center">
              <Text className="text-2xl mr-3">💰</Text>
              <Text className="text-xl">Résumé financier</Text>
            </CardTitle>
          </CardHeader>
          <CardContent className="gap-4">
            <View className="flex-row justify-between items-center">
              <Text className="text-lg font-medium">Coût total :</Text>
              <Text className="text-lg font-bold text-primary">
                {formatCurrency(totalCost)}
              </Text>
            </View>
            
            <View className="flex-row justify-between items-center">
              <Text className="text-base text-muted-foreground">
                Coût par personne :
              </Text>
              <Text className="text-base font-medium">
                {formatCurrency(costPerPerson)}
              </Text>
            </View>
            
            <View className="flex-row justify-between items-center">
              <Text className="text-base text-muted-foreground">
                Participants :
              </Text>
              <Text className="text-base">
                {participants.filter(p => p.status === 'accepted').length}
              </Text>
            </View>

            {isOrganizer && (
              <Button
                variant="outline"
                onPress={handleRecalculate}
                disabled={loading}
                className="mt-2"
              >
                <Text>🔄 Recalculer</Text>
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Résumé par participant */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Répartition par participant</CardTitle>
          </CardHeader>
          <CardContent className="gap-3">
            {summaries.map((summary) => (
              <View
                key={summary.participant_id}
                className="flex-row items-center justify-between p-3 bg-muted/30 rounded-lg"
              >
                <View className="flex-1">
                  <Text className="font-medium">
                    {summary.participant?.name_display || "Participant inconnu"}
                  </Text>
                  <View className="flex-row gap-4 mt-1">
                    <Text className="text-sm text-muted-foreground">
                      Payé: {formatCurrency(summary.total_spent)}
                    </Text>
                    <Text className="text-sm text-muted-foreground">
                      Doit: {formatCurrency(summary.total_owed)}
                    </Text>
                  </View>
                </View>
                
                <View className="items-end">
                  <Badge 
                    variant={summary.net_balance > 0.01 ? "default" : 
                            summary.net_balance < -0.01 ? "destructive" : "secondary"}
                  >
                    <Text className="text-xs">
                      {getBalanceText(summary.net_balance)}
                    </Text>
                  </Badge>
                  <Text className={`text-sm font-medium mt-1 ${getBalanceColor(summary.net_balance)}`}>
                    {formatCurrency(Math.abs(summary.net_balance))}
                  </Text>
                </View>
              </View>
            ))}
          </CardContent>
        </Card>

        {/* Suggestions de remboursement */}
        {settlementSuggestions.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Remboursements suggérés</CardTitle>
            </CardHeader>
            <CardContent className="gap-3">
              {settlementSuggestions.map((suggestion, index) => (
                <View
                  key={index}
                  className="flex-row items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg"
                >
                  <View className="flex-1">
                    <Text className="font-medium">
                      {suggestion.from_participant.name_display}
                    </Text>
                    <Text className="text-sm text-muted-foreground">
                      doit {formatCurrency(suggestion.amount)} à{" "}
                      {suggestion.to_participant.name_display}
                    </Text>
                  </View>
                  
                  {isOrganizer && (
                    <Button
                      size="sm"
                      variant="outline"
                      onPress={() => handleCreateSettlement(suggestion)}
                    >
                      <Text className="text-xs">Créer transaction</Text>
                    </Button>
                  )}
                </View>
              ))}
              
              <Separator className="my-2" />
              
              <View className="bg-muted/50 p-3 rounded-lg">
                <Text className="text-sm text-muted-foreground">
                  💡 Ces suggestions optimisent le nombre de transactions nécessaires.
                </Text>
              </View>
            </CardContent>
          </Card>
        )}

        {/* Message si tout est équilibré */}
        {settlementSuggestions.length === 0 && totalCost > 0 && (
          <Card className="mb-6">
            <CardContent className="items-center p-6">
              <Text className="text-6xl mb-4">✅</Text>
              <Text className="text-xl font-semibold mb-2 text-center text-green-600 dark:text-green-400">
                Comptes équilibrés !
              </Text>
              <Text className="text-muted-foreground text-center">
                Aucun remboursement nécessaire. Tous les participants ont payé leur part équitable.
              </Text>
            </CardContent>
          </Card>
        )}
      </View>
    </ScrollView>
  );
}
