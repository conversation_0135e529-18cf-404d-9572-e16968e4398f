import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  fetchEventDetails,
  fetchParticipantsForEvent,
  createParticipant,
} from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Participant } from "~/lib/types";
import { Database } from "~/lib/database.types";

type ParticipantStatusEnum = Database["public"]["Enums"]["participant_status"];

export default function ParticipantsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { session } = useAuth();
  const router = useRouter();

  const [event, setEvent] = useState<Event | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [participantName, setParticipantName] = useState("");
  const [inviteLoading, setInviteLoading] = useState(false);

  const loadData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const eventId = parseInt(id as string, 10);
      const [eventDetails, eventParticipants] = await Promise.all([
        fetchEventDetails(eventId),
        fetchParticipantsForEvent(eventId),
      ]);

      setEvent(eventDetails);
      setParticipants(eventParticipants || []);
    } catch (error) {
      console.error("Error loading data:", error);
      showToast("Erreur lors du chargement.", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [id]);

  const handleAddParticipant = async () => {
    if (!event || !session?.user?.id || !participantName.trim()) return;

    // Vérifier si le nom n'est pas déjà utilisé
    const existingParticipant = participants.find(
      (p) =>
        p.anonymous_name?.toLowerCase() === participantName.trim().toLowerCase()
    );

    if (existingParticipant) {
      showToast("Ce nom de participant existe déjà.", { type: "error" });
      return;
    }

    try {
      setInviteLoading(true);

      // Générer un token unique
      const invitationToken = crypto.randomUUID();

      // Créer un participant avec nom anonyme
      const participantData = {
        event_id: event.id,
        user_id: null, // Participant anonyme pour l'instant
        role: "guest" as const,
        status: "pending" as ParticipantStatusEnum,
        anonymous_name: participantName.trim(),
        anonymous_email: null,
        anonymous_phone: null,
        invitation_token: invitationToken,
      };

      const result = await createParticipant(participantData);

      if (result) {
        showToast(`Participant "${participantName.trim()}" ajouté !`, {
          type: "success",
        });

        setParticipantName("");
        // Recharger la liste des participants
        loadData();
      } else {
        throw new Error("Échec de l'ajout du participant");
      }
    } catch (error) {
      console.error("Error adding participant:", error);
      showToast("Erreur lors de l'ajout du participant.", {
        type: "error",
      });
    } finally {
      setInviteLoading(false);
    }
  };

  const getStatusEmoji = (status: ParticipantStatusEnum) => {
    switch (status) {
      case "accepted":
        return "✅";
      case "declined":
        return "❌";
      case "maybe":
        return "❓";
      default:
        return "⏳";
    }
  };

  const getStatusText = (status: ParticipantStatusEnum) => {
    switch (status) {
      case "accepted":
        return "Accepté";
      case "declined":
        return "Refusé";
      case "maybe":
        return "Peut-être";
      default:
        return "En attente";
    }
  };

  const copyEventLink = async () => {
    const baseUrl =
      Platform.OS === "web"
        ? window.location.origin
        : "https://party-organizer.app"; // Remplacer par votre domaine
    const eventLink = `${baseUrl}/join/${event?.id}`;

    if (
      Platform.OS === "web" &&
      typeof navigator !== "undefined" &&
      navigator.clipboard
    ) {
      try {
        await navigator.clipboard.writeText(eventLink);
        showToast("Lien de l'événement copié !", { type: "success" });
      } catch (error) {
        showToast(`Lien: ${eventLink}`, { type: "info" });
      }
    } else {
      showToast(`Lien: ${eventLink}`, { type: "info" });
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!event) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-lg text-destructive">Événement introuvable.</Text>
        <Button className="mt-4" onPress={() => router.back()}>
          <Text>Retour</Text>
        </Button>
      </View>
    );
  }

  const isOrganizer = session?.user?.id === event.organizer_id;

  if (!isOrganizer) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-lg text-destructive">
          Seul l'organisateur peut gérer les participants.
        </Text>
        <Button className="mt-4" onPress={() => router.back()}>
          <Text>Retour</Text>
        </Button>
      </View>
    );
  }

  return (
    <ScrollView
      className="flex-1 bg-background"
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        {/* Header */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-xl">
              Participants - {event.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-muted-foreground">
              {participants.length} participant
              {participants.length > 1 ? "s" : ""}
            </Text>
          </CardContent>
        </Card>

        {/* Ajouter un participant (organisateur seulement) */}
        {isOrganizer && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Ajouter un participant</CardTitle>
            </CardHeader>
            <CardContent className="gap-4">
              <View>
                <Label className="mb-2">Nom du participant</Label>
                <Input
                  placeholder="Ex: Maxence Manson"
                  value={participantName}
                  onChangeText={setParticipantName}
                  onSubmitEditing={handleAddParticipant}
                />
              </View>
              <Button
                onPress={handleAddParticipant}
                disabled={inviteLoading || !participantName.trim()}
                className="w-full"
              >
                <Text className="text-primary-foreground">
                  {inviteLoading ? "Ajout..." : "Ajouter le participant"}
                </Text>
              </Button>

              {/* Bouton pour copier le lien de l'événement */}
              <Button
                variant="outline"
                onPress={copyEventLink}
                className="w-full"
              >
                <Text>📋 Copier le lien de l'événement</Text>
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Liste des participants */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Liste des participants</CardTitle>
          </CardHeader>
          <CardContent className="gap-3">
            {participants.map((participant) => (
              <View
                key={participant.id}
                className="flex-row items-center justify-between p-3 bg-muted/50 rounded-lg"
              >
                <View className="flex-1">
                  <Text className="font-medium">
                    {participant.anonymous_name ||
                      participant.anonymous_email ||
                      "Utilisateur"}
                  </Text>
                  <Text className="text-sm text-muted-foreground">
                    {participant.role === "organizer"
                      ? "Organisateur"
                      : "Invité"}
                  </Text>
                </View>
                <View className="flex-row items-center gap-3">
                  <View className="flex-row items-center">
                    <Text className="mr-2">
                      {getStatusEmoji(participant.status)}
                    </Text>
                    <Text className="text-sm">
                      {getStatusText(participant.status)}
                    </Text>
                  </View>
                </View>
              </View>
            ))}

            {participants.length === 0 && (
              <Text className="text-center text-muted-foreground py-8">
                Aucun participant pour le moment
              </Text>
            )}
          </CardContent>
        </Card>

        {/* Bouton retour */}
        <Button
          variant="outline"
          onPress={() => router.back()}
          className="mt-6 w-full"
        >
          <Text>Revenir aux détails de l'événement</Text>
        </Button>
      </View>
    </ScrollView>
  );
}
