/**
 * Service d'analytics pour Party Organizer
 * Calcule les statistiques et métriques pour le tableau de bord organisateur
 */

import { supabase } from './supabase';
import { cache, CACHE_KEYS } from './cacheService';
import { Event, Participant, Item } from './types';

export interface EventStats {
  totalEvents: number;
  upcomingEvents: number;
  pastEvents: number;
  cancelledEvents: number;
  averageParticipants: number;
  totalParticipants: number;
  participationRate: number; // Pourcentage de participants qui viennent réellement
}

export interface FinancialStats {
  totalBudget: number;
  totalSpent: number;
  averageCostPerEvent: number;
  averageCostPerParticipant: number;
  mostExpensiveCategory: string;
  savingsRate: number; // Pourcentage d'économies vs budget initial
}

export interface ParticipantStats {
  totalUniqueParticipants: number;
  averageParticipantsPerEvent: number;
  mostActiveParticipants: Array<{
    name: string;
    eventsCount: number;
    totalContribution: number;
  }>;
  participantRetentionRate: number;
}

export interface ItemStats {
  totalItems: number;
  completedItems: number;
  completionRate: number;
  mostPopularCategories: Array<{
    category: string;
    count: number;
    averageCost: number;
  }>;
  itemsPerEvent: number;
}

export interface TimeStats {
  averageEventDuration: number;
  mostPopularTimeSlots: Array<{
    hour: number;
    count: number;
  }>;
  mostPopularDays: Array<{
    dayOfWeek: number;
    count: number;
  }>;
  seasonalTrends: Array<{
    month: number;
    eventsCount: number;
  }>;
}

export interface OrganizerDashboard {
  eventStats: EventStats;
  financialStats: FinancialStats;
  participantStats: ParticipantStats;
  itemStats: ItemStats;
  timeStats: TimeStats;
  insights: string[];
  recommendations: string[];
  alerts: string[];
  lastUpdated: Date;
}

class AnalyticsService {
  private readonly CACHE_TTL = 10 * 60 * 1000; // 10 minutes

  /**
   * Génère le tableau de bord complet pour un organisateur
   */
  async generateDashboard(organizerId: string): Promise<OrganizerDashboard> {
    try {
      // Vérifier le cache d'abord
      const cacheKey = 'organizer_dashboard';
      const cached = await cache.get<OrganizerDashboard>(cacheKey, { organizerId });
      
      if (cached) {
        console.log('Dashboard loaded from cache');
        return cached;
      }

      console.log('Generating dashboard for organizer:', organizerId);

      // Récupérer toutes les données nécessaires
      const events = await this.getOrganizerEvents(organizerId);
      const participants = await this.getOrganizerParticipants(organizerId);
      const items = await this.getOrganizerItems(organizerId);

      // Calculer les statistiques
      const eventStats = this.calculateEventStats(events);
      const financialStats = await this.calculateFinancialStats(events);
      const participantStats = this.calculateParticipantStats(participants, events);
      const itemStats = this.calculateItemStats(items);
      const timeStats = this.calculateTimeStats(events);

      // Générer insights et recommandations
      const insights = this.generateInsights(eventStats, financialStats, participantStats);
      const recommendations = this.generateRecommendations(eventStats, financialStats, itemStats);
      const alerts = this.generateAlerts(events, eventStats);

      const dashboard: OrganizerDashboard = {
        eventStats,
        financialStats,
        participantStats,
        itemStats,
        timeStats,
        insights,
        recommendations,
        alerts,
        lastUpdated: new Date(),
      };

      // Mettre en cache
      await cache.set(cacheKey, dashboard, { organizerId }, this.CACHE_TTL);

      return dashboard;
    } catch (error) {
      console.error('Error generating dashboard:', error);
      throw error;
    }
  }

  /**
   * Récupère tous les événements d'un organisateur
   */
  private async getOrganizerEvents(organizerId: string): Promise<Event[]> {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('organizer_id', organizerId)
      .order('date_time', { ascending: false });

    if (error) {
      console.error('Error fetching organizer events:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Récupère tous les participants des événements d'un organisateur
   */
  private async getOrganizerParticipants(organizerId: string): Promise<Participant[]> {
    const { data, error } = await supabase
      .from('participants')
      .select(`
        *,
        events!inner(organizer_id)
      `)
      .eq('events.organizer_id', organizerId);

    if (error) {
      console.error('Error fetching organizer participants:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Récupère tous les items des événements d'un organisateur
   */
  private async getOrganizerItems(organizerId: string): Promise<Item[]> {
    const { data, error } = await supabase
      .from('items')
      .select(`
        *,
        events!inner(organizer_id)
      `)
      .eq('events.organizer_id', organizerId);

    if (error) {
      console.error('Error fetching organizer items:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Calcule les statistiques d'événements
   */
  private calculateEventStats(events: Event[]): EventStats {
    const now = new Date();
    const upcoming = events.filter(e => new Date(e.date_time) > now);
    const past = events.filter(e => new Date(e.date_time) <= now);
    
    // Calculer la moyenne de participants (approximation)
    const totalParticipants = events.reduce((sum, event) => {
      // Estimation basée sur le nombre d'items ou une valeur par défaut
      return sum + 5; // À améliorer avec de vraies données
    }, 0);

    return {
      totalEvents: events.length,
      upcomingEvents: upcoming.length,
      pastEvents: past.length,
      cancelledEvents: 0, // À implémenter avec un champ status
      averageParticipants: events.length > 0 ? totalParticipants / events.length : 0,
      totalParticipants,
      participationRate: 85, // À calculer avec de vraies données de présence
    };
  }

  /**
   * Calcule les statistiques financières
   */
  private async calculateFinancialStats(events: Event[]): Promise<FinancialStats> {
    // Récupérer les résumés financiers
    const eventIds = events.map(e => e.id);
    
    if (eventIds.length === 0) {
      return {
        totalBudget: 0,
        totalSpent: 0,
        averageCostPerEvent: 0,
        averageCostPerParticipant: 0,
        mostExpensiveCategory: 'Aucune',
        savingsRate: 0,
      };
    }

    const { data: summaries, error } = await supabase
      .from('financial_summaries')
      .select('*')
      .in('event_id', eventIds);

    if (error) {
      console.error('Error fetching financial summaries:', error);
      return {
        totalBudget: 0,
        totalSpent: 0,
        averageCostPerEvent: 0,
        averageCostPerParticipant: 0,
        mostExpensiveCategory: 'Aucune',
        savingsRate: 0,
      };
    }

    const totalSpent = summaries?.reduce((sum, s) => sum + (s.total_paid || 0), 0) || 0;
    const totalBudget = totalSpent * 1.2; // Estimation du budget initial

    return {
      totalBudget,
      totalSpent,
      averageCostPerEvent: events.length > 0 ? totalSpent / events.length : 0,
      averageCostPerParticipant: totalSpent > 0 ? totalSpent / (events.length * 5) : 0,
      mostExpensiveCategory: 'Nourriture', // À calculer avec de vraies données
      savingsRate: totalBudget > 0 ? ((totalBudget - totalSpent) / totalBudget) * 100 : 0,
    };
  }

  /**
   * Calcule les statistiques de participants
   */
  private calculateParticipantStats(participants: Participant[], events: Event[]): ParticipantStats {
    const uniqueParticipants = new Set(participants.map(p => p.user_id || p.anonymous_name)).size;
    
    return {
      totalUniqueParticipants: uniqueParticipants,
      averageParticipantsPerEvent: events.length > 0 ? participants.length / events.length : 0,
      mostActiveParticipants: [], // À implémenter avec de vraies données
      participantRetentionRate: 75, // À calculer avec de vraies données
    };
  }

  /**
   * Calcule les statistiques d'items
   */
  private calculateItemStats(items: Item[]): ItemStats {
    const completed = items.filter(i => i.completed).length;
    
    // Grouper par catégorie
    const categoryStats = items.reduce((acc, item) => {
      const category = item.category || 'Autre';
      if (!acc[category]) {
        acc[category] = { count: 0, totalCost: 0 };
      }
      acc[category].count++;
      acc[category].totalCost += item.actual_cost || 0;
      return acc;
    }, {} as Record<string, { count: number; totalCost: number }>);

    const mostPopularCategories = Object.entries(categoryStats)
      .map(([category, stats]) => ({
        category,
        count: stats.count,
        averageCost: stats.count > 0 ? stats.totalCost / stats.count : 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalItems: items.length,
      completedItems: completed,
      completionRate: items.length > 0 ? (completed / items.length) * 100 : 0,
      mostPopularCategories,
      itemsPerEvent: 0, // À calculer avec le nombre d'événements
    };
  }

  /**
   * Calcule les statistiques temporelles
   */
  private calculateTimeStats(events: Event[]): TimeStats {
    // Analyser les heures populaires
    const hourStats = events.reduce((acc, event) => {
      const hour = new Date(event.date_time).getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const mostPopularTimeSlots = Object.entries(hourStats)
      .map(([hour, count]) => ({ hour: parseInt(hour), count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);

    // Analyser les jours populaires
    const dayStats = events.reduce((acc, event) => {
      const day = new Date(event.date_time).getDay();
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const mostPopularDays = Object.entries(dayStats)
      .map(([day, count]) => ({ dayOfWeek: parseInt(day), count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);

    return {
      averageEventDuration: 3, // À calculer avec de vraies données
      mostPopularTimeSlots,
      mostPopularDays,
      seasonalTrends: [], // À implémenter
    };
  }

  /**
   * Génère des insights intelligents
   */
  private generateInsights(
    eventStats: EventStats,
    financialStats: FinancialStats,
    participantStats: ParticipantStats
  ): string[] {
    const insights: string[] = [];

    if (eventStats.totalEvents > 5) {
      insights.push(`🎉 Vous avez organisé ${eventStats.totalEvents} événements ! Vous êtes un organisateur expérimenté.`);
    }

    if (financialStats.savingsRate > 10) {
      insights.push(`💰 Excellent ! Vous économisez ${financialStats.savingsRate.toFixed(1)}% sur vos budgets.`);
    }

    if (participantStats.participantRetentionRate > 70) {
      insights.push(`👥 Vos participants reviennent souvent (${participantStats.participantRetentionRate}% de fidélité) !`);
    }

    if (eventStats.averageParticipants > 8) {
      insights.push(`🎊 Vos événements attirent en moyenne ${eventStats.averageParticipants.toFixed(1)} participants.`);
    }

    return insights;
  }

  /**
   * Génère des recommandations
   */
  private generateRecommendations(
    eventStats: EventStats,
    financialStats: FinancialStats,
    itemStats: ItemStats
  ): string[] {
    const recommendations: string[] = [];

    if (itemStats.completionRate < 70) {
      recommendations.push('📝 Essayez de simplifier vos listes d\'items pour améliorer le taux de completion.');
    }

    if (financialStats.averageCostPerParticipant > 50) {
      recommendations.push('💡 Considérez des alternatives moins coûteuses pour réduire les frais par participant.');
    }

    if (eventStats.upcomingEvents === 0) {
      recommendations.push('🚀 Planifiez votre prochain événement ! Vos participants vous attendent.');
    }

    return recommendations;
  }

  /**
   * Génère des alertes
   */
  private generateAlerts(events: Event[], eventStats: EventStats): string[] {
    const alerts: string[] = [];
    const now = new Date();
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    // Événements proches sans préparation
    const upcomingEvents = events.filter(e => {
      const eventDate = new Date(e.date_time);
      return eventDate > now && eventDate <= nextWeek;
    });

    if (upcomingEvents.length > 0) {
      alerts.push(`⏰ ${upcomingEvents.length} événement(s) dans les 7 prochains jours. Vérifiez la préparation !`);
    }

    return alerts;
  }

  /**
   * Invalide le cache du dashboard
   */
  async invalidateDashboard(organizerId: string): Promise<void> {
    await cache.delete('organizer_dashboard', { organizerId });
  }
}

// Instance singleton
export const analyticsService = new AnalyticsService();

// Fonctions utilitaires
export const analytics = {
  generateDashboard: (organizerId: string) => analyticsService.generateDashboard(organizerId),
  invalidateDashboard: (organizerId: string) => analyticsService.invalidateDashboard(organizerId),
};
