// --- Begin lib/toastService.ts ---
import { create } from "zustand";

type ToastType = "success" | "error" | "info";

interface ToastState {
  isVisible: boolean;
  message: string;
  type: ToastType;
  duration: number;
  show: (
    message: string,
    options?: { type?: ToastType; duration?: number }
  ) => void;
  hide: () => void;
}

export const useToastStore = create<ToastState>((set) => ({
  isVisible: false,
  message: "",
  type: "info",
  duration: 3000,
  show: (message, options = {}) =>
    set({
      message,
      type: options.type || "info",
      duration: options.duration || 3000,
      isVisible: true,
    }),
  hide: () => set({ isVisible: false }),
}));

// Fonction utilitaire pour un accès facile
export const showToast = (
  message: string,
  options?: { type?: ToastType; duration?: number }
) => {
  useToastStore.getState().show(message, options);
};

// --- End lib/toastService.ts ---
