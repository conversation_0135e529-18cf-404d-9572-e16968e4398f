import React from "react";
import { View, Platform } from "react-native";
import { Text } from "~/components/ui/text";

// Import conditionnel pour éviter les erreurs sur les plateformes non supportées
let QRCodeSVG: any = null;
let QRCode: any = null;

try {
  if (Platform.OS === "web") {
    QRCode = require("react-qr-code").default;
  } else {
    QRCodeSVG = require("react-native-qrcode-svg").default;
  }
} catch (error) {
  console.warn("QR Code library not available:", error);
}

interface QRCodeGeneratorProps {
  value: string;
  size?: number;
  backgroundColor?: string;
  foregroundColor?: string;
  logo?: string;
  title?: string;
  description?: string;
}

export function QRCodeGenerator({
  value,
  size = 200,
  backgroundColor = "#ffffff",
  foregroundColor = "#000000",
  logo,
  title,
  description,
}: QRCodeGeneratorProps) {
  // Vérifier si les librairies sont disponibles
  if (!QRCodeSVG && !QRCode) {
    return (
      <View 
        className="items-center justify-center border-2 border-dashed border-muted-foreground/30 rounded-lg"
        style={{ width: size, height: size }}
      >
        <Text className="text-muted-foreground text-center text-sm">
          QR Code non disponible
        </Text>
      </View>
    );
  }

  const renderQRCode = () => {
    if (Platform.OS === "web" && QRCode) {
      return (
        <QRCode
          value={value}
          size={size}
          bgColor={backgroundColor}
          fgColor={foregroundColor}
          level="M"
          includeMargin={true}
        />
      );
    } else if (QRCodeSVG) {
      return (
        <QRCodeSVG
          value={value}
          size={size}
          backgroundColor={backgroundColor}
          color={foregroundColor}
          logo={logo ? { uri: logo } : undefined}
          logoSize={logo ? size * 0.2 : undefined}
          logoBackgroundColor={backgroundColor}
          logoMargin={2}
          logoBorderRadius={4}
          quietZone={10}
          enableLinearGradient={false}
        />
      );
    }
    
    return null;
  };

  return (
    <View className="items-center">
      {title && (
        <Text className="text-lg font-semibold mb-2 text-center">
          {title}
        </Text>
      )}
      
      <View 
        className="bg-white p-4 rounded-lg shadow-sm border border-border"
        style={{ 
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        }}
      >
        {renderQRCode()}
      </View>
      
      {description && (
        <Text className="text-sm text-muted-foreground mt-2 text-center max-w-[250px]">
          {description}
        </Text>
      )}
    </View>
  );
}

// Fonction utilitaire pour générer l'URL de partage
export function generateShareUrl(eventId: string | number, baseUrl?: string): string {
  const base = baseUrl || 
    (Platform.OS === "web" && typeof window !== "undefined" 
      ? window.location.origin 
      : "https://party-organizer.app");
  
  return `${base}/join/${eventId}`;
}

// Fonction utilitaire pour générer le message de partage
export function generateShareMessage(eventTitle: string, shareUrl: string, customMessage?: string): string {
  if (customMessage) {
    return customMessage.replace("{title}", eventTitle).replace("{url}", shareUrl);
  }
  
  return `🎉 Rejoignez l'événement "${eventTitle}" !\n\n📱 Scannez le QR code ou cliquez sur le lien :\n${shareUrl}`;
}

// Fonction pour générer les liens de partage optimisés
export function generateSocialShareLinks(eventTitle: string, shareUrl: string) {
  const message = encodeURIComponent(generateShareMessage(eventTitle, shareUrl));
  const title = encodeURIComponent(`Événement: ${eventTitle}`);
  
  return {
    whatsapp: `https://wa.me/?text=${message}`,
    telegram: `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(eventTitle)}`,
    sms: Platform.OS === "ios" 
      ? `sms:&body=${message}`
      : `sms:?body=${message}`,
    email: `mailto:?subject=${title}&body=${message}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
    twitter: `https://twitter.com/intent/tweet?text=${message}`,
  };
}
