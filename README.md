# Party Organizer

Une application mobile pour organiser facilement des événements et des fêtes avec vos amis. G<PERSON>rez les invitations, les tâches et les items à apporter pour chaque événement.

## Fonctionnalités

- **Gestion d'événements** : <PERSON><PERSON><PERSON>, modifiez et supprimez des événements
- **Gestion des participants** : Invitez des amis et suivez leur statut de participation
- **Liste d'items** : Gérez les items à apporter pour chaque événement
- **Contacts** : Gérez votre liste de contacts pour faciliter les invitations
- **Profil utilisateur** : Personnalisez votre profil et gérez vos paramètres
- **Mode sombre/clair** : Interface adaptative selon vos préférences
- **Multi-plateforme** : Fonctionne sur iOS, Android et Web

## Prérequis

- [Node.js](https://nodejs.org/) (version 16 ou supérieure)
- [npm](https://www.npmjs.com/) ou [yarn](https://yarnpkg.com/) ou [pnpm](https://pnpm.io/)
- [Expo CLI](https://docs.expo.dev/get-started/installation/)
- [Git](https://git-scm.com/)

## Installation

1. Clonez le dépôt :

   ```bash
   git clone https://github.com/votre-username/party-organizer.git
   cd party-organizer
   ```

2. Installez les dépendances :

   ```bash
   npm install
   # ou
   yarn install
   # ou
   pnpm install
   ```

3. Installez les peer dependencies :

   ```bash
   npx expo install --fix
   ```

4. Configurez les variables d'environnement :

   - Créez un fichier `.env` à la racine du projet avec les informations suivantes :

   ```
   EXPO_PUBLIC_SUPABASE_URL=votre_url_supabase
   EXPO_PUBLIC_SUPABASE_ANON_KEY=votre_clé_anon
   EXPO_PUBLIC_SUPABASE_SERVICE_KEY=votre_clé_service
   ```

5. Générez les fichiers CSS pour NativeWind :
   ```bash
   npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css
   ```

## Démarrage

Pour lancer l'application en mode développement :

```bash
npx expo start --clear
```

Options spécifiques à la plateforme :

- iOS : `npm run ios` ou `npx expo start --clear --ios`
- Android : `npm run android` ou `npx expo start --clear --android`
- Web : `npm run web` ou `npx expo start --clear --web`

## Structure du projet

```
party-organizer/
├── app/                    # Dossier principal des écrans (Expo Router)
│   ├── (tabs)/             # Écrans accessibles via la barre d'onglets
│   │   ├── index.tsx       # Écran principal (liste des événements)
│   │   ├── contacts.tsx    # Gestion des contacts
│   │   ├── tasks.tsx       # Liste des tâches assignées
│   │   ├── messages.tsx    # Messagerie (à venir)
│   │   ├── profile.tsx     # Profil utilisateur et paramètres
│   │   └── _layout.tsx     # Configuration de la barre d'onglets
│   ├── event/              # Écrans liés aux événements
│   │   ├── [id]/           # Détails d'un événement spécifique
│   │   │   ├── index.tsx   # Affichage des détails de l'événement
│   │   │   └── _layout.tsx # Configuration de la navigation
│   │   └── create.tsx      # Création d'un nouvel événement
│   └── _layout.tsx         # Configuration globale de la navigation
├── assets/                 # Images, polices et autres ressources
├── components/             # Composants réutilisables
│   ├── ui/                 # Composants d'interface utilisateur
│   └── ...                 # Autres composants spécifiques
├── lib/                    # Bibliothèques et utilitaires
│   ├── supabase.ts         # Configuration de Supabase
│   ├── supabaseCrud.ts     # Fonctions CRUD pour Supabase
│   ├── types.ts            # Types TypeScript
│   └── ...                 # Autres utilitaires
├── supabase/               # Configuration Supabase
│   └── sql/                # Scripts SQL pour la base de données
├── .env                    # Variables d'environnement (à créer)
├── global.css              # Styles CSS globaux
├── tailwind.config.js      # Configuration de Tailwind CSS
└── package.json            # Dépendances et scripts
```

## Base de données

L'application utilise Supabase comme backend. Les scripts SQL pour configurer la base de données se trouvent dans le dossier `supabase/sql/`.

Pour configurer la base de données :

1. Créez un projet sur [Supabase](https://supabase.com/)
2. Exécutez les scripts SQL dans l'ordre suivant :
   - `schema.sql` - Structure de la base de données
   - `functions.sql` - Fonctions SQL
   - `policies.sql` - Politiques de sécurité
   - `fixes.sql` - Corrections pour les problèmes connus

## Technologies utilisées

- [React Native](https://reactnative.dev/) - Framework mobile
- [Expo](https://expo.dev/) - Plateforme de développement React Native
- [Expo Router](https://docs.expo.dev/router/introduction/) - Navigation
- [NativeWind](https://www.nativewind.dev/) - Tailwind CSS pour React Native
- [Supabase](https://supabase.com/) - Backend as a Service
- [TypeScript](https://www.typescriptlang.org/) - Typage statique
- [Lucide Icons](https://lucide.dev/) - Icônes

## Dépannage

- **Erreur de compilation NativeWind** : Exécutez `npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css`
- **Problèmes avec Expo** : Utilisez `npx expo start --clear` pour nettoyer le cache
- **Erreurs de dépendances** : Exécutez `npx expo install --fix` pour résoudre les problèmes de peer dependencies
- **Erreur "Unknown event handler property onPress"** : Cette erreur se produit sur le web car React Native Web utilise `onClick` au lieu de `onPress`. Utilisez l'utilitaire `handlePressEvent` du fichier `lib/platformUtils.ts` pour gérer les événements de manière compatible :

  ```tsx
  import { handlePressEvent } from "~/lib/platformUtils";

  // Au lieu de :
  <Button onPress={() => doSomething()}>
    <Text>Click me</Text>
  </Button>

  // Utilisez :
  <Button {...handlePressEvent(() => doSomething())}>
    <Text>Click me</Text>
  </Button>
  ```

- **Problèmes de compatibilité web/mobile** : Si vous rencontrez d'autres problèmes de compatibilité entre le web et mobile, vérifiez que vous utilisez des conditions basées sur la plateforme :

  ```tsx
  import { Platform } from "react-native";

  // Exemple de code conditionnel selon la plateforme
  {
    Platform.OS === "web" ? (
      <WebSpecificComponent />
    ) : (
      <MobileSpecificComponent />
    );
  }
  ```

- **Avertissements "MOCK profile data" ou "needs refactoring"** : Ces avertissements ont été corrigés dans la dernière version. Si vous les voyez encore :

  - Assurez-vous d'avoir la dernière version du code
  - Vérifiez que votre base de données Supabase est correctement configurée
  - Les fonctions utilisent maintenant un système de fallback automatique vers le client admin en cas de problème RLS

- **Erreurs de politiques RLS (Row Level Security)** : L'application gère automatiquement les erreurs de récursion infinie en utilisant le client admin comme fallback. Si vous rencontrez des problèmes persistants :

  - Vérifiez que votre clé `EXPO_PUBLIC_SUPABASE_SERVICE_KEY` est correctement configurée
  - Consultez les logs pour identifier les opérations problématiques
  - Utilisez le script de test `lib/testCrud.ts` pour diagnostiquer les problèmes

- **Erreur "ScrollView child layout must be applied through contentContainerStyle"** : Cette erreur se produit quand des propriétés de layout (`alignItems`, `justifyContent`, `flex`) sont appliquées directement au ScrollView au lieu d'utiliser `contentContainerStyle`. Solution :

  ```tsx
  // ❌ Incorrect
  <ScrollView className="flex-1 justify-center items-center">
    <View>Content</View>
  </ScrollView>

  // ✅ Correct
  <ScrollView
    style={{ flex: 1 }}
    contentContainerStyle={{ justifyContent: 'center', alignItems: 'center' }}
  >
    <View>Content</View>
  </ScrollView>
  ```

- **Erreur "View config getter callback for component `button` must be a function"** : Cette erreur se produit quand un élément HTML `button` (minuscule) est utilisé dans React Native. Solution :

  ```tsx
  // ❌ Incorrect (ne fonctionne que sur le web)
  <button onClick={handleClick}>Click me</button>;

  // ✅ Correct (fonctionne sur toutes les plateformes)
  import { Button } from "~/components/ui/button";
  <Button onPress={handleClick}>
    <Text>Click me</Text>
  </Button>;

  // Ou utiliser l'utilitaire pour la compatibilité
  import { handlePressEvent } from "~/lib/platformUtils";
  <Button {...handlePressEvent(handleClick)}>
    <Text>Click me</Text>
  </Button>;
  ```
