import AsyncStorage from '@react-native-async-storage/async-storage';
import { Event, Item, Participant, Contact } from './types';

// Types pour le cache
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface CacheConfig {
  ttl: number; // Time to live en millisecondes
  maxSize: number; // Nombre maximum d'entrées
}

// Configuration par défaut
const DEFAULT_CONFIG: CacheConfig = {
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 100,
};

// Configuration spécifique par type de données
const CACHE_CONFIGS: Record<string, CacheConfig> = {
  events: { ttl: 10 * 60 * 1000, maxSize: 50 }, // 10 minutes
  items: { ttl: 5 * 60 * 1000, maxSize: 200 }, // 5 minutes
  participants: { ttl: 15 * 60 * 1000, maxSize: 100 }, // 15 minutes
  contacts: { ttl: 30 * 60 * 1000, maxSize: 50 }, // 30 minutes
  profile: { ttl: 60 * 60 * 1000, maxSize: 10 }, // 1 heure
};

class CacheManager {
  private memoryCache = new Map<string, CacheEntry<any>>();
  private cacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    evictions: 0,
  };

  /**
   * Génère une clé de cache unique
   */
  private generateKey(type: string, identifier: string | number): string {
    return `${type}:${identifier}`;
  }

  /**
   * Vérifie si une entrée de cache est valide
   */
  private isValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() < entry.expiresAt;
  }

  /**
   * Obtient la configuration pour un type de données
   */
  private getConfig(type: string): CacheConfig {
    return CACHE_CONFIGS[type] || DEFAULT_CONFIG;
  }

  /**
   * Nettoie les entrées expirées du cache mémoire
   */
  private cleanupMemoryCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.memoryCache.entries()) {
      if (now >= entry.expiresAt) {
        this.memoryCache.delete(key);
        this.cacheStats.evictions++;
      }
    }
  }

  /**
   * Évince les anciennes entrées si le cache est plein
   */
  private evictIfNeeded(type: string): void {
    const config = this.getConfig(type);
    const typeEntries = Array.from(this.memoryCache.entries())
      .filter(([key]) => key.startsWith(`${type}:`))
      .sort(([, a], [, b]) => a.timestamp - b.timestamp);

    while (typeEntries.length >= config.maxSize) {
      const [oldestKey] = typeEntries.shift()!;
      this.memoryCache.delete(oldestKey);
      this.cacheStats.evictions++;
    }
  }

  /**
   * Récupère une valeur du cache
   */
  async get<T>(type: string, identifier: string | number): Promise<T | null> {
    const key = this.generateKey(type, identifier);

    // Vérifier le cache mémoire d'abord
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry && this.isValid(memoryEntry)) {
      this.cacheStats.hits++;
      return memoryEntry.data;
    }

    // Vérifier le stockage persistant
    try {
      const stored = await AsyncStorage.getItem(key);
      if (stored) {
        const entry: CacheEntry<T> = JSON.parse(stored);
        if (this.isValid(entry)) {
          // Remettre en cache mémoire
          this.memoryCache.set(key, entry);
          this.cacheStats.hits++;
          return entry.data;
        } else {
          // Supprimer l'entrée expirée
          await AsyncStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.warn('Cache read error:', error);
    }

    this.cacheStats.misses++;
    return null;
  }

  /**
   * Stocke une valeur dans le cache
   */
  async set<T>(type: string, identifier: string | number, data: T): Promise<void> {
    const key = this.generateKey(type, identifier);
    const config = this.getConfig(type);
    const now = Date.now();

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + config.ttl,
    };

    // Nettoyer et éviter si nécessaire
    this.cleanupMemoryCache();
    this.evictIfNeeded(type);

    // Stocker en mémoire
    this.memoryCache.set(key, entry);

    // Stocker de manière persistante (en arrière-plan)
    try {
      await AsyncStorage.setItem(key, JSON.stringify(entry));
    } catch (error) {
      console.warn('Cache write error:', error);
    }

    this.cacheStats.sets++;
  }

  /**
   * Supprime une entrée du cache
   */
  async remove(type: string, identifier: string | number): Promise<void> {
    const key = this.generateKey(type, identifier);
    
    this.memoryCache.delete(key);
    
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.warn('Cache remove error:', error);
    }
  }

  /**
   * Supprime toutes les entrées d'un type
   */
  async removeByType(type: string): Promise<void> {
    const prefix = `${type}:`;
    
    // Supprimer du cache mémoire
    for (const key of this.memoryCache.keys()) {
      if (key.startsWith(prefix)) {
        this.memoryCache.delete(key);
      }
    }

    // Supprimer du stockage persistant
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const keysToRemove = allKeys.filter(key => key.startsWith(prefix));
      await AsyncStorage.multiRemove(keysToRemove);
    } catch (error) {
      console.warn('Cache removeByType error:', error);
    }
  }

  /**
   * Vide complètement le cache
   */
  async clear(): Promise<void> {
    this.memoryCache.clear();
    
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.warn('Cache clear error:', error);
    }

    this.cacheStats = { hits: 0, misses: 0, sets: 0, evictions: 0 };
  }

  /**
   * Obtient les statistiques du cache
   */
  getStats() {
    const total = this.cacheStats.hits + this.cacheStats.misses;
    const hitRate = total > 0 ? (this.cacheStats.hits / total) * 100 : 0;

    return {
      ...this.cacheStats,
      hitRate: Math.round(hitRate * 100) / 100,
      memorySize: this.memoryCache.size,
    };
  }

  /**
   * Précharge les données fréquemment utilisées
   */
  async preload(userId: string): Promise<void> {
    // Cette méthode sera appelée au démarrage de l'app
    // pour précharger les données importantes
    console.log('Cache preloading for user:', userId);
  }
}

// Instance singleton
export const cacheManager = new CacheManager();

// Fonctions utilitaires pour les types spécifiques
export const EventCache = {
  get: (id: number) => cacheManager.get<Event>('events', id),
  set: (id: number, event: Event) => cacheManager.set('events', id, event),
  remove: (id: number) => cacheManager.remove('events', id),
  clear: () => cacheManager.removeByType('events'),
};

export const ItemCache = {
  get: (eventId: number) => cacheManager.get<Item[]>('items', `event_${eventId}`),
  set: (eventId: number, items: Item[]) => cacheManager.set('items', `event_${eventId}`, items),
  remove: (eventId: number) => cacheManager.remove('items', `event_${eventId}`),
  clear: () => cacheManager.removeByType('items'),
};

export const ParticipantCache = {
  get: (eventId: number) => cacheManager.get<Participant[]>('participants', `event_${eventId}`),
  set: (eventId: number, participants: Participant[]) => cacheManager.set('participants', `event_${eventId}`, participants),
  remove: (eventId: number) => cacheManager.remove('participants', `event_${eventId}`),
  clear: () => cacheManager.removeByType('participants'),
};

export const ContactCache = {
  get: (userId: string) => cacheManager.get<Contact[]>('contacts', userId),
  set: (userId: string, contacts: Contact[]) => cacheManager.set('contacts', userId, contacts),
  remove: (userId: string) => cacheManager.remove('contacts', userId),
  clear: () => cacheManager.removeByType('contacts'),
};

// Fonction pour invalider le cache lors de mutations
export const invalidateCache = {
  event: (eventId: number) => {
    EventCache.remove(eventId);
    ItemCache.remove(eventId);
    ParticipantCache.remove(eventId);
  },
  
  allEvents: () => {
    EventCache.clear();
    ItemCache.clear();
    ParticipantCache.clear();
  },
  
  user: (userId: string) => {
    ContactCache.remove(userId);
    cacheManager.removeByType('profile');
  },
};
