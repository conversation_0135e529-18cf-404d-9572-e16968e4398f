/**
 * Script d'initialisation des achievements pour Party Organizer
 * Exécute les scripts SQL pour créer les tables et insérer les badges
 */

import { supabaseAdmin } from '../lib/supabase';
import { readFileSync } from 'fs';
import { join } from 'path';

async function initializeAchievements() {
  try {
    console.log('🚀 Initialisation du système d\'achievements...');

    // Lire le schéma des achievements
    const schemaPath = join(__dirname, '../database/achievements_schema.sql');
    const schemaSQL = readFileSync(schemaPath, 'utf8');

    console.log('📋 Création des tables d\'achievements...');
    
    // Exécuter le schéma
    const { error: schemaError } = await supabaseAdmin.rpc('exec_sql', {
      sql: schemaSQL
    });

    if (schemaError) {
      console.error('❌ Erreur lors de la création du schéma:', schemaError);
      return;
    }

    console.log('✅ Tables créées avec succès');

    // Lire les badges prédéfinis
    const badgesPath = join(__dirname, '../database/badges_seed.sql');
    const badgesSQL = readFileSync(badgesPath, 'utf8');

    console.log('🏆 Insertion des badges prédéfinis...');

    // Exécuter l'insertion des badges
    const { error: badgesError } = await supabaseAdmin.rpc('exec_sql', {
      sql: badgesSQL
    });

    if (badgesError) {
      console.error('❌ Erreur lors de l\'insertion des badges:', badgesError);
      return;
    }

    console.log('✅ Badges insérés avec succès');

    // Vérifier l'insertion
    const { data: badges, error: countError } = await supabaseAdmin
      .from('badges')
      .select('count(*)', { count: 'exact' });

    if (countError) {
      console.error('❌ Erreur lors de la vérification:', countError);
      return;
    }

    console.log(`🎉 Initialisation terminée ! ${badges?.length || 0} badges disponibles`);

  } catch (error) {
    console.error('💥 Erreur fatale:', error);
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  initializeAchievements();
}

export { initializeAchievements };
