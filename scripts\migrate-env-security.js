#!/usr/bin/env node

/**
 * Script de migration pour sécuriser les variables d'environnement Supabase
 * 
 * Ce script aide à migrer de EXPO_PUBLIC_SUPABASE_SERVICE_KEY vers SUPABASE_SERVICE_ROLE_KEY
 * pour éviter l'exposition de la clé service côté client.
 */

const fs = require('fs');
const path = require('path');

const ENV_FILE = path.join(process.cwd(), '.env');
const ENV_EXAMPLE_FILE = path.join(process.cwd(), '.env.example');

console.log('🔒 Migration de sécurité des variables d\'environnement Supabase\n');

// Vérifier si le fichier .env existe
if (!fs.existsSync(ENV_FILE)) {
  console.log('❌ Fichier .env non trouvé.');
  console.log('📝 Créez un fichier .env basé sur .env.example\n');
  
  if (fs.existsSync(ENV_EXAMPLE_FILE)) {
    console.log('💡 Commande suggérée:');
    console.log('   cp .env.example .env\n');
  }
  process.exit(1);
}

// Lire le fichier .env
let envContent = fs.readFileSync(ENV_FILE, 'utf8');
let hasChanges = false;

console.log('🔍 Analyse du fichier .env...\n');

// Vérifier si la clé service est exposée
if (envContent.includes('EXPO_PUBLIC_SUPABASE_SERVICE_KEY=')) {
  console.log('⚠️  PROBLÈME DE SÉCURITÉ DÉTECTÉ:');
  console.log('   EXPO_PUBLIC_SUPABASE_SERVICE_KEY est exposée côté client!\n');
  
  // Proposer la migration
  console.log('🔧 Migration automatique...');
  
  // Remplacer EXPO_PUBLIC_SUPABASE_SERVICE_KEY par SUPABASE_SERVICE_ROLE_KEY
  envContent = envContent.replace(
    /EXPO_PUBLIC_SUPABASE_SERVICE_KEY=/g,
    'SUPABASE_SERVICE_ROLE_KEY='
  );
  
  hasChanges = true;
  console.log('✅ EXPO_PUBLIC_SUPABASE_SERVICE_KEY → SUPABASE_SERVICE_ROLE_KEY\n');
}

// Ajouter des commentaires de sécurité si nécessaire
if (!envContent.includes('# Variables privées')) {
  const lines = envContent.split('\n');
  const serviceKeyIndex = lines.findIndex(line => line.startsWith('SUPABASE_SERVICE_ROLE_KEY='));
  
  if (serviceKeyIndex !== -1) {
    lines.splice(serviceKeyIndex, 0, 
      '',
      '# Variables privées (JAMAIS exposées côté client)',
      '# ⚠️ IMPORTANT: Cette clé ne doit JAMAIS être préfixée par EXPO_PUBLIC_'
    );
    envContent = lines.join('\n');
    hasChanges = true;
    console.log('📝 Commentaires de sécurité ajoutés\n');
  }
}

// Sauvegarder les changements
if (hasChanges) {
  // Créer une sauvegarde
  const backupFile = `${ENV_FILE}.backup.${Date.now()}`;
  fs.copyFileSync(ENV_FILE, backupFile);
  console.log(`💾 Sauvegarde créée: ${path.basename(backupFile)}`);
  
  // Écrire le nouveau fichier
  fs.writeFileSync(ENV_FILE, envContent);
  console.log('✅ Fichier .env mis à jour avec succès!\n');
  
  console.log('🔒 SÉCURITÉ AMÉLIORÉE:');
  console.log('   ✓ Clé service non exposée côté client');
  console.log('   ✓ Variables correctement séparées');
  console.log('   ✓ Commentaires de sécurité ajoutés\n');
} else {
  console.log('✅ Aucun problème de sécurité détecté dans .env\n');
}

// Vérifications finales
console.log('🔍 Vérifications finales:');

const finalContent = fs.readFileSync(ENV_FILE, 'utf8');

if (finalContent.includes('EXPO_PUBLIC_SUPABASE_SERVICE_KEY=')) {
  console.log('❌ EXPO_PUBLIC_SUPABASE_SERVICE_KEY encore présente');
} else {
  console.log('✅ Aucune clé service exposée');
}

if (finalContent.includes('SUPABASE_SERVICE_ROLE_KEY=')) {
  console.log('✅ SUPABASE_SERVICE_ROLE_KEY configurée');
} else {
  console.log('⚠️  SUPABASE_SERVICE_ROLE_KEY manquante');
}

console.log('\n🎯 PROCHAINES ÉTAPES:');
console.log('1. Vérifiez que votre .env contient les bonnes valeurs');
console.log('2. Redémarrez votre serveur de développement');
console.log('3. Vérifiez les logs pour les avertissements de sécurité');
console.log('\n🚀 Migration terminée!');
