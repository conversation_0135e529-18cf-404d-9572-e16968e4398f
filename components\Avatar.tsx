import React from 'react';
import { View, Text, Image, ActivityIndicator } from 'react-native';
import { avatarService } from '~/lib/avatarService';
import { cn } from '~/lib/utils';

interface AvatarProps {
  avatarUrl?: string | null;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallbackEmoji?: string;
  loading?: boolean;
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12',
  lg: 'w-16 h-16',
  xl: 'w-20 h-20',
};

const emojiSizeClasses = {
  sm: 'text-lg',
  md: 'text-2xl',
  lg: 'text-3xl',
  xl: 'text-4xl',
};

export function Avatar({
  avatarUrl,
  size = 'md',
  className,
  fallbackEmoji,
  loading = false,
}: AvatarProps) {
  const avatarData = avatarService.getAvatarType(avatarUrl);
  
  // Si un emoji de fallback est fourni, l'utiliser
  if (fallbackEmoji && !avatarUrl) {
    avatarData.value = fallbackEmoji;
    avatarData.type = 'emoji';
  }

  const containerClasses = cn(
    'rounded-full bg-muted items-center justify-center overflow-hidden',
    sizeClasses[size],
    className
  );

  if (loading) {
    return (
      <View className={containerClasses}>
        <ActivityIndicator size="small" />
      </View>
    );
  }

  if (avatarData.type === 'photo') {
    return (
      <View className={containerClasses}>
        <Image
          source={{ uri: avatarData.value }}
          className="w-full h-full"
          resizeMode="cover"
          onError={() => {
            // En cas d'erreur de chargement, afficher un emoji par défaut
            console.warn('Erreur de chargement de l\'avatar:', avatarData.value);
          }}
        />
      </View>
    );
  }

  // Affichage emoji
  return (
    <View className={containerClasses}>
      <Text className={cn(emojiSizeClasses[size])}>
        {avatarData.value}
      </Text>
    </View>
  );
}

// Composant Avatar avec indicateur de statut
interface AvatarWithStatusProps extends AvatarProps {
  status?: 'online' | 'offline' | 'away';
  showStatus?: boolean;
}

export function AvatarWithStatus({
  status = 'offline',
  showStatus = false,
  ...avatarProps
}: AvatarWithStatusProps) {
  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    away: 'bg-yellow-500',
  };

  const statusSizes = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
    xl: 'w-5 h-5',
  };

  return (
    <View className="relative">
      <Avatar {...avatarProps} />
      {showStatus && (
        <View
          className={cn(
            'absolute -bottom-0.5 -right-0.5 rounded-full border-2 border-background',
            statusColors[status],
            statusSizes[avatarProps.size || 'md']
          )}
        />
      )}
    </View>
  );
}

// Composant Avatar cliquable avec overlay
interface AvatarButtonProps extends AvatarProps {
  onPress?: () => void;
  editable?: boolean;
}

export function AvatarButton({
  onPress,
  editable = false,
  ...avatarProps
}: AvatarButtonProps) {
  if (!onPress && !editable) {
    return <Avatar {...avatarProps} />;
  }

  return (
    <View className="relative">
      <Avatar {...avatarProps} />
      {editable && (
        <View className="absolute inset-0 rounded-full bg-black/30 items-center justify-center">
          <Text className="text-white text-xs font-medium">✏️</Text>
        </View>
      )}
    </View>
  );
}

// Groupe d'avatars (pour afficher plusieurs utilisateurs)
interface AvatarGroupProps {
  avatars: Array<{
    id: string;
    avatarUrl?: string | null;
    name?: string;
  }>;
  size?: 'sm' | 'md' | 'lg';
  max?: number;
  className?: string;
}

export function AvatarGroup({
  avatars,
  size = 'md',
  max = 3,
  className,
}: AvatarGroupProps) {
  const displayAvatars = avatars.slice(0, max);
  const remainingCount = Math.max(0, avatars.length - max);

  const overlapClasses = {
    sm: '-ml-2',
    md: '-ml-3',
    lg: '-ml-4',
  };

  return (
    <View className={cn('flex-row items-center', className)}>
      {displayAvatars.map((avatar, index) => (
        <View
          key={avatar.id}
          className={cn(
            'border-2 border-background rounded-full',
            index > 0 && overlapClasses[size]
          )}
          style={{ zIndex: displayAvatars.length - index }}
        >
          <Avatar
            avatarUrl={avatar.avatarUrl}
            size={size}
            fallbackEmoji={avatar.name ? avatar.name.charAt(0).toUpperCase() : undefined}
          />
        </View>
      ))}
      
      {remainingCount > 0 && (
        <View
          className={cn(
            'rounded-full bg-muted border-2 border-background items-center justify-center',
            sizeClasses[size],
            overlapClasses[size]
          )}
        >
          <Text className={cn('font-medium text-muted-foreground', emojiSizeClasses[size])}>
            +{remainingCount}
          </Text>
        </View>
      )}
    </View>
  );
}
