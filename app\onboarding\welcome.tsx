import React from "react";
import { useRouter } from "expo-router";
import { OnboardingWelcomeScreen } from "~/components/onboarding/OnboardingComponents";
import { useOnboarding } from "~/hooks/useOnboarding";

export default function WelcomeScreen() {
  const router = useRouter();
  const { completeStep, skipOnboarding } = useOnboarding();

  const handleGetStarted = async () => {
    await completeStep("welcome");
    router.push("/onboarding/profile");
  };

  const handleSkip = async () => {
    await skipOnboarding();
  };

  return (
    <OnboardingWelcomeScreen
      onGetStarted={handleGetStarted}
      onSkip={handleSkip}
    />
  );
}
