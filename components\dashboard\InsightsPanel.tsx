import React, { useState } from "react";
import { View, ScrollView, Pressable, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";

interface InsightsPanelProps {
  insights: string[];
  recommendations: string[];
  alerts: string[];
  onActionTaken?: (action: string) => void;
}

export function InsightsPanel({ 
  insights, 
  recommendations, 
  alerts, 
  onActionTaken 
}: InsightsPanelProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['alerts']));

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const handleActionClick = (action: string) => {
    onActionTaken?.(action);
  };

  return (
    <View className="gap-4">
      {/* Alertes - Toujours visibles et prioritaires */}
      {alerts.length > 0 && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
          <Pressable
            onPress={() => toggleSection('alerts')}
            {...(Platform.OS === "web" ? { style: { cursor: "pointer" } } : {})}
          >
            <CardHeader className="pb-2">
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Text className="text-lg mr-2">🚨</Text>
                  <CardTitle className="text-base text-red-700 dark:text-red-300">
                    Alertes ({alerts.length})
                  </CardTitle>
                </View>
                <Text className="text-red-600 dark:text-red-400">
                  {expandedSections.has('alerts') ? '▼' : '▶'}
                </Text>
              </View>
            </CardHeader>
          </Pressable>
          
          {expandedSections.has('alerts') && (
            <CardContent className="pt-0">
              <View className="gap-3">
                {alerts.map((alert, index) => (
                  <View key={index} className="p-3 bg-white dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
                    <Text className="text-sm text-red-700 dark:text-red-300 mb-2">
                      {alert}
                    </Text>
                    <View className="flex-row gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onPress={() => handleActionClick('view_upcoming_events')}
                        className="border-red-300 text-red-700 dark:border-red-700 dark:text-red-300"
                      >
                        <Text className="text-xs">Voir événements</Text>
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onPress={() => handleActionClick('dismiss_alert')}
                        className="border-red-300 text-red-700 dark:border-red-700 dark:text-red-300"
                      >
                        <Text className="text-xs">Ignorer</Text>
                      </Button>
                    </View>
                  </View>
                ))}
              </View>
            </CardContent>
          )}
        </Card>
      )}

      {/* Insights - Informations positives */}
      {insights.length > 0 && (
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <Pressable
            onPress={() => toggleSection('insights')}
            {...(Platform.OS === "web" ? { style: { cursor: "pointer" } } : {})}
          >
            <CardHeader className="pb-2">
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Text className="text-lg mr-2">💡</Text>
                  <CardTitle className="text-base text-blue-700 dark:text-blue-300">
                    Insights ({insights.length})
                  </CardTitle>
                </View>
                <Text className="text-blue-600 dark:text-blue-400">
                  {expandedSections.has('insights') ? '▼' : '▶'}
                </Text>
              </View>
            </CardHeader>
          </Pressable>
          
          {expandedSections.has('insights') && (
            <CardContent className="pt-0">
              <View className="gap-3">
                {insights.map((insight, index) => (
                  <View key={index} className="p-3 bg-white dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                    <Text className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                      {insight}
                    </Text>
                    <View className="flex-row gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onPress={() => handleActionClick('share_achievement')}
                        className="border-blue-300 text-blue-700 dark:border-blue-700 dark:text-blue-300"
                      >
                        <Text className="text-xs">Partager</Text>
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onPress={() => handleActionClick('view_details')}
                        className="border-blue-300 text-blue-700 dark:border-blue-700 dark:text-blue-300"
                      >
                        <Text className="text-xs">Détails</Text>
                      </Button>
                    </View>
                  </View>
                ))}
              </View>
            </CardContent>
          )}
        </Card>
      )}

      {/* Recommandations - Suggestions d'amélioration */}
      {recommendations.length > 0 && (
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <Pressable
            onPress={() => toggleSection('recommendations')}
            {...(Platform.OS === "web" ? { style: { cursor: "pointer" } } : {})}
          >
            <CardHeader className="pb-2">
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Text className="text-lg mr-2">🎯</Text>
                  <CardTitle className="text-base text-green-700 dark:text-green-300">
                    Recommandations ({recommendations.length})
                  </CardTitle>
                </View>
                <Text className="text-green-600 dark:text-green-400">
                  {expandedSections.has('recommendations') ? '▼' : '▶'}
                </Text>
              </View>
            </CardHeader>
          </Pressable>
          
          {expandedSections.has('recommendations') && (
            <CardContent className="pt-0">
              <View className="gap-3">
                {recommendations.map((recommendation, index) => (
                  <View key={index} className="p-3 bg-white dark:bg-green-900/20 rounded border border-green-200 dark:border-green-800">
                    <Text className="text-sm text-green-700 dark:text-green-300 mb-2">
                      {recommendation}
                    </Text>
                    <View className="flex-row gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onPress={() => handleActionClick('apply_recommendation')}
                        className="border-green-300 text-green-700 dark:border-green-700 dark:text-green-300"
                      >
                        <Text className="text-xs">Appliquer</Text>
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onPress={() => handleActionClick('learn_more')}
                        className="border-green-300 text-green-700 dark:border-green-700 dark:text-green-300"
                      >
                        <Text className="text-xs">En savoir +</Text>
                      </Button>
                    </View>
                  </View>
                ))}
              </View>
            </CardContent>
          )}
        </Card>
      )}

      {/* Message si aucun insight */}
      {insights.length === 0 && recommendations.length === 0 && alerts.length === 0 && (
        <Card>
          <CardContent className="items-center p-6">
            <Text className="text-4xl mb-3">📊</Text>
            <Text className="text-lg font-semibold mb-2">
              Pas encore d'insights
            </Text>
            <Text className="text-muted-foreground text-center">
              Organisez quelques événements pour voir apparaître des analyses et recommandations personnalisées.
            </Text>
            <Button
              onPress={() => handleActionClick('create_event')}
              className="mt-4"
            >
              <Text className="text-primary-foreground">
                🎉 Créer un événement
              </Text>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Actions rapides */}
      {(insights.length > 0 || recommendations.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">🚀 Actions rapides</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="flex-row flex-wrap gap-2">
              <Button
                size="sm"
                variant="outline"
                onPress={() => handleActionClick('create_event')}
              >
                <Text className="text-xs">➕ Nouvel événement</Text>
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onPress={() => handleActionClick('view_templates')}
              >
                <Text className="text-xs">📋 Templates</Text>
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onPress={() => handleActionClick('export_data')}
              >
                <Text className="text-xs">📊 Exporter</Text>
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                onPress={() => handleActionClick('invite_participants')}
              >
                <Text className="text-xs">👥 Inviter</Text>
              </Button>
            </View>
          </CardContent>
        </Card>
      )}

      {/* Conseils pour améliorer les insights */}
      <Card className="border-dashed border-2 border-muted">
        <CardContent className="p-4">
          <View className="flex-row items-start">
            <Text className="text-2xl mr-3">💡</Text>
            <View className="flex-1">
              <Text className="font-semibold mb-1">
                Améliorez vos insights
              </Text>
              <Text className="text-sm text-muted-foreground mb-2">
                Plus vous organisez d'événements, plus les analyses deviennent précises et utiles.
              </Text>
              <View className="flex-row flex-wrap gap-1">
                <Badge variant="outline">
                  <Text className="text-xs">Ajoutez des coûts réels</Text>
                </Badge>
                <Badge variant="outline">
                  <Text className="text-xs">Marquez les présences</Text>
                </Badge>
                <Badge variant="outline">
                  <Text className="text-xs">Complétez les items</Text>
                </Badge>
              </View>
            </View>
          </View>
        </CardContent>
      </Card>
    </View>
  );
}
