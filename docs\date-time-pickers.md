# 📅 Date & Time Pickers

Ce document décrit les composants de sélection de date et d'heure disponibles dans l'application Party Organizer.

## 🎯 Composants Disponibles

### 1. DatePicker
Composant pour sélectionner une date uniquement.

```tsx
import { DatePicker } from "~/components/ui/date-picker";

<DatePicker
  value={date}
  onChange={setDate}
  minimumDate={new Date()}
  error={hasError}
  placeholder="Sélectionner une date"
/>
```

**Props:**
- `value: Date` - Date actuelle
- `onChange: (date: Date) => void` - Callback lors du changement
- `minimumDate?: Date` - Date minimum sélectionnable
- `maximumDate?: Date` - Date maximum sélectionnable
- `error?: boolean` - Affichage d'erreur
- `placeholder?: string` - Texte de placeholder

### 2. TimePicker
Composant pour sélectionner une heure uniquement.

```tsx
import { TimePicker } from "~/components/ui/time-picker";

<TimePicker
  value={time}
  onChange={setTime}
  error={hasError}
  placeholder="Sélectionner une heure"
/>
```

**Props:**
- `value: Date` - Date/heure actuelle
- `onChange: (date: Date) => void` - Callback lors du changement
- `error?: boolean` - Affichage d'erreur
- `placeholder?: string` - Texte de placeholder

### 3. DateTimePicker (Recommandé)
Composant combiné pour sélectionner date et heure.

```tsx
import { DateTimePicker } from "~/components/ui/datetime-picker";

<DateTimePicker
  value={dateTime}
  onChange={setDateTime}
  minimumDate={new Date()}
  error={!!errors.dateTime}
  errorMessage={errors.dateTime}
  required={true}
  label="Date et Heure"
/>
```

**Props:**
- `value: Date` - Date/heure actuelle
- `onChange: (date: Date) => void` - Callback lors du changement
- `minimumDate?: Date` - Date minimum sélectionnable
- `maximumDate?: Date` - Date maximum sélectionnable
- `error?: boolean` - Affichage d'erreur
- `errorMessage?: string` - Message d'erreur à afficher
- `required?: boolean` - Champ obligatoire (affiche *)
- `label?: string` - Label du composant

## 🌐 Compatibilité Cross-Platform

### Web
- Utilise les inputs HTML5 natifs `type="date"` et `type="time"`
- Interface native du navigateur
- Validation automatique

### Mobile (iOS/Android)
- Utilise `@react-native-community/datetimepicker`
- Interface native de la plateforme
- Affichage modal ou inline selon la plateforme

## 🎨 Styling

Les composants utilisent les classes Tailwind/NativeWind pour le styling :
- `border-border` - Bordure normale
- `border-destructive` - Bordure d'erreur
- `text-foreground` - Texte principal
- `text-muted-foreground` - Texte secondaire
- `bg-background` - Arrière-plan

## 📝 Exemple d'Usage Complet

```tsx
import React, { useState } from "react";
import { DateTimePicker } from "~/components/ui/datetime-picker";

export function EventForm() {
  const [dateTime, setDateTime] = useState(new Date());
  const [errors, setErrors] = useState<{dateTime?: string}>({});

  const validateDateTime = () => {
    if (dateTime <= new Date()) {
      setErrors({ dateTime: "La date doit être dans le futur" });
      return false;
    }
    setErrors({});
    return true;
  };

  return (
    <DateTimePicker
      value={dateTime}
      onChange={setDateTime}
      minimumDate={new Date()}
      error={!!errors.dateTime}
      errorMessage={errors.dateTime}
      required={true}
      label="Date et Heure de l'événement"
    />
  );
}
```

## 🔧 Installation

Les dépendances nécessaires sont déjà installées :
- `@react-native-community/datetimepicker` pour mobile
- Support HTML5 natif pour web

## ✨ Fonctionnalités

- ✅ Cross-platform (Web, iOS, Android)
- ✅ Validation automatique
- ✅ Gestion des erreurs
- ✅ Interface native sur chaque plateforme
- ✅ Accessibilité
- ✅ Styling cohérent avec le design system
