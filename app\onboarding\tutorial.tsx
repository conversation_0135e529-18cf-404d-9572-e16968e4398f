import React, { useState } from "react";
import { View, ScrollView, Platform } from "react-native";
import { useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { useOnboarding } from "~/hooks/useOnboarding";
import { 
  OnboardingProgressBar, 
  OnboardingStepCard 
} from "~/components/onboarding/OnboardingComponents";

export default function OnboardingTutorialScreen() {
  const router = useRouter();
  const { steps, progress, completeStep, goToStep } = useOnboarding();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  // Filtrer les étapes du tutoriel (exclure welcome, profile, completion)
  const tutorialSteps = steps.filter(step => 
    !['welcome', 'profile_setup', 'completion'].includes(step.id)
  );

  const currentStep = tutorialSteps[currentStepIndex];

  const handleStepAction = async (stepId: string) => {
    // Marquer l'étape comme vue (pas forcément complétée)
    await completeStep(stepId);
    
    // Naviguer vers l'action correspondante
    switch (stepId) {
      case 'first_event':
        router.push('/create-event-with-template');
        break;
      case 'invite_participants':
        // Simuler l'invitation
        setTimeout(() => {
          setCurrentStepIndex(prev => Math.min(prev + 1, tutorialSteps.length - 1));
        }, 1000);
        break;
      case 'manage_items':
        // Simuler la gestion d'items
        setTimeout(() => {
          setCurrentStepIndex(prev => Math.min(prev + 1, tutorialSteps.length - 1));
        }, 1000);
        break;
      case 'financial_tracking':
        router.push('/(tabs)/dashboard');
        break;
      case 'notifications':
        // Simuler l'activation des notifications
        setTimeout(() => {
          setCurrentStepIndex(prev => Math.min(prev + 1, tutorialSteps.length - 1));
        }, 1000);
        break;
      case 'dashboard':
        router.push('/(tabs)/dashboard');
        break;
      default:
        setCurrentStepIndex(prev => Math.min(prev + 1, tutorialSteps.length - 1));
    }
  };

  const handleNext = () => {
    if (currentStepIndex < tutorialSteps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    } else {
      // Tutoriel terminé
      router.push('/onboarding/completion');
    }
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const handleSkipTutorial = () => {
    router.push('/onboarding/completion');
  };

  if (!currentStep) {
    return (
      <View className="flex-1 justify-center items-center bg-background p-6">
        <Text className="text-lg font-semibold mb-2">
          Chargement du tutoriel...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-background">
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto p-6" : "p-6"}>
        
        {/* Progress */}
        <OnboardingProgressBar progress={progress} />

        {/* Header */}
        <View className="mb-6">
          <Text className="text-2xl font-bold mb-2">
            🎯 Tour guidé interactif
          </Text>
          <Text className="text-muted-foreground">
            Découvrez les fonctionnalités principales en quelques étapes
          </Text>
        </View>

        {/* Indicateur d'étape */}
        <Card className="mb-6 bg-primary/5 border-primary/20">
          <CardContent className="p-4">
            <View className="flex-row items-center justify-between">
              <Text className="font-semibold">
                Étape {currentStepIndex + 1} sur {tutorialSteps.length}
              </Text>
              <Text className="text-sm text-muted-foreground">
                {Math.round(((currentStepIndex + 1) / tutorialSteps.length) * 100)}% terminé
              </Text>
            </View>
            <View className="w-full h-2 bg-muted rounded-full mt-2">
              <View 
                className="h-full bg-primary rounded-full transition-all duration-300"
                style={{ width: `${((currentStepIndex + 1) / tutorialSteps.length) * 100}%` }}
              />
            </View>
          </CardContent>
        </Card>

        {/* Étape actuelle */}
        <View className="mb-6">
          <OnboardingStepCard
            step={currentStep}
            isActive={true}
            onComplete={() => handleStepAction(currentStep.id)}
          />
        </View>

        {/* Aperçu des prochaines étapes */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>🔮 Prochaines étapes</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-2">
              {tutorialSteps.slice(currentStepIndex + 1, currentStepIndex + 4).map((step, index) => (
                <View key={step.id} className="flex-row items-center gap-3 p-2 bg-muted/30 rounded">
                  <Text className="text-lg">{step.icon}</Text>
                  <View className="flex-1">
                    <Text className="font-medium text-sm">{step.title}</Text>
                    <Text className="text-xs text-muted-foreground">
                      {step.description}
                    </Text>
                  </View>
                  <Text className="text-xs text-muted-foreground">
                    {currentStepIndex + index + 2}
                  </Text>
                </View>
              ))}
              
              {tutorialSteps.length > currentStepIndex + 4 && (
                <Text className="text-xs text-muted-foreground text-center mt-2">
                  ... et {tutorialSteps.length - currentStepIndex - 4} autres étapes
                </Text>
              )}
            </View>
          </CardContent>
        </Card>

        {/* Navigation */}
        <View className="gap-3">
          <View className="flex-row gap-3">
            <Button
              variant="outline"
              onPress={handlePrevious}
              disabled={currentStepIndex === 0}
              className="flex-1"
            >
              <Text>← Précédent</Text>
            </Button>
            
            <Button
              onPress={handleNext}
              className="flex-1"
            >
              <Text className="text-primary-foreground">
                {currentStepIndex === tutorialSteps.length - 1 ? 'Terminer' : 'Suivant →'}
              </Text>
            </Button>
          </View>

          <Button
            variant="ghost"
            onPress={handleSkipTutorial}
          >
            <Text className="text-muted-foreground">
              Ignorer le tutoriel
            </Text>
          </Button>
        </View>

        {/* Conseils */}
        <Card className="mt-6 border-dashed border-2 border-muted">
          <CardContent className="p-4">
            <View className="flex-row items-start">
              <Text className="text-2xl mr-3">💡</Text>
              <View className="flex-1">
                <Text className="font-semibold mb-1">
                  Conseil pour cette étape
                </Text>
                <Text className="text-sm text-muted-foreground">
                  {currentStep.id === 'first_event' && 
                    "Utilisez un template pour créer votre premier événement rapidement. Vous pourrez toujours le modifier après."}
                  {currentStep.id === 'invite_participants' && 
                    "Ajoutez d'abord les noms des participants, puis partagez le lien de l'événement."}
                  {currentStep.id === 'manage_items' && 
                    "Organisez vos items par catégories et assignez-les aux participants pour une meilleure coordination."}
                  {currentStep.id === 'financial_tracking' && 
                    "La gestion financière se fait automatiquement. Ajoutez simplement les coûts réels des items."}
                  {currentStep.id === 'notifications' && 
                    "Les notifications vous tiennent informé des mises à jour importantes de vos événements."}
                  {currentStep.id === 'dashboard' && 
                    "Votre tableau de bord vous donne une vue d'ensemble de toutes vos performances d'organisateur."}
                </Text>
              </View>
            </View>
          </CardContent>
        </Card>
      </View>
    </ScrollView>
  );
}
