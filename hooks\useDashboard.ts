/**
 * Hook personnalisé pour gérer le tableau de bord organisateur
 * Fournit des données en temps réel et des actions
 */

import { useState, useEffect, useCallback } from 'react';
import { analytics, OrganizerDashboard } from '~/lib/analyticsService';
import { exportData } from '~/lib/exportService';
import { useAuth } from '~/lib/AuthContext';
import { showToast } from '~/lib/toastService';

export interface DashboardState {
  dashboard: OrganizerDashboard | null;
  loading: boolean;
  refreshing: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface DashboardActions {
  loadDashboard: () => Promise<void>;
  refreshDashboard: () => Promise<void>;
  exportDashboard: (format?: 'text' | 'csv' | 'json') => Promise<void>;
  invalidateCache: () => Promise<void>;
}

export function useDashboard(): DashboardState & DashboardActions {
  const { session } = useAuth();
  const [state, setState] = useState<DashboardState>({
    dashboard: null,
    loading: true,
    refreshing: false,
    error: null,
    lastUpdated: null,
  });

  /**
   * Charge le tableau de bord
   */
  const loadDashboard = useCallback(async () => {
    if (!session?.user?.id) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Utilisateur non connecté',
      }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const dashboardData = await analytics.generateDashboard(session.user.id);
      
      setState(prev => ({
        ...prev,
        dashboard: dashboardData,
        loading: false,
        lastUpdated: new Date(),
      }));
    } catch (error) {
      console.error('Error loading dashboard:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Erreur lors du chargement du tableau de bord',
      }));
    }
  }, [session?.user?.id]);

  /**
   * Actualise le tableau de bord
   */
  const refreshDashboard = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      setState(prev => ({ ...prev, refreshing: true, error: null }));
      
      // Invalider le cache et recharger
      await analytics.invalidateDashboard(session.user.id);
      const dashboardData = await analytics.generateDashboard(session.user.id);
      
      setState(prev => ({
        ...prev,
        dashboard: dashboardData,
        refreshing: false,
        lastUpdated: new Date(),
      }));

      showToast('Tableau de bord actualisé', { type: 'success' });
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
      setState(prev => ({
        ...prev,
        refreshing: false,
        error: 'Erreur lors de l\'actualisation',
      }));
      showToast('Erreur lors de l\'actualisation', { type: 'error' });
    }
  }, [session?.user?.id]);

  /**
   * Exporte le tableau de bord
   */
  const exportDashboard = useCallback(async (format: 'text' | 'csv' | 'json' = 'text') => {
    if (!state.dashboard) {
      showToast('Aucune donnée à exporter', { type: 'warning' });
      return;
    }

    try {
      showToast('Génération du rapport...', { type: 'info' });
      
      const exportResult = await exportData.dashboard(state.dashboard, {
        format,
        includeFinancials: true,
        includeParticipants: true,
        includeItems: true,
      });

      await exportData.share(exportResult);
      showToast('Rapport exporté avec succès !', { type: 'success' });
    } catch (error) {
      console.error('Error exporting dashboard:', error);
      showToast('Erreur lors de l\'export', { type: 'error' });
    }
  }, [state.dashboard]);

  /**
   * Invalide le cache
   */
  const invalidateCache = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      await analytics.invalidateDashboard(session.user.id);
      showToast('Cache invalidé', { type: 'info' });
    } catch (error) {
      console.error('Error invalidating cache:', error);
      showToast('Erreur lors de l\'invalidation du cache', { type: 'error' });
    }
  }, [session?.user?.id]);

  // Charger le dashboard au montage et quand l'utilisateur change
  useEffect(() => {
    loadDashboard();
  }, [loadDashboard]);

  // Actualisation automatique périodique (toutes les 5 minutes)
  useEffect(() => {
    if (!session?.user?.id || !state.dashboard) return;

    const interval = setInterval(() => {
      // Actualisation silencieuse (sans toast)
      refreshDashboard();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [session?.user?.id, state.dashboard, refreshDashboard]);

  return {
    ...state,
    loadDashboard,
    refreshDashboard,
    exportDashboard,
    invalidateCache,
  };
}

/**
 * Hook simplifié pour obtenir juste les statistiques principales
 */
export function useDashboardStats() {
  const { dashboard, loading, error } = useDashboard();

  const stats = dashboard ? {
    totalEvents: dashboard.eventStats.totalEvents,
    upcomingEvents: dashboard.eventStats.upcomingEvents,
    totalParticipants: dashboard.participantStats.totalUniqueParticipants,
    totalSpent: dashboard.financialStats.totalSpent,
    completionRate: dashboard.itemStats.completionRate,
    hasAlerts: dashboard.alerts.length > 0,
    hasRecommendations: dashboard.recommendations.length > 0,
  } : null;

  return {
    stats,
    loading,
    error,
    hasData: !!dashboard,
  };
}

/**
 * Hook pour obtenir les insights et alertes
 */
export function useDashboardInsights() {
  const { dashboard, loading, error } = useDashboard();

  const insights = dashboard ? {
    insights: dashboard.insights,
    recommendations: dashboard.recommendations,
    alerts: dashboard.alerts,
    hasInsights: dashboard.insights.length > 0,
    hasRecommendations: dashboard.recommendations.length > 0,
    hasAlerts: dashboard.alerts.length > 0,
    totalInsights: dashboard.insights.length + dashboard.recommendations.length + dashboard.alerts.length,
  } : null;

  return {
    insights,
    loading,
    error,
    hasData: !!dashboard,
  };
}

/**
 * Hook pour les tendances temporelles
 */
export function useDashboardTrends() {
  const { dashboard, loading, error } = useDashboard();

  const trends = dashboard ? {
    timeStats: dashboard.timeStats,
    mostPopularHour: dashboard.timeStats.mostPopularTimeSlots[0]?.hour,
    mostPopularDay: dashboard.timeStats.mostPopularDays[0]?.dayOfWeek,
    averageDuration: dashboard.timeStats.averageEventDuration,
  } : null;

  return {
    trends,
    loading,
    error,
    hasData: !!dashboard,
  };
}

/**
 * Hook pour les actions rapides du dashboard
 */
export function useDashboardActions() {
  const { exportDashboard, refreshDashboard, invalidateCache } = useDashboard();

  const actions = {
    export: {
      text: () => exportDashboard('text'),
      csv: () => exportDashboard('csv'),
      json: () => exportDashboard('json'),
    },
    refresh: refreshDashboard,
    clearCache: invalidateCache,
  };

  return actions;
}
