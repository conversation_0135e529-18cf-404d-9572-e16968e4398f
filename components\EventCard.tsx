import { Card, CardTitle } from "~/components/ui/card";
import { Pressable, Text, View, Share } from "react-native";
import * as AspectRatio from "@rn-primitives/aspect-ratio";
import { EventWithParticipantStatus } from "~/lib/types";
import { Link, useRouter } from "expo-router";
import { cn } from "~/lib/utils";
import { Badge } from "./ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import {
  generateShareUrl,
  generateShareMessage,
} from "~/components/QRCodeGenerator";

interface EventCardProps {
  event: EventWithParticipantStatus;
  isOrganizer: boolean;
  isPast: boolean;
}

export function EventCard({ event, isOrganizer, isPast }: EventCardProps) {
  const router = useRouter();
  const { id, title, date_time, icon, location, participant_status } = event;

  const eventDate = new Date(date_time);
  const formattedDate = eventDate.toLocaleDateString("fr-FR", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
  const formattedTime = eventDate.toLocaleTimeString("fr-FR", {
    hour: "2-digit",
    minute: "2-digit",
  });

  let statusBadge = null;
  if (!isOrganizer && participant_status) {
    switch (participant_status) {
      case "pending":
        statusBadge = (
          <Badge variant="outline" className="bg-muted border-border">
            <Text className="text-muted-foreground">En attente</Text>
          </Badge>
        );
        break;
      case "maybe":
        statusBadge = (
          <Badge variant="outline" className="bg-muted border-border">
            <Text className="text-muted-foreground">Peut-être</Text>
          </Badge>
        );
        break;
    }
  }

  const createSlug = (text: string) => {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/--+/g, "-")
      .trim();
  };

  const handleShare = async () => {
    try {
      const shareUrl = generateShareUrl(id);
      const shareMessage = generateShareMessage(title, shareUrl);
      await Share.share({
        message: shareMessage,
        url: shareUrl, // url est principalement pour iOS
        title: `Invitation: ${title}`,
      });
    } catch (error) {
      console.error("Erreur lors du partage", error);
    }
  };

  return (
    <Link
      href={{
        pathname: "/event/[id]",
        params: { id: id.toString(), title: createSlug(title) },
      }}
      asChild
      accessibilityLabel={`Event: ${title}`}
      accessibilityRole="link"
    >
      <Pressable
        className="mb-2 group"
        accessibilityRole="button"
        accessibilityLabel={`View details for ${title}`}
      >
        <Card
          className={cn(
            "w-full flex-row group-active:bg-muted overflow-hidden border",
            isPast ? "opacity-60 border-border/50" : "border-border",
            isOrganizer ? "border-l-4 border-l-primary" : ""
          )}
          accessibilityLabel={title}
        >
          <AspectRatio.Root
            ratio={1}
            className="flex items-center justify-center w-20 h-auto bg-muted/50"
            accessibilityLabel={icon || "Party icon"}
          >
            <Text className="text-4xl leading-tight">{icon || "🎉"}</Text>
          </AspectRatio.Root>
          <View className="flex-1 justify-center p-3 relative">
            <View className="flex-row items-start justify-between">
              <CardTitle
                className="text-lg font-semibold text-foreground mb-1 flex-1 mr-2"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {title}
              </CardTitle>
              <View className="flex-row items-center gap-2">
                {isOrganizer && (
                  <Badge variant="organizer">
                    <Text>Orga</Text>
                  </Badge>
                )}
                {statusBadge}
              </View>
            </View>
            <Text
              className="text-sm text-muted-foreground"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {formattedDate} à {formattedTime}
            </Text>
            {location && (
              <Text
                className="text-sm text-muted-foreground mt-1"
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {location}
              </Text>
            )}
            {/* Popover de partage en bas à droite */}
            <View className="absolute bottom-2 right-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Pressable
                    className="p-2 h-8 w-8 items-center justify-center rounded-full hover:bg-muted"
                    accessibilityLabel="More options"
                  >
                    <Text className="text-lg text-muted-foreground">⋯</Text>
                  </Pressable>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2">
                  <Pressable
                    onPress={handleShare}
                    className="p-2 rounded hover:bg-muted"
                    accessibilityLabel="Share event"
                  >
                    <Text>Partager</Text>
                  </Pressable>
                  <Pressable
                    onPress={() => router.push(`/event/${id}/share`)}
                    className="p-2 rounded hover:bg-muted"
                    accessibilityLabel="View QR Code"
                  >
                    <Text>QR Code</Text>
                  </Pressable>
                </PopoverContent>
              </Popover>
            </View>
          </View>
        </Card>
      </Pressable>
    </Link>
  );
}
