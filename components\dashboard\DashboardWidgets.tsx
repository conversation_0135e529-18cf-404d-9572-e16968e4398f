import React from "react";
import { View, ScrollView } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { 
  EventStats, 
  FinancialStats, 
  ParticipantStats, 
  ItemStats, 
  TimeStats 
} from "~/lib/analyticsService";

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  color?: 'default' | 'success' | 'warning' | 'error';
}

export function StatCard({ 
  title, 
  value, 
  subtitle, 
  icon, 
  trend, 
  trendValue, 
  color = 'default' 
}: StatCardProps) {
  const getColorClasses = () => {
    switch (color) {
      case 'success': return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'warning': return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'error': return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      default: return 'border-border bg-card';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      case 'stable': return '➡️';
      default: return '';
    }
  };

  return (
    <Card className={`${getColorClasses()}`}>
      <CardContent className="p-4">
        <View className="flex-row items-start justify-between">
          <View className="flex-1">
            <Text className="text-sm text-muted-foreground mb-1">{title}</Text>
            <Text className="text-2xl font-bold mb-1">{value}</Text>
            {subtitle && (
              <Text className="text-xs text-muted-foreground">{subtitle}</Text>
            )}
            {trend && trendValue && (
              <View className="flex-row items-center mt-2">
                <Text className="text-xs mr-1">{getTrendIcon()}</Text>
                <Text className="text-xs text-muted-foreground">{trendValue}</Text>
              </View>
            )}
          </View>
          <Text className="text-2xl">{icon}</Text>
        </View>
      </CardContent>
    </Card>
  );
}

interface EventStatsWidgetProps {
  stats: EventStats;
}

export function EventStatsWidget({ stats }: EventStatsWidgetProps) {
  return (
    <View className="gap-3">
      <Text className="text-lg font-semibold">📊 Statistiques d'événements</Text>
      
      <View className="flex-row gap-3">
        <View className="flex-1">
          <StatCard
            title="Total événements"
            value={stats.totalEvents}
            icon="🎉"
            color="default"
          />
        </View>
        <View className="flex-1">
          <StatCard
            title="À venir"
            value={stats.upcomingEvents}
            icon="📅"
            color="success"
          />
        </View>
      </View>

      <View className="flex-row gap-3">
        <View className="flex-1">
          <StatCard
            title="Participants moy."
            value={stats.averageParticipants.toFixed(1)}
            icon="👥"
            color="default"
          />
        </View>
        <View className="flex-1">
          <StatCard
            title="Taux présence"
            value={`${stats.participationRate}%`}
            icon="✅"
            color={stats.participationRate > 80 ? 'success' : 'warning'}
          />
        </View>
      </View>
    </View>
  );
}

interface FinancialStatsWidgetProps {
  stats: FinancialStats;
}

export function FinancialStatsWidget({ stats }: FinancialStatsWidgetProps) {
  return (
    <View className="gap-3">
      <Text className="text-lg font-semibold">💰 Statistiques financières</Text>
      
      <View className="flex-row gap-3">
        <View className="flex-1">
          <StatCard
            title="Budget total"
            value={`${stats.totalBudget.toFixed(0)}€`}
            icon="💳"
            color="default"
          />
        </View>
        <View className="flex-1">
          <StatCard
            title="Dépensé"
            value={`${stats.totalSpent.toFixed(0)}€`}
            icon="💸"
            color="warning"
          />
        </View>
      </View>

      <View className="flex-row gap-3">
        <View className="flex-1">
          <StatCard
            title="Coût/événement"
            value={`${stats.averageCostPerEvent.toFixed(0)}€`}
            icon="📊"
            color="default"
          />
        </View>
        <View className="flex-1">
          <StatCard
            title="Économies"
            value={`${stats.savingsRate.toFixed(1)}%`}
            icon="🎯"
            color={stats.savingsRate > 10 ? 'success' : 'warning'}
          />
        </View>
      </View>

      {stats.mostExpensiveCategory && (
        <Card>
          <CardContent className="p-3">
            <Text className="text-sm text-muted-foreground mb-1">
              Catégorie la plus coûteuse
            </Text>
            <View className="flex-row items-center">
              <Badge variant="secondary" className="mr-2">
                <Text className="text-xs">{stats.mostExpensiveCategory}</Text>
              </Badge>
              <Text className="text-sm text-muted-foreground">
                Optimisez cette catégorie pour économiser
              </Text>
            </View>
          </CardContent>
        </Card>
      )}
    </View>
  );
}

interface ParticipantStatsWidgetProps {
  stats: ParticipantStats;
}

export function ParticipantStatsWidget({ stats }: ParticipantStatsWidgetProps) {
  return (
    <View className="gap-3">
      <Text className="text-lg font-semibold">👥 Statistiques participants</Text>
      
      <View className="flex-row gap-3">
        <View className="flex-1">
          <StatCard
            title="Participants uniques"
            value={stats.totalUniqueParticipants}
            icon="🙋"
            color="default"
          />
        </View>
        <View className="flex-1">
          <StatCard
            title="Fidélité"
            value={`${stats.participantRetentionRate}%`}
            icon="🤝"
            color={stats.participantRetentionRate > 70 ? 'success' : 'warning'}
          />
        </View>
      </View>

      {stats.mostActiveParticipants.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">🏆 Participants les plus actifs</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-2">
              {stats.mostActiveParticipants.slice(0, 3).map((participant, index) => (
                <View key={index} className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <Text className="text-lg mr-2">
                      {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}
                    </Text>
                    <Text className="font-medium">{participant.name}</Text>
                  </View>
                  <Text className="text-sm text-muted-foreground">
                    {participant.eventsCount} événements
                  </Text>
                </View>
              ))}
            </View>
          </CardContent>
        </Card>
      )}
    </View>
  );
}

interface ItemStatsWidgetProps {
  stats: ItemStats;
}

export function ItemStatsWidget({ stats }: ItemStatsWidgetProps) {
  return (
    <View className="gap-3">
      <Text className="text-lg font-semibold">📝 Statistiques items</Text>
      
      <View className="flex-row gap-3">
        <View className="flex-1">
          <StatCard
            title="Items total"
            value={stats.totalItems}
            icon="📋"
            color="default"
          />
        </View>
        <View className="flex-1">
          <StatCard
            title="Taux completion"
            value={`${stats.completionRate.toFixed(1)}%`}
            icon="✅"
            color={stats.completionRate > 80 ? 'success' : 'warning'}
          />
        </View>
      </View>

      {stats.mostPopularCategories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">📊 Catégories populaires</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-2">
              {stats.mostPopularCategories.slice(0, 3).map((category, index) => (
                <View key={index} className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <Badge variant="outline" className="mr-2">
                      <Text className="text-xs">{category.category}</Text>
                    </Badge>
                  </View>
                  <View className="flex-row items-center gap-2">
                    <Text className="text-sm text-muted-foreground">
                      {category.count} items
                    </Text>
                    {category.averageCost > 0 && (
                      <Text className="text-sm text-muted-foreground">
                        {category.averageCost.toFixed(0)}€ moy.
                      </Text>
                    )}
                  </View>
                </View>
              ))}
            </View>
          </CardContent>
        </Card>
      )}
    </View>
  );
}

interface TimeStatsWidgetProps {
  stats: TimeStats;
}

export function TimeStatsWidget({ stats }: TimeStatsWidgetProps) {
  const getDayName = (dayOfWeek: number) => {
    const days = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
    return days[dayOfWeek];
  };

  const formatHour = (hour: number) => {
    return `${hour.toString().padStart(2, '0')}:00`;
  };

  return (
    <View className="gap-3">
      <Text className="text-lg font-semibold">⏰ Tendances temporelles</Text>
      
      <StatCard
        title="Durée moyenne"
        value={`${stats.averageEventDuration}h`}
        icon="⌛"
        color="default"
      />

      {stats.mostPopularTimeSlots.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">🕐 Créneaux populaires</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-2">
              {stats.mostPopularTimeSlots.map((slot, index) => (
                <View key={index} className="flex-row items-center justify-between">
                  <Text className="font-medium">{formatHour(slot.hour)}</Text>
                  <View className="flex-row items-center">
                    <View className="w-20 h-2 bg-muted rounded mr-2">
                      <View 
                        className="h-full bg-primary rounded"
                        style={{ 
                          width: `${(slot.count / Math.max(...stats.mostPopularTimeSlots.map(s => s.count))) * 100}%` 
                        }}
                      />
                    </View>
                    <Text className="text-sm text-muted-foreground w-8">
                      {slot.count}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </CardContent>
        </Card>
      )}

      {stats.mostPopularDays.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">📅 Jours populaires</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <View className="gap-2">
              {stats.mostPopularDays.map((day, index) => (
                <View key={index} className="flex-row items-center justify-between">
                  <Text className="font-medium">{getDayName(day.dayOfWeek)}</Text>
                  <View className="flex-row items-center">
                    <View className="w-20 h-2 bg-muted rounded mr-2">
                      <View 
                        className="h-full bg-primary rounded"
                        style={{ 
                          width: `${(day.count / Math.max(...stats.mostPopularDays.map(d => d.count))) * 100}%` 
                        }}
                      />
                    </View>
                    <Text className="text-sm text-muted-foreground w-8">
                      {day.count}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </CardContent>
        </Card>
      )}
    </View>
  );
}
