import React, { useState, useEffect } from "react";
import { View, ScrollView, RefreshControl, Platform } from "react-native";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { useAuth } from "~/lib/AuthContext";
import { useAchievements } from "~/hooks/useAchievements";
import { streaks } from "~/lib/streaksService";
import { challenges } from "~/lib/challengesService";
import { collections } from "~/lib/collectionsService";
import {
  UserStatsCard,
  AchievementProgressList,
  Leaderboard,
  QuickStats,
  StreakCard,
  ChallengeCard,
  CollectionCard,
} from "~/components/achievements/AchievementComponents";

export default function AchievementsScreen() {
  const { session, isAnonymous } = useAuth();
  const {
    badges,
    userStats,
    userAchievements,
    progress,
    leaderboard,
    loading,
    error,
    nextLevelInfo,
    loadAchievements,
    checkNewAchievements,
    refreshLeaderboard,
  } = useAchievements();

  const [activeTab, setActiveTab] = useState<
    | "overview"
    | "badges"
    | "streaks"
    | "challenges"
    | "collections"
    | "leaderboard"
  >("overview");
  const [refreshing, setRefreshing] = useState(false);

  // États pour les nouvelles fonctionnalités
  const [userStreaks, setUserStreaks] = useState<any[]>([]);
  const [activeChallenges, setActiveChallenges] = useState<any[]>([]);
  const [userParticipations, setUserParticipations] = useState<any[]>([]);
  const [badgeCollections, setBadgeCollections] = useState<any[]>([]);
  const [collectionProgress, setCollectionProgress] = useState<any[]>([]);

  // Charger les nouvelles données
  const loadExtendedData = async () => {
    if (!session?.user?.id) return;

    try {
      const [
        streaksData,
        challengesData,
        participationsData,
        collectionsData,
        progressData,
      ] = await Promise.all([
        streaks.getUserStreaks(session.user.id),
        challenges.getActive(),
        challenges.getUserParticipations(session.user.id),
        collections.getAllWithBadges(),
        collections.getUserProgress(session.user.id),
      ]);

      setUserStreaks(streaksData);
      setActiveChallenges(challengesData);
      setUserParticipations(participationsData);
      setBadgeCollections(collectionsData);
      setCollectionProgress(progressData);
    } catch (error) {
      console.error("Error loading extended data:", error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadAchievements();
      await loadExtendedData();
      if (activeTab === "leaderboard") {
        await refreshLeaderboard();
      }
    } catch (error) {
      console.error("Error refreshing:", error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleCheckAchievements = async () => {
    try {
      const newBadges = await checkNewAchievements();
      if (newBadges.length === 0) {
        // showToast('Aucun nouveau badge à débloquer', { type: 'info' });
      }
    } catch (error) {
      console.error("Error checking achievements:", error);
    }
  };

  // Charger les données étendues au montage
  useEffect(() => {
    if (session?.user?.id) {
      loadExtendedData();
    }
  }, [session?.user?.id]);

  if (isAnonymous) {
    return (
      <View className="flex-1 bg-background">
        <View className="p-6 justify-center items-center flex-1">
          <Text className="text-6xl mb-4">🏆</Text>
          <Text className="text-2xl font-bold text-center mb-2">
            Débloquez des achievements !
          </Text>
          <Text className="text-muted-foreground text-center mb-6">
            Connectez-vous pour suivre votre progression et débloquer des badges
          </Text>
          <Button
            onPress={() => {
              /* Navigation vers login */
            }}
          >
            <Text className="text-primary-foreground">Se connecter</Text>
          </Button>
        </View>
      </View>
    );
  }

  if (loading && !userStats) {
    return (
      <View className="flex-1 bg-background justify-center items-center">
        <Text className="text-lg">Chargement des achievements...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 bg-background justify-center items-center p-6">
        <Text className="text-lg font-semibold mb-2">Erreur</Text>
        <Text className="text-muted-foreground text-center mb-4">{error}</Text>
        <Button onPress={loadAchievements}>
          <Text className="text-primary-foreground">Réessayer</Text>
        </Button>
      </View>
    );
  }

  const recentBadges = userAchievements
    .filter((a) => a.is_completed)
    .sort(
      (a, b) =>
        new Date(b.unlocked_at).getTime() - new Date(a.unlocked_at).getTime()
    )
    .slice(0, 5)
    .map((a) => a.badge!)
    .filter(Boolean);

  return (
    <View className="flex-1 bg-background">
      {/* Header avec onglets */}
      <View className="p-4 pb-0">
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-2xl font-bold">🏆 Achievements</Text>
          <Button variant="outline" size="sm" onPress={handleCheckAchievements}>
            <Text className="text-xs">Vérifier</Text>
          </Button>
        </View>

        {/* Onglets */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          className="mb-4"
        >
          <View className="flex-row bg-muted rounded-lg p-1 gap-1">
            <Button
              variant={activeTab === "overview" ? "default" : "ghost"}
              size="sm"
              onPress={() => setActiveTab("overview")}
            >
              <Text
                className={`text-xs ${
                  activeTab === "overview"
                    ? "text-primary-foreground"
                    : "text-foreground"
                }`}
              >
                Vue d'ensemble
              </Text>
            </Button>
            <Button
              variant={activeTab === "badges" ? "default" : "ghost"}
              size="sm"
              onPress={() => setActiveTab("badges")}
            >
              <Text
                className={`text-xs ${
                  activeTab === "badges"
                    ? "text-primary-foreground"
                    : "text-foreground"
                }`}
              >
                Badges
              </Text>
            </Button>
            <Button
              variant={activeTab === "streaks" ? "default" : "ghost"}
              size="sm"
              onPress={() => setActiveTab("streaks")}
            >
              <Text
                className={`text-xs ${
                  activeTab === "streaks"
                    ? "text-primary-foreground"
                    : "text-foreground"
                }`}
              >
                Streaks
              </Text>
            </Button>
            <Button
              variant={activeTab === "challenges" ? "default" : "ghost"}
              size="sm"
              onPress={() => setActiveTab("challenges")}
            >
              <Text
                className={`text-xs ${
                  activeTab === "challenges"
                    ? "text-primary-foreground"
                    : "text-foreground"
                }`}
              >
                Challenges
              </Text>
            </Button>
            <Button
              variant={activeTab === "collections" ? "default" : "ghost"}
              size="sm"
              onPress={() => setActiveTab("collections")}
            >
              <Text
                className={`text-xs ${
                  activeTab === "collections"
                    ? "text-primary-foreground"
                    : "text-foreground"
                }`}
              >
                Collections
              </Text>
            </Button>
            <Button
              variant={activeTab === "leaderboard" ? "default" : "ghost"}
              size="sm"
              onPress={() => setActiveTab("leaderboard")}
            >
              <Text
                className={`text-xs ${
                  activeTab === "leaderboard"
                    ? "text-primary-foreground"
                    : "text-foreground"
                }`}
              >
                Classement
              </Text>
            </Button>
          </View>
        </ScrollView>
      </View>

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <View className="p-4 pt-0">
          {activeTab === "overview" && (
            <View className="gap-4">
              {/* Stats rapides */}
              {userStats && (
                <QuickStats stats={userStats} recentBadges={recentBadges} />
              )}

              {/* Statistiques détaillées */}
              {userStats && (
                <UserStatsCard
                  stats={userStats}
                  nextLevelInfo={nextLevelInfo}
                />
              )}

              {/* Badges récents */}
              {recentBadges.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>🎉 Derniers badges débloqués</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <View className="flex-row flex-wrap gap-2">
                      {recentBadges.map((badge) => (
                        <View
                          key={badge.id}
                          className="items-center p-2 bg-muted/50 rounded-lg"
                        >
                          <Text className="text-2xl mb-1">{badge.icon}</Text>
                          <Text className="text-xs text-center font-medium">
                            {badge.title}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </CardContent>
                </Card>
              )}

              {/* Progression proche */}
              {progress.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>🎯 Prochains objectifs</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <View className="gap-3">
                      {progress
                        .filter((p) => !p.is_completed && p.percentage > 0)
                        .slice(0, 3)
                        .map((item) => (
                          <View
                            key={item.badge.id}
                            className="flex-row items-center gap-3"
                          >
                            <Text className="text-2xl">{item.badge.icon}</Text>
                            <View className="flex-1">
                              <Text className="font-medium text-sm">
                                {item.badge.title}
                              </Text>
                              <View className="flex-row items-center gap-2 mt-1">
                                <View className="flex-1 h-2 bg-muted rounded-full">
                                  <View
                                    className="h-full bg-primary rounded-full"
                                    style={{ width: `${item.percentage}%` }}
                                  />
                                </View>
                                <Text className="text-xs text-muted-foreground">
                                  {item.current}/{item.required}
                                </Text>
                              </View>
                            </View>
                          </View>
                        ))}
                    </View>
                  </CardContent>
                </Card>
              )}
            </View>
          )}

          {activeTab === "badges" && (
            <AchievementProgressList
              progress={progress}
              onBadgePress={(badge) => {
                // Afficher les détails du badge
                console.log("Badge pressed:", badge);
              }}
            />
          )}

          {activeTab === "streaks" && (
            <View className="gap-4">
              <StreakCard
                streaks={userStreaks}
                onStreakPress={(streak) => {
                  console.log("Streak pressed:", streak);
                }}
              />

              {userStreaks.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>🏆 Milestones de Streaks</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <Text className="text-sm text-muted-foreground">
                      Continuez vos streaks pour débloquer des récompenses
                      spéciales !
                    </Text>
                    <View className="mt-3 gap-2">
                      <Text className="text-xs">
                        🔥 3 jours → Badge "Régulier" (50 pts)
                      </Text>
                      <Text className="text-xs">
                        ⚡ 7 jours → Badge "Assidu" (150 pts)
                      </Text>
                      <Text className="text-xs">
                        💎 14 jours → Badge "Dévoué" (300 pts)
                      </Text>
                      <Text className="text-xs">
                        👑 30 jours → Thèmes premium débloqués
                      </Text>
                    </View>
                  </CardContent>
                </Card>
              )}
            </View>
          )}

          {activeTab === "challenges" && (
            <View className="gap-4">
              {activeChallenges.length === 0 ? (
                <Card>
                  <CardContent className="p-6 items-center">
                    <Text className="text-6xl mb-4">🏆</Text>
                    <Text className="text-lg font-semibold mb-2">
                      Aucun challenge actif
                    </Text>
                    <Text className="text-muted-foreground text-center">
                      Les challenges apparaîtront ici. Revenez bientôt !
                    </Text>
                  </CardContent>
                </Card>
              ) : (
                activeChallenges.map((challenge) => {
                  const participation = userParticipations.find(
                    (p) => p.challenge_id === challenge.id
                  );
                  return (
                    <ChallengeCard
                      key={challenge.id}
                      challenge={challenge}
                      participation={participation}
                      onJoin={() =>
                        challenges.join(session?.user?.id!, challenge.id)
                      }
                      onViewLeaderboard={() => {
                        console.log(
                          "View leaderboard for challenge:",
                          challenge.id
                        );
                      }}
                    />
                  );
                })
              )}
            </View>
          )}

          {activeTab === "collections" && (
            <View className="gap-4">
              {badgeCollections.length === 0 ? (
                <Card>
                  <CardContent className="p-6 items-center">
                    <Text className="text-6xl mb-4">📚</Text>
                    <Text className="text-lg font-semibold mb-2">
                      Collections en cours de chargement
                    </Text>
                    <Text className="text-muted-foreground text-center">
                      Les collections de badges apparaîtront ici
                    </Text>
                  </CardContent>
                </Card>
              ) : (
                badgeCollections.map((collection) => {
                  const userBadgeIds = userAchievements
                    .filter((a) => a.is_completed)
                    .map((a) => a.badge_id);
                  const isCompleted = collectionProgress.find(
                    (p) => p.collection_id === collection.id && p.is_completed
                  );

                  return (
                    <CollectionCard
                      key={collection.id}
                      collection={collection}
                      userBadges={userBadgeIds}
                      isCompleted={!!isCompleted}
                      onPress={() => {
                        console.log("Collection pressed:", collection);
                      }}
                    />
                  );
                })
              )}
            </View>
          )}

          {activeTab === "leaderboard" && (
            <Leaderboard
              leaderboard={leaderboard}
              currentUserId={session?.user?.id}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
}
