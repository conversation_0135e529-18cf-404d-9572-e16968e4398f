/**
 * Service de gestion des templates d'événements pour Party Organizer
 * Gère les templates prédéfinis, personnalisés et la création d'événements depuis templates
 */

import { supabase } from './supabase';
import { 
  EventTemplate, 
  TemplateItem, 
  EventTemplateInsert, 
  TemplateItemInsert,
  EventTemplateData,
  EventFromTemplate 
} from './types';

/**
 * Récupère tous les templates disponibles pour un utilisateur
 */
export async function fetchAvailableTemplates(userId?: string) {
  try {
    const { data, error } = await supabase
      .from('event_templates')
      .select(`
        *,
        template_items(*)
      `)
      .or(`category.eq.predefined,is_public.eq.true${userId ? `,created_by.eq.${userId}` : ''}`)
      .order('category', { ascending: true })
      .order('usage_count', { ascending: false });

    if (error) {
      console.error('Error fetching templates:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching templates:', error);
    return [];
  }
}

/**
 * Récupère un template spécifique avec ses items
 */
export async function fetchTemplate(templateId: number) {
  try {
    const { data, error } = await supabase
      .from('event_templates')
      .select(`
        *,
        template_items(*)
      `)
      .eq('id', templateId)
      .single();

    if (error) {
      console.error('Error fetching template:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error fetching template:', error);
    return null;
  }
}

/**
 * Crée un nouveau template personnalisé
 */
export async function createCustomTemplate(
  template: EventTemplateInsert,
  items: TemplateItemInsert[]
) {
  try {
    // Créer le template
    const { data: templateData, error: templateError } = await supabase
      .from('event_templates')
      .insert(template)
      .select()
      .single();

    if (templateError) {
      console.error('Error creating template:', templateError);
      return null;
    }

    // Créer les items du template
    if (items.length > 0) {
      const itemsWithTemplateId = items.map((item, index) => ({
        ...item,
        template_id: templateData.id,
        order_index: index,
      }));

      const { error: itemsError } = await supabase
        .from('template_items')
        .insert(itemsWithTemplateId);

      if (itemsError) {
        console.error('Error creating template items:', itemsError);
        // Supprimer le template si les items n'ont pas pu être créés
        await supabase.from('event_templates').delete().eq('id', templateData.id);
        return null;
      }
    }

    return templateData;
  } catch (error) {
    console.error('Error creating custom template:', error);
    return null;
  }
}

/**
 * Sauvegarde un événement existant comme template
 */
export async function saveEventAsTemplate(
  eventId: number,
  templateName: string,
  templateDescription: string,
  userId: string
) {
  try {
    // Récupérer les données de l'événement
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select(`
        *,
        items(*)
      `)
      .eq('id', eventId)
      .single();

    if (eventError || !eventData) {
      console.error('Error fetching event for template:', eventError);
      return null;
    }

    // Créer les données du template
    const templateData: EventTemplateData = {
      title_template: eventData.title,
      description_template: eventData.description || undefined,
      default_time: eventData.time || '19:00',
      default_duration: 3,
      tags: ['custom', 'saved'],
    };

    // Créer le template
    const template: EventTemplateInsert = {
      name: templateName,
      description: templateDescription,
      icon: eventData.icon || '🎉',
      category: 'custom',
      created_by: userId,
      is_public: false,
      template_data: templateData,
    };

    // Créer les items du template
    const templateItems: TemplateItemInsert[] = eventData.items?.map((item, index) => ({
      template_id: 0, // Sera remplacé
      name: item.name,
      category: item.category,
      estimated_cost: item.estimated_cost,
      estimated_effort: item.estimated_effort,
      is_essential: !item.is_suggestion,
      suggested_quantity: 1,
      description: null,
      order_index: index,
    })) || [];

    return await createCustomTemplate(template, templateItems);
  } catch (error) {
    console.error('Error saving event as template:', error);
    return null;
  }
}

/**
 * Incrémente le compteur d'utilisation d'un template
 */
export async function incrementTemplateUsage(templateId: number) {
  try {
    const { error } = await supabase
      .from('event_templates')
      .update({ 
        usage_count: supabase.sql`usage_count + 1`,
        updated_at: new Date().toISOString()
      })
      .eq('id', templateId);

    if (error) {
      console.error('Error incrementing template usage:', error);
    }
  } catch (error) {
    console.error('Error incrementing template usage:', error);
  }
}

/**
 * Génère les données d'événement depuis un template
 */
export function generateEventFromTemplate(
  template: EventTemplate,
  customizations: EventFromTemplate['customizations']
): Partial<Event> {
  const templateData = template.template_data;
  
  // Calculer la date par défaut (aujourd'hui + temps de préparation)
  const defaultDate = new Date();
  if (templateData.preparation_time) {
    defaultDate.setDate(defaultDate.getDate() + templateData.preparation_time);
  }

  // Générer le titre depuis le template
  let title = customizations.title || templateData.title_template;
  if (title.includes('{theme}') && !customizations.title) {
    title = title.replace('{theme}', 'Thème');
  }

  // Générer la description
  let description = customizations.description || templateData.description_template || '';

  // Construire la date/heure
  const eventDate = customizations.date || defaultDate.toISOString().split('T')[0];
  const eventTime = customizations.time || templateData.default_time;
  const dateTime = `${eventDate}T${eventTime}:00`;

  return {
    title,
    description,
    icon: template.icon,
    date_time: dateTime,
    location: customizations.location || '',
    // Les autres champs seront ajoutés lors de la création
  };
}

/**
 * Génère les items depuis un template
 */
export function generateItemsFromTemplate(
  template: EventTemplate,
  selectedItemIds?: number[],
  customItems?: Omit<TemplateItem, 'id' | 'template_id' | 'created_at'>[]
): Omit<Item, 'id' | 'event_id' | 'created_at' | 'updated_at'>[] {
  const items: Omit<Item, 'id' | 'event_id' | 'created_at' | 'updated_at'>[] = [];

  // Ajouter les items sélectionnés du template
  if (template.template_items) {
    const itemsToAdd = selectedItemIds 
      ? template.template_items.filter(item => selectedItemIds.includes(item.id))
      : template.template_items.filter(item => item.is_essential);

    items.push(...itemsToAdd.map(item => ({
      suggester_id: null,
      name: item.name,
      category: item.category,
      estimated_cost: item.estimated_cost,
      estimated_effort: item.estimated_effort,
      is_suggestion: false,
      is_personal: false,
      assigned_participant_id: null,
      fixed_by_participant_id: null,
      completed: false,
      actual_cost: null,
      paid_by_participant_id: null,
    })));
  }

  // Ajouter les items personnalisés
  if (customItems) {
    items.push(...customItems.map(item => ({
      suggester_id: null,
      name: item.name,
      category: item.category,
      estimated_cost: item.estimated_cost,
      estimated_effort: item.estimated_effort,
      is_suggestion: false,
      is_personal: false,
      assigned_participant_id: null,
      fixed_by_participant_id: null,
      completed: false,
      actual_cost: null,
      paid_by_participant_id: null,
    })));
  }

  return items;
}

/**
 * Recherche des templates par nom ou tags
 */
export async function searchTemplates(query: string, userId?: string) {
  try {
    const { data, error } = await supabase
      .from('event_templates')
      .select(`
        *,
        template_items(*)
      `)
      .or(`category.eq.predefined,is_public.eq.true${userId ? `,created_by.eq.${userId}` : ''}`)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,template_data->>tags.ilike.%${query}%`)
      .order('usage_count', { ascending: false });

    if (error) {
      console.error('Error searching templates:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error searching templates:', error);
    return [];
  }
}

/**
 * Supprime un template personnalisé
 */
export async function deleteCustomTemplate(templateId: number, userId: string) {
  try {
    const { error } = await supabase
      .from('event_templates')
      .delete()
      .eq('id', templateId)
      .eq('created_by', userId)
      .eq('category', 'custom');

    if (error) {
      console.error('Error deleting template:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error deleting template:', error);
    return false;
  }
}
