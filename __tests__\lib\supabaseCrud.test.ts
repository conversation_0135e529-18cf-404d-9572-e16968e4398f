import {
  createEvent,
  updateEvent,
  deleteEvent,
  fetchEventsForUser,
  createItem,
  updateItem,
  deleteItem,
  fetchItemsForEvent,
} from '../../lib/supabaseCrud';
import { EventInsert, ItemInsert } from '../../lib/types';

// Mock Supabase
jest.mock('../../lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: mockEvent, error: null })),
        })),
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(() => Promise.resolve({ data: mockEvent, error: null })),
          })),
        })),
      })),
      delete: jest.fn(() => ({
        eq: jest.fn(() => Promise.resolve({ error: null })),
      })),
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => Promise.resolve({ data: [mockEvent], error: null })),
        })),
      })),
    })),
  },
}));

const mockEvent = {
  id: 1,
  title: 'Test Event',
  description: 'Test Description',
  date: '2024-12-31',
  time: '20:00',
  location: 'Test Location',
  organizer_id: 'user-123',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockItem = {
  id: 1,
  event_id: 1,
  suggester_id: null,
  name: 'Test Item',
  category: 'food',
  estimated_cost: 'medium',
  estimated_effort: 'low',
  is_suggestion: false,
  is_personal: false,
  assigned_participant_id: null,
  fixed_by_participant_id: null,
  completed: false,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

describe('supabaseCrud - Events', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createEvent', () => {
    it('should create event successfully', async () => {
      const eventData: EventInsert = {
        title: 'Test Event',
        description: 'Test Description',
        date: '2024-12-31',
        time: '20:00',
        location: 'Test Location',
        organizer_id: 'user-123',
      };

      const result = await createEvent(eventData);
      expect(result).toEqual(mockEvent);
    });

    it('should handle creation error', async () => {
      const { supabase } = require('../../lib/supabase');
      supabase.from.mockReturnValue({
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(() => Promise.resolve({ data: null, error: { message: 'Error' } })),
          })),
        })),
      });

      const eventData: EventInsert = {
        title: 'Test Event',
        description: 'Test Description',
        date: '2024-12-31',
        time: '20:00',
        location: 'Test Location',
        organizer_id: 'user-123',
      };

      const result = await createEvent(eventData);
      expect(result).toBeNull();
    });
  });

  describe('updateEvent', () => {
    it('should update event successfully', async () => {
      const updates = { title: 'Updated Event' };
      const result = await updateEvent(1, updates);
      expect(result).toEqual(mockEvent);
    });
  });

  describe('deleteEvent', () => {
    it('should delete event successfully', async () => {
      const result = await deleteEvent(1);
      expect(result).toBe(true);
    });

    it('should handle deletion error', async () => {
      const { supabase } = require('../../lib/supabase');
      supabase.from.mockReturnValue({
        delete: jest.fn(() => ({
          eq: jest.fn(() => Promise.resolve({ error: { message: 'Error' } })),
        })),
      });

      const result = await deleteEvent(1);
      expect(result).toBe(false);
    });
  });

  describe('fetchEventsForUser', () => {
    it('should fetch events successfully', async () => {
      const result = await fetchEventsForUser('user-123');
      expect(result).toEqual([mockEvent]);
    });
  });
});

describe('supabaseCrud - Items', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createItem', () => {
    it('should create item successfully', async () => {
      const { supabase } = require('../../lib/supabase');
      supabase.from.mockReturnValue({
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(() => Promise.resolve({ data: mockItem, error: null })),
          })),
        })),
      });

      const itemData: ItemInsert = {
        event_id: 1,
        suggester_id: null,
        name: 'Test Item',
        category: 'food',
        estimated_cost: 'medium',
        estimated_effort: 'low',
        is_suggestion: false,
        is_personal: false,
        assigned_participant_id: null,
        fixed_by_participant_id: null,
        completed: false,
      };

      const result = await createItem(itemData);
      expect(result).toEqual(mockItem);
    });
  });

  describe('updateItem', () => {
    it('should update item successfully', async () => {
      const { supabase } = require('../../lib/supabase');
      supabase.from.mockReturnValue({
        update: jest.fn(() => ({
          eq: jest.fn(() => ({
            select: jest.fn(() => ({
              single: jest.fn(() => Promise.resolve({ data: mockItem, error: null })),
            })),
          })),
        })),
      });

      const updates = { name: 'Updated Item' };
      const result = await updateItem(1, updates);
      expect(result).toEqual(mockItem);
    });
  });

  describe('deleteItem', () => {
    it('should delete item successfully', async () => {
      const { supabase } = require('../../lib/supabase');
      supabase.from.mockReturnValue({
        delete: jest.fn(() => ({
          eq: jest.fn(() => Promise.resolve({ error: null })),
        })),
      });

      const result = await deleteItem(1);
      expect(result).toBe(true);
    });
  });

  describe('fetchItemsForEvent', () => {
    it('should fetch items successfully', async () => {
      const { supabase } = require('../../lib/supabase');
      supabase.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => Promise.resolve({ data: [mockItem], error: null })),
          })),
        })),
      });

      const result = await fetchItemsForEvent(1);
      expect(result).toEqual([mockItem]);
    });
  });
});
