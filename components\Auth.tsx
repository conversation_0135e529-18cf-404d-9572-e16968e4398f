// --- Begin components\Auth.tsx ---
import React, { useState } from "react";
import { Alert, StyleSheet, View, Platform } from "react-native";
import { supabase } from "~/lib/supabase";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { useRouter } from "expo-router"; // Import useRouter

export default function Auth() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter(); // Initialize router

  async function signInWithEmail() {
    setLoading(true);
    const { error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password,
    });

    if (error) Alert.alert(error.message);
    setLoading(false);
  }

  async function signUpWithEmail() {
    setLoading(true);
    const {
      data: { session },
      error,
    } = await supabase.auth.signUp({
      email: email,
      password: password,
    });

    if (error) Alert.alert(error.message);
    if (!session)
      Alert.alert("Please check your inbox for email verification!");
    setLoading(false);
  }

  // Fonction pour gérer les événements de clic/pression en fonction de la plateforme
  const handlePress = (callback: () => void) => {
    return Platform.OS === "web"
      ? { onClick: callback }
      : { onPress: callback };
  };

  return (
    <View className="w-full flex gap-2">
      <View>
        <Input
          placeholder="Email"
          onChangeText={(text) => setEmail(text)}
          value={email}
          autoCapitalize={"none"}
        />
      </View>
      <View>
        <Input
          placeholder="Password"
          onChangeText={(text) => setPassword(text)}
          value={password}
          secureTextEntry={true}
          autoCapitalize={"none"}
        />
      </View>
      <View>
        <Button disabled={loading} {...handlePress(() => signInWithEmail())}>
          <Text>Sign in</Text>
        </Button>
      </View>
      <View>
        <Button disabled={loading} {...handlePress(() => signUpWithEmail())}>
          <Text>Sign up</Text>
        </Button>
      </View>
      <View>
        <Button {...handlePress(() => router.push("/auth"))}>
          <Text>Let's Sign Up!</Text>
        </Button>
      </View>
    </View>
  );
}
// --- End components\Auth.tsx ---
