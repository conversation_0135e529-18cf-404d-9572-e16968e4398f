# Scripts SQL pour Supabase

Ce dossier contient tous les scripts SQL nécessaires pour configurer la base de données Supabase.

## Organisation des fichiers

- `schema.sql` : Définition du schéma de base de données (tables, types, triggers)
- `functions.sql` : Définition des fonctions SQL (RPC)
- `policies.sql` : Définition des politiques RLS (Row Level Security)
- `fixes.sql` : Corrections pour les problèmes connus (récursion infinie dans les politiques)

## Ordre d'exécution recommandé

1. `schema.sql` - Crée la structure de la base de données
2. `functions.sql` - Ajoute les fonctions nécessaires
3. `policies.sql` - Configure les politiques de sécurité
4. `fixes.sql` - Applique les corrections pour les problèmes connus

## Notes importantes

- Les politiques RLS peuvent causer des problèmes de récursion infinie lorsqu'elles sont mal configurées
- Utilisez le client admin (`supabaseAdmin`) pour contourner les politiques RLS en cas de problème
- Vérifiez toujours l'existence des tables avant de les manipuler
- Certaines tables mentionnées dans le code (comme "items") peuvent ne pas exister dans la base de données actuelle
