# 🔧 Résolution des Erreurs d'Upload d'Avatars

## 🚨 **Erreurs Identifiées et Solutions**

### **Erreur 1: `new row violates row-level security policy`**
```
POST https://yqeabguwqlgivxeetbfr.supabase.co/storage/v1/bucket 400 (Bad Request)
StorageApiError: new row violates row-level security policy
```

**Cause** : Les politiques RLS (Row Level Security) ne sont pas encore créées dans Supabase.

**Solution** : Créer les politiques RLS manuellement dans Supabase Dashboard.

### **Erreur 2: `Failed to fetch` / `ERR_CONNECTION_RESET`**
```
POST https://yqeabguwqlgivxeetbfr.supabase.co/storage/v1/object/avatars/... net::ERR_CONNECTION_RESET
TypeError: Failed to fetch
```

**Cause** : Problème avec le format ou la taille du fichier uploadé.

**Solution** : ✅ **CORRIGÉ** - Amélioration de la gestion des fichiers dans `avatarService.ts`.

## 🛠️ **Actions Correctives Appliquées**

### ✅ **1. Service d'avatars amélioré**
- **Structure de fichiers** : `userId/timestamp.extension` au lieu de `userId_timestamp.extension`
- **Gestion d'erreurs** : Meilleure validation des fichiers et gestion des erreurs
- **Types MIME** : Détection automatique du type de contenu
- **Limite de taille** : Vérification de la taille (5MB max)
- **Suppression du bucket init** : Évite les erreurs de création de bucket existant

### ✅ **2. Politiques RLS préparées**
- **Script SQL** : `scripts/avatar_policies.sql` mis à jour
- **Suppression sécurisée** : Supprime les anciennes politiques avant création
- **Vérification** : Requêtes pour vérifier les politiques créées

## 🔐 **Étapes pour Créer les Politiques RLS**

### **Méthode 1 : Dashboard Supabase (Recommandée)**

1. **Aller sur** : https://supabase.com/dashboard
2. **Sélectionner votre projet** : yqeabguwqlgivxeetbfr
3. **Naviguer vers** : SQL Editor
4. **Copier-coller le script** :

```sql
-- Supprimer les anciennes politiques si elles existent
DROP POLICY IF EXISTS "Public read access for avatars" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatars" ON storage.objects;

-- 1. Lecture publique des avatars
CREATE POLICY "Public read access for avatars" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

-- 2. Upload pour utilisateurs authentifiés
CREATE POLICY "Authenticated users can upload avatars" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- 3. Mise à jour de ses propres avatars
CREATE POLICY "Users can update their own avatars" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- 4. Suppression de ses propres avatars
CREATE POLICY "Users can delete their own avatars" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);
```

5. **Exécuter le script** : Cliquer sur "Run"

### **Méthode 2 : Via l'interface Storage**

1. **Aller dans** : Storage > Policies
2. **Sélectionner** : `objects` table
3. **Créer les 4 politiques** une par une avec les conditions ci-dessus

## 🧪 **Tests après Correction**

### **Test 1 : Upload d'image**
1. **Se connecter** avec un compte utilisateur
2. **Aller dans Profil** → Cliquer sur l'avatar
3. **Sélectionner** "Photo de profil"
4. **Choisir une image** (< 5MB, JPEG/PNG/WebP)
5. **Vérifier** : Upload réussi, toast de succès

### **Test 2 : Vérification Supabase**
1. **Aller dans** : Supabase Dashboard > Storage > avatars
2. **Vérifier** : Dossier `{userId}` créé avec l'image
3. **Tester l'URL** : L'image est accessible publiquement

### **Test 3 : Suppression automatique**
1. **Uploader une nouvelle image** pour le même utilisateur
2. **Vérifier** : L'ancienne image est supprimée automatiquement

## 📋 **Checklist de Résolution**

- [x] ✅ Service d'avatars corrigé
- [x] ✅ Gestion d'erreurs améliorée
- [x] ✅ Structure de fichiers optimisée
- [x] ✅ Script SQL préparé
- [ ] 🔄 Politiques RLS créées dans Supabase
- [ ] 🔄 Tests d'upload effectués

## 🎯 **Résultat Attendu**

Après avoir créé les politiques RLS, le système d'avatars devrait fonctionner parfaitement avec :

- ✅ **Upload d'images** sans erreur
- ✅ **Stockage sécurisé** dans Supabase
- ✅ **Suppression automatique** des anciens avatars
- ✅ **Gestion d'erreurs** robuste
- ✅ **Feedback utilisateur** approprié

## 🚀 **Prochaines Étapes**

1. **Créer les politiques RLS** dans Supabase Dashboard
2. **Tester l'upload d'avatars** dans l'application
3. **Vérifier le stockage** dans Supabase Storage
4. **Confirmer le bon fonctionnement** du système complet

**Le système d'avatars sera alors 100% opérationnel ! 🎉**
