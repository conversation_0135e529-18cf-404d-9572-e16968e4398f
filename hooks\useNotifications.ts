/**
 * Hook personnalisé pour gérer les notifications dans Party Organizer
 */

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'expo-router';
import * as Notifications from 'expo-notifications';
import { 
  setupNotificationListeners,
  getNotificationHistory,
  markNotificationAsRead,
  markNotificationAsClicked 
} from '~/lib/notificationService';
import { 
  notifyParticipantJoined,
  notifyItemAdded,
  notifyCostUpdated,
  notifySettlementRequest,
  scheduleEventReminders,
  notifyFinancialSummaryReady 
} from '~/lib/smartNotifications';
import { useAuth } from '~/lib/AuthContext';
import { showToast } from '~/lib/toastService';

export interface NotificationHistoryItem {
  id: number;
  notification_type: string;
  title: string;
  body: string;
  data: any;
  sent_at: string;
  read_at: string | null;
  clicked_at: string | null;
  events?: {
    title: string;
    icon: string;
  };
}

export function useNotifications() {
  const { session } = useAuth();
  const router = useRouter();
  const [history, setHistory] = useState<NotificationHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  // Charger l'historique des notifications
  const loadHistory = useCallback(async () => {
    if (!session?.user?.id) return;

    setLoading(true);
    try {
      const notifications = await getNotificationHistory(session.user.id);
      setHistory(notifications);
      
      // Compter les non-lues
      const unread = notifications.filter(n => !n.read_at).length;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Erreur lors du chargement des notifications:', error);
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id]);

  // Marquer comme lue
  const markAsRead = useCallback(async (notificationId: number) => {
    if (!session?.user?.id) return;

    try {
      await markNotificationAsRead(session.user.id, notificationId);
      
      // Mettre à jour localement
      setHistory(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { ...n, read_at: new Date().toISOString() }
            : n
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Erreur lors du marquage comme lu:', error);
    }
  }, [session?.user?.id]);

  // Marquer comme cliquée et naviguer
  const handleNotificationClick = useCallback(async (notification: NotificationHistoryItem) => {
    if (!session?.user?.id) return;

    try {
      // Marquer comme cliquée
      await markNotificationAsClicked(session.user.id, notification.id);
      
      // Marquer comme lue si pas déjà fait
      if (!notification.read_at) {
        await markAsRead(notification.id);
      }

      // Navigation basée sur le type de notification
      const { type, eventId } = notification.data || {};
      
      switch (type) {
        case 'participant_joined':
        case 'item_added':
        case 'event_reminder':
          if (eventId) {
            router.push(`/event/${eventId}`);
          }
          break;
          
        case 'cost_updated':
          if (eventId) {
            router.push(`/event/${eventId}/finances`);
          }
          break;
          
        case 'settlement_request':
          if (eventId) {
            router.push(`/event/${eventId}/finances`);
          }
          break;
          
        default:
          if (eventId) {
            router.push(`/event/${eventId}`);
          }
      }
    } catch (error) {
      console.error('Erreur lors du clic sur notification:', error);
    }
  }, [session?.user?.id, router, markAsRead]);

  // Configurer les listeners de notifications
  useEffect(() => {
    const cleanup = setupNotificationListeners(
      // Notification reçue
      (notification) => {
        console.log('Notification reçue:', notification);
        
        // Recharger l'historique pour inclure la nouvelle notification
        loadHistory();
        
        // Afficher un toast si l'app est ouverte
        showToast(notification.request.content.title || 'Nouvelle notification', {
          type: 'info'
        });
      },
      
      // Notification cliquée
      (response) => {
        console.log('Notification cliquée:', response);
        
        const { type, eventId } = response.notification.request.content.data || {};
        
        // Navigation directe basée sur les données
        if (eventId) {
          switch (type) {
            case 'cost_updated':
            case 'settlement_request':
              router.push(`/event/${eventId}/finances`);
              break;
            default:
              router.push(`/event/${eventId}`);
          }
        }
      }
    );

    return cleanup;
  }, [loadHistory, router]);

  // Charger l'historique au montage
  useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  // Fonctions d'envoi de notifications (pour les organisateurs)
  const notifications = {
    // Notifier qu'un participant a rejoint
    participantJoined: notifyParticipantJoined,
    
    // Notifier qu'un item a été ajouté
    itemAdded: notifyItemAdded,
    
    // Notifier qu'un coût a été mis à jour
    costUpdated: notifyCostUpdated,
    
    // Notifier une demande de remboursement
    settlementRequest: notifySettlementRequest,
    
    // Programmer des rappels d'événement
    scheduleReminders: scheduleEventReminders,
    
    // Notifier que le résumé financier est prêt
    financialSummaryReady: notifyFinancialSummaryReady,
  };

  return {
    // État
    history,
    loading,
    unreadCount,
    
    // Actions
    loadHistory,
    markAsRead,
    handleNotificationClick,
    
    // Fonctions d'envoi
    notifications,
  };
}

// Hook simplifié pour juste obtenir le nombre de notifications non lues
export function useUnreadNotifications() {
  const { session } = useAuth();
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (!session?.user?.id) {
      setUnreadCount(0);
      return;
    }

    const loadUnreadCount = async () => {
      try {
        const notifications = await getNotificationHistory(session.user.id, 20);
        const unread = notifications.filter(n => !n.read_at).length;
        setUnreadCount(unread);
      } catch (error) {
        console.error('Erreur lors du chargement du nombre de notifications:', error);
      }
    };

    loadUnreadCount();

    // Recharger périodiquement
    const interval = setInterval(loadUnreadCount, 30000); // Toutes les 30 secondes

    return () => clearInterval(interval);
  }, [session?.user?.id]);

  return unreadCount;
}
