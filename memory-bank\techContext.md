# Tech Context

## Technologies Used

- **React Native (with Expo)**: Core framework for cross-platform mobile development.
- **TypeScript**: For static typing.
- **NativeWind v4.1.23**: For using Tailwind CSS in React Native.
- **Tailwind CSS v3.3.5**: Utility-first CSS framework.
- **ShadCN UI (for React Native)**: UI component library adapted for React Native, utilizing:
  - `@rn-primitives/*`: Core primitive components.
  - `class-variance-authority`: For managing component variants.
  - `clsx`: For constructing class strings conditionally.
  - `tailwind-merge`: For intelligently merging Tailwind CSS classes.
  - `lucide-react-native`: For icons.
  - `tailwindcss-animate`: For animations.
- **Expo Router**: For file-system based routing.
- **Supabase**: Backend-as-a-Service (used for authentication and data, e.g., `fetchProfile`, `updateProfile`).
- **React Navigation**: For navigation theming (`@react-navigation/native`).
- **Zustand**: State management (implied by `useToastStore`).

## Development Setup

- **Expo CLI**: Used for running the development server (`expo start`).
- **Tailwind CSS Processing**: `global.css` is processed by Tailwind CLI into `node_modules/.cache/nativewind/global.css` via a `postinstall` script in `package.json`.
- **ShadCN UI CLI**: Components are added using `npx shadcn-ui@latest add <component-name>`.
- **Configuration Files**:
  - `package.json`: Manages dependencies and scripts.
  - `tailwind.config.js`: Configures Tailwind CSS, including `darkMode: "class"` and NativeWind preset.
  - `components.json`: ShadCN UI configuration (style: default, baseColor: slate, cssVariables: true, css: `app/globals.css` - though `global.css` at root is effectively used).
  - `global.css`: Defines global styles and CSS variables for light and dark themes.
  - `lib/utils.ts`: Contains the `cn` utility function for class name merging.
  - `lib/useColorScheme.tsx`: Custom hook wrapping NativeWind's `useColorScheme` for theme management.
  - `app/_layout.tsx`: Root layout, handles theme application (including web-specific `document.documentElement.classList.toggle`) and React Navigation theming.

## Technical Constraints

- Primarily targets React Native (iOS/Android) and Web via Expo.
- Dark mode implementation relies on CSS variables and the `.dark` class strategy.

## Dependencies

- Supabase for backend services.
- Relies on NativeWind for Tailwind CSS integration in React Native.

## Tool Usage Patterns

- **ShadCN UI**: Components are added via CLI and customized as needed. They are stored in `components/ui/`.
- **NativeWind**: Used for styling all components with Tailwind CSS classes.
- **Dark Mode**: Managed by `lib/useColorScheme.tsx` (wrapping NativeWind) and `components/ThemeToggle.tsx`. CSS variables in `global.css` define theme colors. The `.dark` class is applied to the root element.
- **Routing**: Expo Router is used for navigation.
- **State Management**: Zustand is used for global state (e.g., Toasts).
- **Database Schema Management**: The full schema is intended to be maintained in `supabase/sql/schema.sql`. This file should be updated alongside any direct schema changes made via the Supabase dashboard or if a migration system isn't used. Auto-generated types for the client are created using `npx supabase gen types typescript ... > lib/database.types.ts`.

_(Cline's Note: This file provides details on the technical landscape of the project, supporting `projectbrief.md` and `systemPatterns.md`.)_
