import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { ParticipantManager } from '../../components/ParticipantManager';

// Mock des dépendances
jest.mock('../../lib/toastService', () => ({
  showToast: jest.fn(),
}));

describe('ParticipantManager', () => {
  const mockProps = {
    participants: ['Alice', 'Bob'],
    onParticipantsChange: jest.fn(),
    currentUserName: 'Organisateur',
    addButtonText: 'Ajouter',
    className: '',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render participant list correctly', () => {
    const { getByText } = render(<ParticipantManager {...mockProps} />);
    
    expect(getByText('Alice')).toBeTruthy();
    expect(getByText('Bob')).toBeTruthy();
    expect(getByText('Organisateur')).toBeTruthy();
  });

  it('should add a new participant', async () => {
    const { getByPlaceholderText, getByText } = render(
      <ParticipantManager {...mockProps} />
    );
    
    const input = getByPlaceholderText('Ex: Maxence Manson');
    const addButton = getByText('Ajouter');
    
    fireEvent.changeText(input, 'Charlie');
    fireEvent.press(addButton);
    
    await waitFor(() => {
      expect(mockProps.onParticipantsChange).toHaveBeenCalledWith([
        'Alice',
        'Bob',
        'Charlie',
      ]);
    });
  });

  it('should not add empty participant name', async () => {
    const { getByPlaceholderText, getByText } = render(
      <ParticipantManager {...mockProps} />
    );
    
    const input = getByPlaceholderText('Ex: Maxence Manson');
    const addButton = getByText('Ajouter');
    
    fireEvent.changeText(input, '   '); // Espaces seulement
    fireEvent.press(addButton);
    
    await waitFor(() => {
      expect(mockProps.onParticipantsChange).not.toHaveBeenCalled();
    });
  });

  it('should not add duplicate participant', async () => {
    const { getByPlaceholderText, getByText } = render(
      <ParticipantManager {...mockProps} />
    );
    
    const input = getByPlaceholderText('Ex: Maxence Manson');
    const addButton = getByText('Ajouter');
    
    fireEvent.changeText(input, 'Alice'); // Nom déjà existant
    fireEvent.press(addButton);
    
    await waitFor(() => {
      expect(mockProps.onParticipantsChange).not.toHaveBeenCalled();
    });
  });

  it('should remove participant', async () => {
    const { getAllByText } = render(<ParticipantManager {...mockProps} />);
    
    const removeButtons = getAllByText('×');
    fireEvent.press(removeButtons[0]); // Supprimer Alice
    
    await waitFor(() => {
      expect(mockProps.onParticipantsChange).toHaveBeenCalledWith(['Bob']);
    });
  });

  it('should not add current user name', async () => {
    const { getByPlaceholderText, getByText } = render(
      <ParticipantManager {...mockProps} />
    );
    
    const input = getByPlaceholderText('Ex: Maxence Manson');
    const addButton = getByText('Ajouter');
    
    fireEvent.changeText(input, 'Organisateur'); // Nom de l'utilisateur actuel
    fireEvent.press(addButton);
    
    await waitFor(() => {
      expect(mockProps.onParticipantsChange).not.toHaveBeenCalled();
    });
  });

  it('should clear input after successful add', async () => {
    const { getByPlaceholderText, getByText } = render(
      <ParticipantManager {...mockProps} />
    );
    
    const input = getByPlaceholderText('Ex: Maxence Manson');
    const addButton = getByText('Ajouter');
    
    fireEvent.changeText(input, 'Charlie');
    fireEvent.press(addButton);
    
    await waitFor(() => {
      expect(input.props.value).toBe('');
    });
  });
});
