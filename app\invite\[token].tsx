import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  fetchEventByInvitationToken,
  updateParticipant,
  fetchParticipantByToken,
} from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Participant } from "~/lib/types";
import { Database } from "~/lib/database.types";

type ParticipantStatusEnum = Database["public"]["Enums"]["participant_status"];

export default function InvitationScreen() {
  const { token } = useLocalSearchParams<{ token: string }>();
  const { session } = useAuth();
  const router = useRouter();
  
  const [event, setEvent] = useState<Event | null>(null);
  const [participant, setParticipant] = useState<Participant | null>(null);
  const [loading, setLoading] = useState(true);
  const [responding, setResponding] = useState(false);
  const [anonymousName, setAnonymousName] = useState("");
  const [error, setError] = useState<string | null>(null);

  const loadInvitationData = async () => {
    if (!token) {
      setError("Token d'invitation manquant");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Récupérer les données de l'invitation
      const [eventData, participantData] = await Promise.all([
        fetchEventByInvitationToken(token),
        fetchParticipantByToken(token),
      ]);

      if (!eventData || !participantData) {
        setError("Invitation invalide ou expirée");
        return;
      }

      setEvent(eventData);
      setParticipant(participantData);
      
      // Pré-remplir le nom si disponible
      if (participantData.anonymous_name) {
        setAnonymousName(participantData.anonymous_name);
      }
      
    } catch (error) {
      console.error("Error loading invitation:", error);
      setError("Erreur lors du chargement de l'invitation");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInvitationData();
  }, [token]);

  const handleResponse = async (status: ParticipantStatusEnum) => {
    if (!participant || !event) return;

    try {
      setResponding(true);

      // Préparer les données de mise à jour
      const updateData: Partial<Participant> = {
        status,
      };

      // Si l'utilisateur a fourni un nom et qu'il n'y en avait pas
      if (anonymousName.trim() && !participant.anonymous_name) {
        updateData.anonymous_name = anonymousName.trim();
      }

      // Si l'utilisateur est connecté, lier le compte
      if (session?.user?.id && !participant.user_id) {
        updateData.user_id = session.user.id;
      }

      const updatedParticipant = await updateParticipant(participant.id, updateData);

      if (updatedParticipant) {
        const statusText = status === "accepted" ? "accepté" : 
                          status === "declined" ? "refusé" : 
                          status === "maybe" ? "marqué comme peut-être" : "mis à jour";
        
        showToast(`Vous avez ${statusText} l'invitation !`, { type: "success" });
        
        // Rediriger vers l'événement si accepté
        if (status === "accepted") {
          router.push(`/event/${event.id}`);
        } else {
          router.push("/(tabs)");
        }
      } else {
        throw new Error("Échec de la mise à jour");
      }
    } catch (error) {
      console.error("Error responding to invitation:", error);
      showToast("Erreur lors de la réponse à l'invitation.", { type: "error" });
    } finally {
      setResponding(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-muted-foreground">
          Chargement de l'invitation...
        </Text>
      </View>
    );
  }

  if (error || !event || !participant) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-6xl mb-4">❌</Text>
        <Text className="text-xl font-semibold mb-2 text-center">
          Invitation invalide
        </Text>
        <Text className="text-muted-foreground text-center mb-6">
          {error || "Cette invitation n'existe pas ou a expiré."}
        </Text>
        <Button onPress={() => router.push("/(tabs)")}>
          <Text>Retour à l'accueil</Text>
        </Button>
      </View>
    );
  }

  // Si l'utilisateur a déjà répondu
  if (participant.status !== "pending") {
    const statusEmoji = participant.status === "accepted" ? "✅" : 
                       participant.status === "declined" ? "❌" : 
                       participant.status === "maybe" ? "❓" : "⏳";
    
    const statusText = participant.status === "accepted" ? "accepté" : 
                      participant.status === "declined" ? "refusé" : 
                      participant.status === "maybe" ? "marqué comme peut-être" : "en attente";

    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-6xl mb-4">{statusEmoji}</Text>
        <Text className="text-xl font-semibold mb-2 text-center">
          Invitation déjà traitée
        </Text>
        <Text className="text-muted-foreground text-center mb-6">
          Vous avez déjà {statusText} cette invitation.
        </Text>
        <View className="flex-row gap-3">
          <Button 
            variant="outline" 
            onPress={() => router.push("/(tabs)")}
          >
            <Text>Accueil</Text>
          </Button>
          {participant.status === "accepted" && (
            <Button onPress={() => router.push(`/event/${event.id}`)}>
              <Text>Voir l'événement</Text>
            </Button>
          )}
        </View>
      </View>
    );
  }

  const eventDate = new Date(event.date_time);
  const formattedDate = eventDate.toLocaleDateString("fr-FR", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  const formattedTime = eventDate.toLocaleTimeString("fr-FR", {
    hour: "2-digit",
    minute: "2-digit",
  });

  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: "#f9fafb" }}
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-md mx-auto" : "w-full"}>
        {/* Header */}
        <Card className="mb-6">
          <CardHeader className="items-center">
            <Text className="text-6xl mb-2">{event.icon || "🎉"}</Text>
            <CardTitle className="text-xl text-center">
              Invitation à {event.title}
            </CardTitle>
          </CardHeader>
          <CardContent className="gap-3">
            {event.description && (
              <Text className="text-center text-muted-foreground">
                {event.description}
              </Text>
            )}
            <View className="flex-row items-center justify-center">
              <Text className="text-muted-foreground mr-2">📅</Text>
              <Text>{formattedDate}</Text>
            </View>
            <View className="flex-row items-center justify-center">
              <Text className="text-muted-foreground mr-2">🕐</Text>
              <Text>{formattedTime}</Text>
            </View>
            {event.location && (
              <View className="flex-row items-center justify-center">
                <Text className="text-muted-foreground mr-2">📍</Text>
                <Text>{event.location}</Text>
              </View>
            )}
          </CardContent>
        </Card>

        {/* Nom (si pas connecté) */}
        {!session && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Votre nom (optionnel)</CardTitle>
            </CardHeader>
            <CardContent>
              <Label className="mb-2">Comment souhaitez-vous apparaître ?</Label>
              <Input
                placeholder="Votre nom ou pseudo"
                value={anonymousName}
                onChangeText={setAnonymousName}
              />
              <Text className="text-xs text-muted-foreground mt-2">
                Ceci aidera l'organisateur à vous identifier
              </Text>
            </CardContent>
          </Card>
        )}

        {/* Boutons de réponse */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Votre réponse</CardTitle>
          </CardHeader>
          <CardContent className="gap-3">
            <Button
              onPress={() => handleResponse("accepted")}
              disabled={responding}
              className="w-full h-12 bg-green-600"
            >
              <Text className="text-white font-medium">
                ✅ J'accepte l'invitation
              </Text>
            </Button>
            
            <Button
              variant="outline"
              onPress={() => handleResponse("maybe")}
              disabled={responding}
              className="w-full h-12"
            >
              <Text>❓ Peut-être</Text>
            </Button>
            
            <Button
              variant="outline"
              onPress={() => handleResponse("declined")}
              disabled={responding}
              className="w-full h-12 border-red-300"
            >
              <Text className="text-red-600">❌ Je refuse</Text>
            </Button>
            
            {responding && (
              <View className="flex-row items-center justify-center mt-2">
                <ActivityIndicator size="small" />
                <Text className="ml-2 text-muted-foreground">
                  Envoi de la réponse...
                </Text>
              </View>
            )}
          </CardContent>
        </Card>

        {!session && (
          <Card className="mt-6">
            <CardContent className="pt-6">
              <Text className="text-sm text-muted-foreground text-center">
                💡 Créez un compte pour gérer facilement vos événements et invitations
              </Text>
              <Button
                variant="outline"
                onPress={() => router.push("/(tabs)/profile")}
                className="w-full mt-3"
              >
                <Text>Créer un compte</Text>
              </Button>
            </CardContent>
          </Card>
        )}
      </View>
    </ScrollView>
  );
}
