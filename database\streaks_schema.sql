-- Extension du schéma pour les streaks et habitudes

-- Table des streaks utilisateur
CREATE TABLE IF NOT EXISTS user_streaks (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  streak_type VARCHAR(50) NOT NULL, -- daily_login, weekly_event, monthly_organization, participation_streak
  current_streak INTEGER NOT NULL DEFAULT 0,
  longest_streak INTEGER NOT NULL DEFAULT 0,
  last_activity_date DATE NOT NULL,
  streak_start_date DATE NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, streak_type)
);

-- Table des habitudes quotidiennes
CREATE TABLE IF NOT EXISTS daily_habits (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  events_created INTEGER NOT NULL DEFAULT 0,
  events_participated INTEGER NOT NULL DEFAULT 0,
  items_managed INTEGER NOT NULL DEFAULT 0,
  participants_invited INTEGER NOT NULL DEFAULT 0,
  app_opens INTEGER NOT NULL DEFAULT 0,
  time_spent_minutes INTEGER NOT NULL DEFAULT 0,
  achievements_unlocked INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Table des fonctionnalités débloquées
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS unlocked_features JSONB DEFAULT '{}';

-- Table des challenges communautaires
CREATE TABLE IF NOT EXISTS community_challenges (
  id SERIAL PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  icon VARCHAR(10) NOT NULL,
  challenge_type VARCHAR(50) NOT NULL, -- individual, community, competitive
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  target_metric VARCHAR(50) NOT NULL, -- events_created, participants_invited, etc.
  target_value INTEGER NOT NULL,
  reward_points INTEGER NOT NULL DEFAULT 0,
  reward_badge_id INTEGER REFERENCES badges(id),
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table de participation aux challenges
CREATE TABLE IF NOT EXISTS challenge_participations (
  id SERIAL PRIMARY KEY,
  challenge_id INTEGER NOT NULL REFERENCES community_challenges(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  current_progress INTEGER NOT NULL DEFAULT 0,
  is_completed BOOLEAN NOT NULL DEFAULT false,
  completed_at TIMESTAMP WITH TIME ZONE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(challenge_id, user_id)
);

-- Table des collections de badges
CREATE TABLE IF NOT EXISTS badge_collections (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  icon VARCHAR(10) NOT NULL,
  badge_ids INTEGER[] NOT NULL, -- Array des IDs de badges
  completion_reward_points INTEGER NOT NULL DEFAULT 0,
  completion_reward_badge_id INTEGER REFERENCES badges(id),
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table de progression des collections
CREATE TABLE IF NOT EXISTS user_collection_progress (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  collection_id INTEGER NOT NULL REFERENCES badge_collections(id) ON DELETE CASCADE,
  completed_badges INTEGER[] NOT NULL DEFAULT '{}',
  is_completed BOOLEAN NOT NULL DEFAULT false,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, collection_id)
);

-- Table des événements saisonniers
CREATE TABLE IF NOT EXISTS seasonal_events (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  icon VARCHAR(10) NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  special_badges INTEGER[] NOT NULL DEFAULT '{}', -- Badges spéciaux disponibles
  bonus_multiplier DECIMAL(3,2) NOT NULL DEFAULT 1.0, -- Multiplicateur de points
  theme_data JSONB DEFAULT '{}', -- Données de thème (couleurs, etc.)
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_user_streaks_user_id ON user_streaks(user_id);
CREATE INDEX IF NOT EXISTS idx_user_streaks_type ON user_streaks(streak_type);
CREATE INDEX IF NOT EXISTS idx_user_streaks_active ON user_streaks(is_active);
CREATE INDEX IF NOT EXISTS idx_daily_habits_user_date ON daily_habits(user_id, date);
CREATE INDEX IF NOT EXISTS idx_daily_habits_date ON daily_habits(date);
CREATE INDEX IF NOT EXISTS idx_challenge_participations_user ON challenge_participations(user_id);
CREATE INDEX IF NOT EXISTS idx_challenge_participations_challenge ON challenge_participations(challenge_id);
CREATE INDEX IF NOT EXISTS idx_community_challenges_active ON community_challenges(is_active);
CREATE INDEX IF NOT EXISTS idx_community_challenges_dates ON community_challenges(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_badge_collections_active ON badge_collections(is_active);
CREATE INDEX IF NOT EXISTS idx_user_collection_progress_user ON user_collection_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_seasonal_events_active ON seasonal_events(is_active);
CREATE INDEX IF NOT EXISTS idx_seasonal_events_dates ON seasonal_events(start_date, end_date);

-- Triggers pour updated_at
CREATE TRIGGER update_user_streaks_updated_at BEFORE UPDATE ON user_streaks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_habits_updated_at BEFORE UPDATE ON daily_habits
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_community_challenges_updated_at BEFORE UPDATE ON community_challenges
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_badge_collections_updated_at BEFORE UPDATE ON badge_collections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_collection_progress_updated_at BEFORE UPDATE ON user_collection_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_seasonal_events_updated_at BEFORE UPDATE ON seasonal_events
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Politiques RLS
ALTER TABLE user_streaks ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_habits ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE challenge_participations ENABLE ROW LEVEL SECURITY;
ALTER TABLE badge_collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_collection_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE seasonal_events ENABLE ROW LEVEL SECURITY;

-- Politiques pour user_streaks
CREATE POLICY "Users can view own streaks" ON user_streaks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own streaks" ON user_streaks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own streaks" ON user_streaks
    FOR UPDATE USING (auth.uid() = user_id);

-- Politiques pour daily_habits
CREATE POLICY "Users can view own habits" ON daily_habits
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own habits" ON daily_habits
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own habits" ON daily_habits
    FOR UPDATE USING (auth.uid() = user_id);

-- Politiques pour community_challenges (lecture publique)
CREATE POLICY "Challenges are viewable by everyone" ON community_challenges
    FOR SELECT USING (is_active = true);

-- Politiques pour challenge_participations
CREATE POLICY "Users can view own participations" ON challenge_participations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own participations" ON challenge_participations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own participations" ON challenge_participations
    FOR UPDATE USING (auth.uid() = user_id);

-- Politiques pour badge_collections (lecture publique)
CREATE POLICY "Collections are viewable by everyone" ON badge_collections
    FOR SELECT USING (is_active = true);

-- Politiques pour user_collection_progress
CREATE POLICY "Users can view own collection progress" ON user_collection_progress
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own collection progress" ON user_collection_progress
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own collection progress" ON user_collection_progress
    FOR UPDATE USING (auth.uid() = user_id);

-- Politiques pour seasonal_events (lecture publique)
CREATE POLICY "Seasonal events are viewable by everyone" ON seasonal_events
    FOR SELECT USING (is_active = true);

-- Fonction pour calculer le score de streak global
CREATE OR REPLACE FUNCTION calculate_user_streak_score(p_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    total_score INTEGER := 0;
    streak_record user_streaks%ROWTYPE;
    multiplier INTEGER;
BEGIN
    FOR streak_record IN 
        SELECT * FROM user_streaks 
        WHERE user_id = p_user_id AND is_active = true
    LOOP
        -- Multiplicateur selon le type de streak
        CASE streak_record.streak_type
            WHEN 'daily_login' THEN multiplier := 1;
            WHEN 'weekly_event' THEN multiplier := 3;
            WHEN 'monthly_organization' THEN multiplier := 5;
            WHEN 'participation_streak' THEN multiplier := 2;
            ELSE multiplier := 1;
        END CASE;
        
        total_score := total_score + (streak_record.current_streak * multiplier);
    END LOOP;
    
    RETURN total_score;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour mettre à jour la progression des challenges
CREATE OR REPLACE FUNCTION update_challenge_progress(
    p_user_id UUID,
    p_metric VARCHAR,
    p_increment INTEGER DEFAULT 1
)
RETURNS void AS $$
DECLARE
    challenge_record community_challenges%ROWTYPE;
    participation_record challenge_participations%ROWTYPE;
BEGIN
    -- Parcourir tous les challenges actifs pour cette métrique
    FOR challenge_record IN 
        SELECT * FROM community_challenges 
        WHERE is_active = true 
        AND target_metric = p_metric
        AND NOW() BETWEEN start_date AND end_date
    LOOP
        -- Récupérer ou créer la participation
        SELECT * INTO participation_record 
        FROM challenge_participations 
        WHERE challenge_id = challenge_record.id AND user_id = p_user_id;
        
        IF NOT FOUND THEN
            INSERT INTO challenge_participations (challenge_id, user_id, current_progress)
            VALUES (challenge_record.id, p_user_id, p_increment);
        ELSE
            UPDATE challenge_participations 
            SET current_progress = current_progress + p_increment,
                is_completed = (current_progress + p_increment >= challenge_record.target_value),
                completed_at = CASE 
                    WHEN current_progress + p_increment >= challenge_record.target_value 
                    THEN NOW() 
                    ELSE completed_at 
                END
            WHERE challenge_id = challenge_record.id AND user_id = p_user_id;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour vérifier les collections complétées
CREATE OR REPLACE FUNCTION check_collection_completion(p_user_id UUID)
RETURNS TABLE(collection_id INTEGER, collection_name VARCHAR) AS $$
DECLARE
    collection_record badge_collections%ROWTYPE;
    user_badges INTEGER[];
    completed_count INTEGER;
BEGIN
    -- Récupérer tous les badges de l'utilisateur
    SELECT ARRAY_AGG(badge_id) INTO user_badges
    FROM user_achievements 
    WHERE user_id = p_user_id AND is_completed = true;
    
    -- Parcourir toutes les collections actives
    FOR collection_record IN 
        SELECT * FROM badge_collections WHERE is_active = true
    LOOP
        -- Compter combien de badges de la collection l'utilisateur possède
        SELECT COUNT(*) INTO completed_count
        FROM unnest(collection_record.badge_ids) AS badge_id
        WHERE badge_id = ANY(user_badges);
        
        -- Si la collection est complète
        IF completed_count = array_length(collection_record.badge_ids, 1) THEN
            -- Vérifier si pas déjà marquée comme complétée
            IF NOT EXISTS (
                SELECT 1 FROM user_collection_progress 
                WHERE user_id = p_user_id 
                AND collection_id = collection_record.id 
                AND is_completed = true
            ) THEN
                -- Marquer comme complétée
                INSERT INTO user_collection_progress (
                    user_id, collection_id, completed_badges, is_completed, completed_at
                ) VALUES (
                    p_user_id, collection_record.id, collection_record.badge_ids, true, NOW()
                ) ON CONFLICT (user_id, collection_id) 
                DO UPDATE SET 
                    completed_badges = collection_record.badge_ids,
                    is_completed = true,
                    completed_at = NOW();
                
                -- Retourner la collection complétée
                collection_id := collection_record.id;
                collection_name := collection_record.name;
                RETURN NEXT;
            END IF;
        END IF;
    END LOOP;
    
    RETURN;
END;
$$ LANGUAGE plpgsql;
