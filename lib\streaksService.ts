/**
 * Service de gestion des streaks et habitudes pour Party Organizer
 * Encourage l'utilisation régulière et la formation d'habitudes
 */

import { supabase } from './supabase';
import { cache } from './cacheService';
import { showToast } from './toastService';
import { achievements } from './achievementsService';

export interface UserStreak {
  id: number;
  user_id: string;
  streak_type: 'daily_login' | 'weekly_event' | 'monthly_organization' | 'participation_streak';
  current_streak: number;
  longest_streak: number;
  last_activity_date: string;
  streak_start_date: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface StreakMilestone {
  days: number;
  title: string;
  description: string;
  icon: string;
  reward_points: number;
  reward_type: 'badge' | 'points' | 'feature' | 'cosmetic';
  reward_data?: Record<string, any>;
}

export interface HabitTracker {
  user_id: string;
  date: string;
  events_created: number;
  events_participated: number;
  items_managed: number;
  participants_invited: number;
  app_opens: number;
  time_spent_minutes: number;
  achievements_unlocked: number;
}

class StreaksService {
  private readonly CACHE_TTL = 10 * 60 * 1000; // 10 minutes

  /**
   * Milestones de streaks prédéfinis
   */
  private getStreakMilestones(): Record<string, StreakMilestone[]> {
    return {
      daily_login: [
        { days: 3, title: 'Régulier', description: 'Connecté 3 jours d\'affilée', icon: '🔥', reward_points: 50, reward_type: 'points' },
        { days: 7, title: 'Assidu', description: 'Connecté 7 jours d\'affilée', icon: '⚡', reward_points: 150, reward_type: 'badge' },
        { days: 14, title: 'Dévoué', description: 'Connecté 14 jours d\'affilée', icon: '💎', reward_points: 300, reward_type: 'badge' },
        { days: 30, title: 'Légendaire', description: 'Connecté 30 jours d\'affilée', icon: '👑', reward_points: 500, reward_type: 'feature', reward_data: { unlock: 'premium_themes' } },
        { days: 60, title: 'Immortel', description: 'Connecté 60 jours d\'affilée', icon: '🌟', reward_points: 1000, reward_type: 'badge' },
        { days: 100, title: 'Centenaire', description: 'Connecté 100 jours d\'affilée', icon: '🏆', reward_points: 2000, reward_type: 'cosmetic', reward_data: { unlock: 'golden_profile' } },
      ],
      weekly_event: [
        { days: 2, title: 'Organisateur Hebdo', description: '2 semaines avec au moins 1 événement', icon: '📅', reward_points: 100, reward_type: 'points' },
        { days: 4, title: 'Planificateur Régulier', description: '4 semaines consécutives d\'organisation', icon: '🗓️', reward_points: 250, reward_type: 'badge' },
        { days: 8, title: 'Maître du Planning', description: '8 semaines consécutives d\'organisation', icon: '📋', reward_points: 500, reward_type: 'feature', reward_data: { unlock: 'advanced_templates' } },
        { days: 12, title: 'Organisateur Professionnel', description: '12 semaines consécutives', icon: '💼', reward_points: 800, reward_type: 'badge' },
      ],
      participation_streak: [
        { days: 5, title: 'Participant Actif', description: '5 événements consécutifs', icon: '🎉', reward_points: 75, reward_type: 'points' },
        { days: 10, title: 'Fêtard Régulier', description: '10 événements consécutifs', icon: '🎊', reward_points: 200, reward_type: 'badge' },
        { days: 20, title: 'Pilier de la Communauté', description: '20 événements consécutifs', icon: '🏛️', reward_points: 400, reward_type: 'feature', reward_data: { unlock: 'vip_status' } },
      ],
    };
  }

  /**
   * Met à jour le streak de connexion quotidienne
   */
  async updateDailyLoginStreak(userId: string): Promise<UserStreak | null> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Récupérer le streak actuel
      const { data: existingStreak } = await supabase
        .from('user_streaks')
        .select('*')
        .eq('user_id', userId)
        .eq('streak_type', 'daily_login')
        .single();

      let newStreak: UserStreak;

      if (!existingStreak) {
        // Créer un nouveau streak
        const { data, error } = await supabase
          .from('user_streaks')
          .insert({
            user_id: userId,
            streak_type: 'daily_login',
            current_streak: 1,
            longest_streak: 1,
            last_activity_date: today,
            streak_start_date: today,
            is_active: true,
          })
          .select()
          .single();

        if (error) throw error;
        newStreak = data;
      } else {
        const lastDate = new Date(existingStreak.last_activity_date);
        const todayDate = new Date(today);
        const diffDays = Math.floor((todayDate.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
          // Déjà connecté aujourd'hui
          return existingStreak;
        } else if (diffDays === 1) {
          // Streak continue
          const newCurrentStreak = existingStreak.current_streak + 1;
          const { data, error } = await supabase
            .from('user_streaks')
            .update({
              current_streak: newCurrentStreak,
              longest_streak: Math.max(existingStreak.longest_streak, newCurrentStreak),
              last_activity_date: today,
              is_active: true,
            })
            .eq('id', existingStreak.id)
            .select()
            .single();

          if (error) throw error;
          newStreak = data;
        } else {
          // Streak cassé, recommencer
          const { data, error } = await supabase
            .from('user_streaks')
            .update({
              current_streak: 1,
              last_activity_date: today,
              streak_start_date: today,
              is_active: true,
            })
            .eq('id', existingStreak.id)
            .select()
            .single();

          if (error) throw error;
          newStreak = data;
        }
      }

      // Vérifier les milestones
      await this.checkStreakMilestones(userId, 'daily_login', newStreak.current_streak);

      return newStreak;
    } catch (error) {
      console.error('Error updating daily login streak:', error);
      return null;
    }
  }

  /**
   * Met à jour le streak d'organisation hebdomadaire
   */
  async updateWeeklyEventStreak(userId: string): Promise<void> {
    try {
      const now = new Date();
      const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
      const weekStartStr = weekStart.toISOString().split('T')[0];

      // Vérifier si un événement a été créé cette semaine
      const { data: eventsThisWeek } = await supabase
        .from('events')
        .select('id')
        .eq('organizer_id', userId)
        .gte('created_at', weekStartStr);

      if (!eventsThisWeek || eventsThisWeek.length === 0) {
        return; // Pas d'événement cette semaine
      }

      // Récupérer le streak actuel
      const { data: existingStreak } = await supabase
        .from('user_streaks')
        .select('*')
        .eq('user_id', userId)
        .eq('streak_type', 'weekly_event')
        .single();

      if (!existingStreak) {
        // Créer un nouveau streak
        await supabase
          .from('user_streaks')
          .insert({
            user_id: userId,
            streak_type: 'weekly_event',
            current_streak: 1,
            longest_streak: 1,
            last_activity_date: weekStartStr,
            streak_start_date: weekStartStr,
            is_active: true,
          });

        await this.checkStreakMilestones(userId, 'weekly_event', 1);
      } else {
        const lastWeek = new Date(existingStreak.last_activity_date);
        const weeksDiff = Math.floor((weekStart.getTime() - lastWeek.getTime()) / (1000 * 60 * 60 * 24 * 7));

        if (weeksDiff === 1) {
          // Streak continue
          const newCurrentStreak = existingStreak.current_streak + 1;
          await supabase
            .from('user_streaks')
            .update({
              current_streak: newCurrentStreak,
              longest_streak: Math.max(existingStreak.longest_streak, newCurrentStreak),
              last_activity_date: weekStartStr,
              is_active: true,
            })
            .eq('id', existingStreak.id);

          await this.checkStreakMilestones(userId, 'weekly_event', newCurrentStreak);
        } else if (weeksDiff > 1) {
          // Streak cassé
          await supabase
            .from('user_streaks')
            .update({
              current_streak: 1,
              last_activity_date: weekStartStr,
              streak_start_date: weekStartStr,
              is_active: true,
            })
            .eq('id', existingStreak.id);
        }
      }
    } catch (error) {
      console.error('Error updating weekly event streak:', error);
    }
  }

  /**
   * Vérifie et récompense les milestones de streaks
   */
  private async checkStreakMilestones(userId: string, streakType: string, currentStreak: number): Promise<void> {
    try {
      const milestones = this.getStreakMilestones()[streakType] || [];
      
      for (const milestone of milestones) {
        if (currentStreak === milestone.days) {
          // Milestone atteint !
          await this.rewardStreakMilestone(userId, streakType, milestone);
        }
      }
    } catch (error) {
      console.error('Error checking streak milestones:', error);
    }
  }

  /**
   * Récompense un milestone de streak
   */
  private async rewardStreakMilestone(userId: string, streakType: string, milestone: StreakMilestone): Promise<void> {
    try {
      // Ajouter les points
      if (milestone.reward_points > 0) {
        await achievements.updateStats(userId, { 
          // Ajouter les points directement via une mise à jour custom
        });
      }

      // Créer un badge spécial pour le streak
      if (milestone.reward_type === 'badge') {
        // Créer un badge dynamique pour ce streak
        const badgeName = `streak_${streakType}_${milestone.days}`;
        
        // Vérifier si le badge existe déjà
        const { data: existingBadge } = await supabase
          .from('badges')
          .select('id')
          .eq('name', badgeName)
          .single();

        let badgeId: number;

        if (!existingBadge) {
          // Créer le badge
          const { data: newBadge } = await supabase
            .from('badges')
            .insert({
              name: badgeName,
              title: milestone.title,
              description: milestone.description,
              icon: milestone.icon,
              category: 'streak',
              rarity: milestone.days >= 30 ? 'legendary' : milestone.days >= 14 ? 'epic' : milestone.days >= 7 ? 'rare' : 'common',
              points: milestone.reward_points,
              requirements: { streak_type: streakType, days: milestone.days },
            })
            .select('id')
            .single();

          badgeId = newBadge!.id;
        } else {
          badgeId = existingBadge.id;
        }

        // Attribuer le badge à l'utilisateur
        await supabase
          .from('user_achievements')
          .upsert({
            user_id: userId,
            badge_id: badgeId,
            is_completed: true,
            completion_data: { 
              streak_type: streakType, 
              days: milestone.days,
              achieved_at: new Date().toISOString() 
            },
          });
      }

      // Notification de célébration
      showToast(
        `🔥 Streak ${milestone.title} ! ${milestone.days} jours consécutifs !`,
        { type: 'success', duration: 5000 }
      );

      // Débloquer des fonctionnalités spéciales
      if (milestone.reward_type === 'feature' && milestone.reward_data) {
        await this.unlockFeature(userId, milestone.reward_data);
      }

    } catch (error) {
      console.error('Error rewarding streak milestone:', error);
    }
  }

  /**
   * Débloque une fonctionnalité spéciale
   */
  private async unlockFeature(userId: string, featureData: Record<string, any>): Promise<void> {
    try {
      // Sauvegarder les fonctionnalités débloquées dans le profil utilisateur
      const { data: profile } = await supabase
        .from('profiles')
        .select('unlocked_features')
        .eq('id', userId)
        .single();

      const currentFeatures = profile?.unlocked_features || {};
      const updatedFeatures = { ...currentFeatures, ...featureData };

      await supabase
        .from('profiles')
        .update({ unlocked_features: updatedFeatures })
        .eq('id', userId);

      showToast(
        `🎉 Nouvelle fonctionnalité débloquée : ${featureData.unlock}!`,
        { type: 'success', duration: 5000 }
      );
    } catch (error) {
      console.error('Error unlocking feature:', error);
    }
  }

  /**
   * Récupère tous les streaks d'un utilisateur
   */
  async getUserStreaks(userId: string): Promise<UserStreak[]> {
    try {
      const cacheKey = 'user_streaks';
      const cached = await cache.get<UserStreak[]>(cacheKey, { userId });
      
      if (cached) {
        return cached;
      }

      const { data, error } = await supabase
        .from('user_streaks')
        .select('*')
        .eq('user_id', userId)
        .order('current_streak', { ascending: false });

      if (error) throw error;

      const streaks = data || [];
      await cache.set(cacheKey, streaks, { userId }, this.CACHE_TTL);
      
      return streaks;
    } catch (error) {
      console.error('Error fetching user streaks:', error);
      return [];
    }
  }

  /**
   * Enregistre l'activité quotidienne
   */
  async trackDailyActivity(userId: string, activity: Partial<HabitTracker>): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];

      // Upsert l'activité du jour
      await supabase
        .from('daily_habits')
        .upsert({
          user_id: userId,
          date: today,
          ...activity,
        });

      // Mettre à jour le streak de connexion
      await this.updateDailyLoginStreak(userId);

    } catch (error) {
      console.error('Error tracking daily activity:', error);
    }
  }

  /**
   * Récupère les habitudes des 30 derniers jours
   */
  async getHabitHistory(userId: string, days: number = 30): Promise<HabitTracker[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      const startDateStr = startDate.toISOString().split('T')[0];

      const { data, error } = await supabase
        .from('daily_habits')
        .select('*')
        .eq('user_id', userId)
        .gte('date', startDateStr)
        .order('date', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error fetching habit history:', error);
      return [];
    }
  }

  /**
   * Calcule les statistiques de streaks
   */
  calculateStreakStats(streaks: UserStreak[]): {
    totalActiveStreaks: number;
    longestStreak: number;
    currentBestStreak: number;
    streakScore: number;
  } {
    const activeStreaks = streaks.filter(s => s.is_active);
    const longestStreak = Math.max(...streaks.map(s => s.longest_streak), 0);
    const currentBestStreak = Math.max(...activeStreaks.map(s => s.current_streak), 0);
    
    // Score basé sur la combinaison de tous les streaks
    const streakScore = activeStreaks.reduce((score, streak) => {
      const multiplier = {
        daily_login: 1,
        weekly_event: 3,
        monthly_organization: 5,
        participation_streak: 2,
      }[streak.streak_type] || 1;
      
      return score + (streak.current_streak * multiplier);
    }, 0);

    return {
      totalActiveStreaks: activeStreaks.length,
      longestStreak,
      currentBestStreak,
      streakScore,
    };
  }
}

// Instance singleton
export const streaksService = new StreaksService();

// Fonctions utilitaires
export const streaks = {
  updateDailyLogin: (userId: string) => streaksService.updateDailyLoginStreak(userId),
  updateWeeklyEvent: (userId: string) => streaksService.updateWeeklyEventStreak(userId),
  getUserStreaks: (userId: string) => streaksService.getUserStreaks(userId),
  trackActivity: (userId: string, activity: Partial<HabitTracker>) => streaksService.trackDailyActivity(userId, activity),
  getHabitHistory: (userId: string, days?: number) => streaksService.getHabitHistory(userId, days),
  calculateStats: (streaks: UserStreak[]) => streaksService.calculateStreakStats(streaks),
};
