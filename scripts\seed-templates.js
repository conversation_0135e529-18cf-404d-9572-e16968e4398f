/**
 * Script pour créer les templates d'événements prédéfinis
 * À exécuter après avoir appliqué les migrations de base de données
 */

import { createClient } from '@supabase/supabase-js';

// Configuration Supabase
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Templates prédéfinis
const predefinedTemplates = [
  {
    name: "Soirée entre amis",
    description: "Une soirée décontractée à la maison avec des amis",
    icon: "🎉",
    category: "predefined",
    created_by: null,
    is_public: true,
    template_data: {
      title_template: "Soirée {theme}",
      description_template: "Une soirée sympa entre amis pour passer un bon moment ensemble",
      default_time: "19:00",
      default_duration: 4,
      suggested_location_types: ["Domicile", "Appartement", "Maison"],
      min_participants: 3,
      max_participants: 12,
      preparation_time: 3,
      tags: ["soirée", "amis", "décontracté", "maison"]
    },
    items: [
      { name: "Apéritif", category: "Boissons", estimated_cost: "€€", estimated_effort: "easy", is_essential: true, order_index: 0 },
      { name: "Chips et gâteaux apéro", category: "Nourriture", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 1 },
      { name: "Plat principal", category: "Nourriture", estimated_cost: "€€", estimated_effort: "medium", is_essential: true, order_index: 2 },
      { name: "Dessert", category: "Nourriture", estimated_cost: "€", estimated_effort: "easy", is_essential: false, order_index: 3 },
      { name: "Boissons sans alcool", category: "Boissons", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 4 },
      { name: "Playlist musicale", category: "Divertissement", estimated_cost: null, estimated_effort: "easy", is_essential: false, order_index: 5 },
      { name: "Jeux de société", category: "Divertissement", estimated_cost: null, estimated_effort: "easy", is_essential: false, order_index: 6 }
    ]
  },
  
  {
    name: "Pique-nique au parc",
    description: "Un pique-nique en plein air pour profiter du beau temps",
    icon: "🧺",
    category: "predefined",
    created_by: null,
    is_public: true,
    template_data: {
      title_template: "Pique-nique {lieu}",
      description_template: "Un moment convivial en plein air avec de la bonne nourriture",
      default_time: "12:00",
      default_duration: 3,
      suggested_location_types: ["Parc", "Jardin public", "Bord de rivière", "Plage"],
      min_participants: 2,
      max_participants: 20,
      preparation_time: 2,
      tags: ["pique-nique", "extérieur", "nature", "déjeuner"]
    },
    items: [
      { name: "Nappe de pique-nique", category: "Matériel", estimated_cost: null, estimated_effort: "easy", is_essential: true, order_index: 0 },
      { name: "Sandwichs", category: "Nourriture", estimated_cost: "€€", estimated_effort: "medium", is_essential: true, order_index: 1 },
      { name: "Salade composée", category: "Nourriture", estimated_cost: "€", estimated_effort: "medium", is_essential: false, order_index: 2 },
      { name: "Fruits frais", category: "Nourriture", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 3 },
      { name: "Boissons fraîches", category: "Boissons", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 4 },
      { name: "Glacière", category: "Matériel", estimated_cost: null, estimated_effort: "easy", is_essential: true, order_index: 5 },
      { name: "Frisbee ou ballon", category: "Divertissement", estimated_cost: null, estimated_effort: "easy", is_essential: false, order_index: 6 },
      { name: "Sacs poubelle", category: "Matériel", estimated_cost: null, estimated_effort: "easy", is_essential: true, order_index: 7 }
    ]
  },

  {
    name: "Soirée jeux vidéo",
    description: "Une soirée gaming entre passionnés",
    icon: "🎮",
    category: "predefined",
    created_by: null,
    is_public: true,
    template_data: {
      title_template: "Soirée Gaming {jeu}",
      description_template: "Une soirée dédiée aux jeux vidéo avec tournois et fun",
      default_time: "18:00",
      default_duration: 6,
      suggested_location_types: ["Domicile", "Gaming café", "Local associatif"],
      min_participants: 2,
      max_participants: 8,
      preparation_time: 1,
      tags: ["gaming", "jeux vidéo", "tournoi", "soirée"]
    },
    items: [
      { name: "Consoles et manettes", category: "Matériel", estimated_cost: null, estimated_effort: "easy", is_essential: true, order_index: 0 },
      { name: "Pizza", category: "Nourriture", estimated_cost: "€€", estimated_effort: "easy", is_essential: true, order_index: 1 },
      { name: "Snacks et bonbons", category: "Nourriture", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 2 },
      { name: "Boissons énergisantes", category: "Boissons", estimated_cost: "€", estimated_effort: "easy", is_essential: false, order_index: 3 },
      { name: "Bières", category: "Boissons", estimated_cost: "€", estimated_effort: "easy", is_essential: false, order_index: 4 },
      { name: "Écrans supplémentaires", category: "Matériel", estimated_cost: null, estimated_effort: "medium", is_essential: false, order_index: 5 }
    ]
  },

  {
    name: "Anniversaire",
    description: "Fête d'anniversaire mémorable",
    icon: "🎂",
    category: "predefined",
    created_by: null,
    is_public: true,
    template_data: {
      title_template: "Anniversaire de {nom}",
      description_template: "Célébrons ensemble cet anniversaire spécial !",
      default_time: "15:00",
      default_duration: 4,
      suggested_location_types: ["Domicile", "Restaurant", "Salle des fêtes"],
      min_participants: 5,
      max_participants: 30,
      preparation_time: 7,
      tags: ["anniversaire", "fête", "célébration", "gâteau"]
    },
    items: [
      { name: "Gâteau d'anniversaire", category: "Nourriture", estimated_cost: "€€", estimated_effort: "medium", is_essential: true, order_index: 0 },
      { name: "Bougies", category: "Décoration", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 1 },
      { name: "Décorations", category: "Décoration", estimated_cost: "€", estimated_effort: "medium", is_essential: true, order_index: 2 },
      { name: "Boissons", category: "Boissons", estimated_cost: "€€", estimated_effort: "easy", is_essential: true, order_index: 3 },
      { name: "Petits fours", category: "Nourriture", estimated_cost: "€€", estimated_effort: "medium", is_essential: false, order_index: 4 },
      { name: "Musique/Playlist", category: "Divertissement", estimated_cost: null, estimated_effort: "easy", is_essential: true, order_index: 5 },
      { name: "Appareil photo", category: "Matériel", estimated_cost: null, estimated_effort: "easy", is_essential: false, order_index: 6 }
    ]
  },

  {
    name: "Barbecue d'été",
    description: "Un barbecue convivial en extérieur",
    icon: "🔥",
    category: "predefined",
    created_by: null,
    is_public: true,
    template_data: {
      title_template: "Barbecue {occasion}",
      description_template: "Un barbecue convivial pour profiter des beaux jours",
      default_time: "18:00",
      default_duration: 4,
      suggested_location_types: ["Jardin", "Terrasse", "Parc avec barbecue"],
      min_participants: 4,
      max_participants: 15,
      preparation_time: 2,
      tags: ["barbecue", "été", "extérieur", "grillades"]
    },
    items: [
      { name: "Charbon ou gaz", category: "Matériel", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 0 },
      { name: "Viandes à griller", category: "Nourriture", estimated_cost: "€€€", estimated_effort: "easy", is_essential: true, order_index: 1 },
      { name: "Légumes pour grillades", category: "Nourriture", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 2 },
      { name: "Salades d'accompagnement", category: "Nourriture", estimated_cost: "€", estimated_effort: "medium", is_essential: true, order_index: 3 },
      { name: "Pain", category: "Nourriture", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 4 },
      { name: "Sauces et condiments", category: "Nourriture", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 5 },
      { name: "Boissons fraîches", category: "Boissons", estimated_cost: "€€", estimated_effort: "easy", is_essential: true, order_index: 6 },
      { name: "Ustensiles de barbecue", category: "Matériel", estimated_cost: null, estimated_effort: "easy", is_essential: true, order_index: 7 }
    ]
  },

  {
    name: "Soirée cinéma",
    description: "Une soirée film cocooning",
    icon: "🍿",
    category: "predefined",
    created_by: null,
    is_public: true,
    template_data: {
      title_template: "Soirée Cinéma {genre}",
      description_template: "Une soirée détente devant un bon film",
      default_time: "20:00",
      default_duration: 3,
      suggested_location_types: ["Domicile", "Cinéma", "Salle de projection"],
      min_participants: 2,
      max_participants: 10,
      preparation_time: 1,
      tags: ["cinéma", "film", "cocooning", "soirée"]
    },
    items: [
      { name: "Pop-corn", category: "Nourriture", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 0 },
      { name: "Bonbons", category: "Nourriture", estimated_cost: "€", estimated_effort: "easy", is_essential: false, order_index: 1 },
      { name: "Boissons", category: "Boissons", estimated_cost: "€", estimated_effort: "easy", is_essential: true, order_index: 2 },
      { name: "Plaids et coussins", category: "Matériel", estimated_cost: null, estimated_effort: "easy", is_essential: false, order_index: 3 },
      { name: "Sélection de films", category: "Divertissement", estimated_cost: null, estimated_effort: "easy", is_essential: true, order_index: 4 }
    ]
  }
];

async function seedTemplates() {
  console.log('🌱 Début du seeding des templates...');

  try {
    for (const templateData of predefinedTemplates) {
      console.log(`📝 Création du template: ${templateData.name}`);

      // Extraire les items
      const { items, ...template } = templateData;

      // Créer le template
      const { data: createdTemplate, error: templateError } = await supabase
        .from('event_templates')
        .insert(template)
        .select()
        .single();

      if (templateError) {
        console.error(`❌ Erreur lors de la création du template ${templateData.name}:`, templateError);
        continue;
      }

      // Créer les items du template
      if (items && items.length > 0) {
        const itemsWithTemplateId = items.map(item => ({
          ...item,
          template_id: createdTemplate.id,
          suggested_quantity: 1,
          description: null
        }));

        const { error: itemsError } = await supabase
          .from('template_items')
          .insert(itemsWithTemplateId);

        if (itemsError) {
          console.error(`❌ Erreur lors de la création des items pour ${templateData.name}:`, itemsError);
        } else {
          console.log(`✅ Template ${templateData.name} créé avec ${items.length} items`);
        }
      }
    }

    console.log('🎉 Seeding des templates terminé avec succès !');
    console.log(`📊 ${predefinedTemplates.length} templates prédéfinis créés`);

  } catch (error) {
    console.error('❌ Erreur lors du seeding:', error);
    process.exit(1);
  }
}

// Exécuter le seeding
seedTemplates();
