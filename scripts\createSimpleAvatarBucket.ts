/**
 * Script pour créer un bucket simple sans aucune restriction
 */

require('dotenv').config();
const { createClient } = require("@supabase/supabase-js");

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("❌ Variables d'environnement manquantes");
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function createSimpleAvatarBucket() {
  try {
    console.log("🪣 Création d'un bucket simple pour les avatars...");

    const bucketName = "simple-avatars";

    // 1. Supprimer le bucket s'il existe
    console.log("1. Nettoyage des buckets existants...");
    try {
      await supabaseAdmin.storage.deleteBucket(bucketName);
      console.log("✅ Ancien bucket supprimé");
    } catch (e) {
      console.log("ℹ️ Aucun ancien bucket à supprimer");
    }

    // 2. Créer un nouveau bucket avec des paramètres minimaux
    console.log("2. Création du nouveau bucket...");
    const { error: createError } = await supabaseAdmin.storage.createBucket(
      bucketName,
      {
        public: true,
        allowedMimeTypes: null, // Accepter tous les types
        fileSizeLimit: null,    // Pas de limite de taille
      }
    );

    if (createError) {
      console.error("❌ Erreur lors de la création du bucket:", createError);
      
      // Essayer avec des paramètres encore plus simples
      console.log("Tentative avec paramètres minimaux...");
      const { error: simpleCreateError } = await supabaseAdmin.storage.createBucket(bucketName);
      
      if (simpleCreateError) {
        console.error("❌ Erreur même avec paramètres minimaux:", simpleCreateError);
        return;
      }
    }

    console.log("✅ Bucket créé avec succès");

    // 3. Test d'upload immédiat
    console.log("3. Test d'upload immédiat...");
    
    const testFileName = `test_${Date.now()}.txt`;
    const testContent = "test content";
    
    const { error: uploadError } = await supabaseAdmin.storage
      .from(bucketName)
      .upload(testFileName, testContent);
      
    if (uploadError) {
      console.log("❌ Erreur d'upload de test:", uploadError);
      
      // Essayer avec un nom de fichier encore plus simple
      const simpleTestName = `${Date.now()}.txt`;
      const { error: simpleUploadError } = await supabaseAdmin.storage
        .from(bucketName)
        .upload(simpleTestName, testContent);
        
      if (simpleUploadError) {
        console.log("❌ Erreur même avec nom simple:", simpleUploadError);
        
        // Afficher des informations de diagnostic
        console.log("\n🔍 Diagnostic:");
        console.log("- Bucket créé:", bucketName);
        console.log("- Erreur upload:", simpleUploadError);
        console.log("- Utilisation de supabaseAdmin:", !!supabaseAdmin);
        
        return;
      } else {
        console.log("✅ Upload réussi avec nom simple");
        await supabaseAdmin.storage.from(bucketName).remove([simpleTestName]);
      }
    } else {
      console.log("✅ Upload de test réussi");
      await supabaseAdmin.storage.from(bucketName).remove([testFileName]);
    }

    // 4. Instructions
    console.log("\n🎯 Instructions:");
    console.log(`1. Modifiez avatarService.ts:`);
    console.log(`   private bucketName = '${bucketName}';`);
    console.log("2. Testez l'upload d'avatar");
    
    // 5. Afficher les informations du bucket
    const { data: buckets } = await supabaseAdmin.storage.listBuckets();
    const bucket = buckets?.find((b: any) => b.name === bucketName);
    
    if (bucket) {
      console.log("\n📊 Informations du bucket:");
      console.log("- Nom:", bucket.name);
      console.log("- Public:", bucket.public);
      console.log("- Créé:", bucket.created_at);
    }

    console.log("\n✅ Configuration terminée !");

  } catch (error) {
    console.error("❌ Erreur générale:", error);
  }
}

// Exécuter le script
if (require.main === module) {
  createSimpleAvatarBucket()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { createSimpleAvatarBucket };
