/**
 * Système de notifications intelligentes pour Party Organizer
 * Gère l'envoi automatique de notifications contextuelles
 */

import { 
  sendNotificationToUser, 
  sendNotificationToEventParticipants,
  scheduleLocalNotification,
  NotificationData 
} from './notificationService';
import { supabase } from './supabase';
import { Event, Participant, Item } from './types';

/**
 * Envoie une notification quand quelqu'un rejoint un événement
 */
export async function notifyParticipantJoined(
  event: Event,
  newParticipant: Participant,
  organizerId: string
) {
  try {
    const notification: NotificationData = {
      type: 'participant_joined',
      eventId: event.id,
      title: `${event.title} - Nouveau participant !`,
      body: `${newParticipant.name_display} a rejoint votre événement`,
      data: {
        participantId: newParticipant.id,
        participantName: newParticipant.name_display,
      }
    };

    // Notifier l'organisateur
    await sendNotificationToUser(organizerId, notification);

    // Notifier les autres participants (optionnel)
    const participantNotification: NotificationData = {
      type: 'participant_joined',
      eventId: event.id,
      title: `${event.title} - Nouveau participant`,
      body: `${newParticipant.name_display} a rejoint l'événement`,
      data: {
        participantId: newParticipant.id,
        participantName: newParticipant.name_display,
      }
    };

    await sendNotificationToEventParticipants(
      event.id, 
      participantNotification, 
      organizerId // Exclure l'organisateur (déjà notifié)
    );

    // Enregistrer dans l'historique
    await saveNotificationHistory(organizerId, notification);

  } catch (error) {
    console.error('Erreur lors de la notification de nouveau participant:', error);
  }
}

/**
 * Envoie une notification quand un item est ajouté
 */
export async function notifyItemAdded(
  event: Event,
  item: Item,
  addedByUserId: string
) {
  try {
    const notification: NotificationData = {
      type: 'item_added',
      eventId: event.id,
      title: `${event.title} - Nouvel item`,
      body: `"${item.name}" a été ajouté à la liste`,
      data: {
        itemId: item.id,
        itemName: item.name,
        category: item.category,
      }
    };

    // Notifier tous les participants sauf celui qui a ajouté l'item
    await sendNotificationToEventParticipants(
      event.id,
      notification,
      addedByUserId
    );

  } catch (error) {
    console.error('Erreur lors de la notification d\'ajout d\'item:', error);
  }
}

/**
 * Envoie une notification quand un coût est mis à jour
 */
export async function notifyCostUpdated(
  event: Event,
  item: Item,
  updatedByUserId: string
) {
  try {
    const notification: NotificationData = {
      type: 'cost_updated',
      eventId: event.id,
      title: `${event.title} - Coût mis à jour`,
      body: `Le coût de "${item.name}" a été mis à jour`,
      data: {
        itemId: item.id,
        itemName: item.name,
        actualCost: item.actual_cost,
      }
    };

    // Notifier tous les participants
    await sendNotificationToEventParticipants(
      event.id,
      notification,
      updatedByUserId
    );

  } catch (error) {
    console.error('Erreur lors de la notification de mise à jour de coût:', error);
  }
}

/**
 * Envoie une notification de demande de remboursement
 */
export async function notifySettlementRequest(
  event: Event,
  fromParticipant: Participant,
  toParticipant: Participant,
  amount: number
) {
  try {
    if (!toParticipant.user_id) return;

    const notification: NotificationData = {
      type: 'settlement_request',
      eventId: event.id,
      title: `${event.title} - Remboursement`,
      body: `${fromParticipant.name_display} vous demande ${amount}€`,
      data: {
        fromParticipantId: fromParticipant.id,
        fromParticipantName: fromParticipant.name_display,
        amount,
      }
    };

    await sendNotificationToUser(toParticipant.user_id, notification);

  } catch (error) {
    console.error('Erreur lors de la notification de demande de remboursement:', error);
  }
}

/**
 * Programme des rappels d'événement
 */
export async function scheduleEventReminders(event: Event) {
  try {
    const eventDate = new Date(event.date_time);
    const now = new Date();

    // Rappel 24h avant
    const reminder24h = new Date(eventDate.getTime() - 24 * 60 * 60 * 1000);
    if (reminder24h > now) {
      await scheduleLocalNotification(
        `${event.title} - Demain !`,
        `Votre événement aura lieu demain à ${eventDate.toLocaleTimeString('fr-FR', {
          hour: '2-digit',
          minute: '2-digit'
        })}`,
        reminder24h,
        {
          type: 'event_reminder',
          eventId: event.id,
          reminderType: '24h'
        }
      );
    }

    // Rappel 2h avant
    const reminder2h = new Date(eventDate.getTime() - 2 * 60 * 60 * 1000);
    if (reminder2h > now) {
      await scheduleLocalNotification(
        `${event.title} - Dans 2h !`,
        `Votre événement commence dans 2 heures`,
        reminder2h,
        {
          type: 'event_reminder',
          eventId: event.id,
          reminderType: '2h'
        }
      );
    }

    // Rappel 30min avant
    const reminder30min = new Date(eventDate.getTime() - 30 * 60 * 1000);
    if (reminder30min > now) {
      await scheduleLocalNotification(
        `${event.title} - Bientôt !`,
        `Votre événement commence dans 30 minutes`,
        reminder30min,
        {
          type: 'event_reminder',
          eventId: event.id,
          reminderType: '30min'
        }
      );
    }

  } catch (error) {
    console.error('Erreur lors de la programmation des rappels:', error);
  }
}

/**
 * Envoie un résumé financier quand tous les coûts sont ajoutés
 */
export async function notifyFinancialSummaryReady(
  event: Event,
  organizerId: string
) {
  try {
    const notification: NotificationData = {
      type: 'cost_updated',
      eventId: event.id,
      title: `${event.title} - Résumé financier prêt`,
      body: `Tous les coûts sont saisis. Consultez le résumé des remboursements.`,
      data: {
        action: 'view_financial_summary'
      }
    };

    // Notifier tous les participants
    await sendNotificationToEventParticipants(event.id, notification);

  } catch (error) {
    console.error('Erreur lors de la notification de résumé financier:', error);
  }
}

/**
 * Enregistre une notification dans l'historique
 */
async function saveNotificationHistory(
  userId: string,
  notification: NotificationData
) {
  try {
    await supabase
      .from('notification_history')
      .insert({
        user_id: userId,
        event_id: notification.eventId,
        notification_type: notification.type,
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
      });
  } catch (error) {
    console.error('Erreur lors de l\'enregistrement de l\'historique:', error);
  }
}

/**
 * Marque une notification comme lue
 */
export async function markNotificationAsRead(
  userId: string,
  notificationId: number
) {
  try {
    await supabase
      .from('notification_history')
      .update({ read_at: new Date().toISOString() })
      .eq('id', notificationId)
      .eq('user_id', userId);
  } catch (error) {
    console.error('Erreur lors du marquage comme lu:', error);
  }
}

/**
 * Marque une notification comme cliquée
 */
export async function markNotificationAsClicked(
  userId: string,
  notificationId: number
) {
  try {
    await supabase
      .from('notification_history')
      .update({ clicked_at: new Date().toISOString() })
      .eq('id', notificationId)
      .eq('user_id', userId);
  } catch (error) {
    console.error('Erreur lors du marquage comme cliqué:', error);
  }
}

/**
 * Récupère l'historique des notifications pour un utilisateur
 */
export async function getNotificationHistory(
  userId: string,
  limit: number = 50
) {
  try {
    const { data, error } = await supabase
      .from('notification_history')
      .select(`
        *,
        events(title, icon)
      `)
      .eq('user_id', userId)
      .order('sent_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'historique:', error);
    return [];
  }
}

/**
 * Nettoie les anciennes notifications (plus de 30 jours)
 */
export async function cleanupOldNotifications() {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    await supabase
      .from('notification_history')
      .delete()
      .lt('sent_at', thirtyDaysAgo.toISOString());

  } catch (error) {
    console.error('Erreur lors du nettoyage des notifications:', error);
  }
}

/**
 * Obtient les statistiques de notifications pour un utilisateur
 */
export async function getNotificationStats(userId: string) {
  try {
    const { data, error } = await supabase
      .from('notification_history')
      .select('notification_type, read_at, clicked_at')
      .eq('user_id', userId);

    if (error || !data) {
      return {
        total: 0,
        read: 0,
        clicked: 0,
        readRate: 0,
        clickRate: 0,
        byType: {}
      };
    }

    const total = data.length;
    const read = data.filter(n => n.read_at).length;
    const clicked = data.filter(n => n.clicked_at).length;

    const byType = data.reduce((acc, n) => {
      acc[n.notification_type] = (acc[n.notification_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      read,
      clicked,
      readRate: total > 0 ? (read / total) * 100 : 0,
      clickRate: total > 0 ? (clicked / total) * 100 : 0,
      byType
    };
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    return {
      total: 0,
      read: 0,
      clicked: 0,
      readRate: 0,
      clickRate: 0,
      byType: {}
    };
  }
}
