import React from "react";
import { useRouter } from "expo-router";
import { OnboardingCompletionScreen } from "~/components/onboarding/OnboardingComponents";
import { useOnboarding } from "~/hooks/useOnboarding";

export default function CompletionScreen() {
  const router = useRouter();
  const { progress, completeStep } = useOnboarding();

  const handleFinish = async () => {
    await completeStep("completion");
    router.replace("/(tabs)/");
  };

  const handleViewDashboard = async () => {
    await completeStep("completion");
    router.replace("/(tabs)/dashboard");
  };

  return (
    <OnboardingCompletionScreen
      onFinish={handleFinish}
      onViewDashboard={handleViewDashboard}
      completedSteps={progress.completedSteps}
      totalSteps={progress.totalSteps}
    />
  );
}
