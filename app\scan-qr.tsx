import React, { useState } from "react";
import { View, Platform } from "react-native";
import { useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { QRCodeScanner } from "~/components/QRCodeScanner";
import { showToast } from "~/lib/toastService";

export default function ScanQRScreen() {
  const router = useRouter();
  const [isScanning, setIsScanning] = useState(true);

  const handleQRCodeScanned = (data: string) => {
    try {
      // Extraire l'ID de l'événement depuis l'URL
      const match = data.match(/\/join\/(\d+)/);
      if (match) {
        const eventId = match[1];
        showToast("QR Code scanné avec succès !", { type: "success" });
        router.replace(`/join/${eventId}`);
      } else {
        showToast("QR Code non reconnu comme un lien d'événement", { type: "error" });
        setIsScanning(false);
      }
    } catch (error) {
      console.error("Error processing scanned data:", error);
      showToast("Erreur lors du traitement du QR code", { type: "error" });
      setIsScanning(false);
    }
  };

  const handleCloseScanner = () => {
    router.back();
  };

  const handleManualEntry = () => {
    router.replace("/join-event");
  };

  if (!isScanning) {
    return (
      <View className="flex-1 bg-background">
        <Card className="m-4">
          <CardHeader>
            <CardTitle className="text-center">Scanner fermé</CardTitle>
          </CardHeader>
          <CardContent className="items-center gap-4">
            <Text className="text-6xl">📱</Text>
            <Text className="text-muted-foreground text-center">
              Le scanner a été fermé. Vous pouvez réessayer ou saisir manuellement le lien.
            </Text>
            <View className="flex-row gap-3">
              <Button variant="outline" onPress={() => setIsScanning(true)}>
                <Text>Réessayer</Text>
              </Button>
              <Button onPress={handleManualEntry}>
                <Text>Saisie manuelle</Text>
              </Button>
            </View>
          </CardContent>
        </Card>
      </View>
    );
  }

  return (
    <QRCodeScanner
      onScan={handleQRCodeScanned}
      onClose={handleCloseScanner}
      title="Scanner un QR Code d'événement"
      description="Pointez votre caméra vers le QR code partagé par l'organisateur"
    />
  );
}
