import React, { useState } from "react";
import { View, ScrollView, Platform, Alert, Modal } from "react-native";
import { useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { showToast } from "~/lib/toastService";
import { QRCodeScanner } from "~/components/QRCodeScanner";

export default function JoinEventScreen() {
  const router = useRouter();
  const [eventLink, setEventLink] = useState("");
  const [loading, setLoading] = useState(false);
  const [showScanner, setShowScanner] = useState(false);

  const extractEventIdFromLink = (link: string): string | null => {
    try {
      // Nettoyer le lien
      const cleanLink = link.trim();

      // Patterns possibles pour les liens d'événement
      const patterns = [
        /\/join\/(\d+)$/, // /join/123
        /\/event\/(\d+)$/, // /event/123
        /event[_-]?id[=:](\d+)/i, // event_id=123 ou eventId:123
        /id[=:](\d+)/i, // id=123 ou id:123
        /(\d+)$/, // Juste un nombre à la fin
      ];

      for (const pattern of patterns) {
        const match = cleanLink.match(pattern);
        if (match && match[1]) {
          return match[1];
        }
      }

      // Si c'est juste un nombre
      if (/^\d+$/.test(cleanLink)) {
        return cleanLink;
      }

      return null;
    } catch (error) {
      console.error("Error extracting event ID:", error);
      return null;
    }
  };

  const handleJoinEvent = async () => {
    if (!eventLink.trim()) {
      showToast("Veuillez saisir un lien d'événement", { type: "error" });
      return;
    }

    setLoading(true);

    try {
      const eventId = extractEventIdFromLink(eventLink);

      if (!eventId) {
        showToast("Lien d'événement invalide", { type: "error" });
        return;
      }

      // Rediriger vers la page de jointure
      router.push(`/join/${eventId}`);
    } catch (error) {
      console.error("Error joining event:", error);
      showToast("Erreur lors de la jointure à l'événement", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const handlePasteFromClipboard = async () => {
    if (Platform.OS === "web") {
      try {
        const text = await navigator.clipboard.readText();
        setEventLink(text);
        showToast("Lien collé depuis le presse-papiers", { type: "success" });
      } catch (error) {
        showToast("Impossible de lire le presse-papiers", { type: "error" });
      }
    } else {
      // Sur mobile, on peut utiliser Clipboard de React Native
      showToast("Collez manuellement le lien dans le champ", { type: "info" });
    }
  };

  const handleExampleLinks = () => {
    Alert.alert(
      "Exemples de liens valides",
      "• http://localhost:8082/join/123\n" +
        "• https://party-organizer.app/join/456\n" +
        "• /join/789\n" +
        "• 123 (juste l'ID)",
      [{ text: "OK" }]
    );
  };

  const handleStartScanner = () => {
    setShowScanner(true);
  };

  const handleCloseScanner = () => {
    setShowScanner(false);
  };

  const handleQRCodeScanned = (data: string) => {
    setEventLink(data);
    setShowScanner(false);
    showToast("QR Code scanné avec succès !", { type: "success" });
    // Optionnellement, rejoindre automatiquement l'événement
    // handleJoinEvent();
  };

  return (
    <ScrollView className="flex-1 bg-background">
      <View className={Platform.OS === "web" ? "max-w-md mx-auto p-6" : "p-4"}>
        {/* En-tête */}
        <View className="items-center mb-8 mt-8">
          <View className="w-20 h-20 bg-primary/10 rounded-full items-center justify-center mb-4">
            <Text className="text-4xl">🔗</Text>
          </View>
          <Text className="text-2xl font-bold text-center mb-2">
            Rejoindre un événement
          </Text>
          <Text className="text-muted-foreground text-center">
            Scannez un QR code ou collez le lien de l'événement ci-dessous.
          </Text>
        </View>

        {/* Formulaire de saisie */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Lien d'invitation</CardTitle>
          </CardHeader>
          <CardContent className="gap-4">
            <View>
              <Label htmlFor="event-link">
                Collez le lien de l'événement ici
              </Label>
              <Input
                id="event-link"
                placeholder="https://party-organizer.app/join/123"
                value={eventLink}
                onChangeText={setEventLink}
                className="mt-2"
                autoCapitalize="none"
                autoCorrect={false}
                keyboardType={Platform.OS === "web" ? "default" : "url"}
              />
            </View>

            {/* Boutons d'action */}
            <View className="gap-3">
              <Button
                onPress={handleJoinEvent}
                disabled={loading}
                className="h-12"
              >
                <Text className="text-primary-foreground font-medium">
                  {loading ? "Vérification..." : "🚀 Rejoindre"}
                </Text>
              </Button>

              {/* Scanner QR Code */}
              <Button
                variant="outline"
                onPress={handleStartScanner}
                disabled={loading}
                className="h-12 bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800"
              >
                <Text className="text-blue-600 dark:text-blue-400 font-medium">
                  📱 Scanner un QR Code
                </Text>
              </Button>

              <View className="flex-row gap-3">
                <Button
                  variant="outline"
                  onPress={handlePasteFromClipboard}
                  className="flex-1 h-12"
                  disabled={loading}
                >
                  <Text>📋 Coller</Text>
                </Button>

                <Button
                  variant="outline"
                  onPress={handleExampleLinks}
                  className="h-12 px-4"
                  disabled={loading}
                >
                  <Text>❓</Text>
                </Button>
              </View>
            </View>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-base">Comment ça marche ?</CardTitle>
          </CardHeader>
          <CardContent className="gap-3">
            <View className="flex-row items-start gap-3">
              <Text className="text-primary font-bold">1.</Text>
              <Text className="text-sm text-muted-foreground flex-1">
                L'organisateur vous envoie un lien d'invitation
              </Text>
            </View>

            <View className="flex-row items-start gap-3">
              <Text className="text-primary font-bold">2.</Text>
              <Text className="text-sm text-muted-foreground flex-1">
                Collez ce lien dans le champ ci-dessus
              </Text>
            </View>

            <View className="flex-row items-start gap-3">
              <Text className="text-primary font-bold">3.</Text>
              <Text className="text-sm text-muted-foreground flex-1">
                Choisissez votre identité dans la liste des participants
              </Text>
            </View>

            <View className="flex-row items-start gap-3">
              <Text className="text-primary font-bold">4.</Text>
              <Text className="text-sm text-muted-foreground flex-1">
                Participez à l'organisation de l'événement !
              </Text>
            </View>
          </CardContent>
        </Card>

        {/* Exemples de liens */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-base">Formats acceptés</CardTitle>
          </CardHeader>
          <CardContent className="gap-2">
            <Text className="text-xs text-muted-foreground">
              • Lien complet : https://party-organizer.app/join/123
            </Text>
            <Text className="text-xs text-muted-foreground">
              • Lien local : http://localhost:8082/join/123
            </Text>
            <Text className="text-xs text-muted-foreground">
              • Lien relatif : /join/123
            </Text>
            <Text className="text-xs text-muted-foreground">
              • ID seulement : 123
            </Text>
          </CardContent>
        </Card>

        {/* Actions alternatives */}
        <View className="gap-3 mb-8">
          <Button
            variant="ghost"
            onPress={() => router.push("/(tabs)")}
            className="h-12"
          >
            <Text className="text-muted-foreground">🏠 Retour à l'accueil</Text>
          </Button>

          <Button
            variant="ghost"
            onPress={() => router.push("/event/create")}
            className="h-12"
          >
            <Text className="text-muted-foreground">
              ➕ Créer un nouvel événement
            </Text>
          </Button>
        </View>
      </View>

      {/* Modal Scanner QR Code */}
      <Modal
        visible={showScanner}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={handleCloseScanner}
      >
        <QRCodeScanner
          onScan={handleQRCodeScanned}
          onClose={handleCloseScanner}
          title="Scanner le QR Code de l'événement"
          description="Pointez votre caméra vers le QR code partagé par l'organisateur"
        />
      </Modal>
    </ScrollView>
  );
}
