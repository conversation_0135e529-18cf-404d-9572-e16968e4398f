/**
 * <PERSON>ript pour corriger définitivement les politiques RLS des avatars
 */

require('dotenv').config();
const { createClient } = require("@supabase/supabase-js");

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("❌ Variables d'environnement manquantes");
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function fixAvatarRLS() {
  try {
    console.log("🔧 Correction des politiques RLS pour les avatars...\n");

    // 1. Supprimer toutes les anciennes politiques
    console.log("1. Suppression des anciennes politiques...");
    
    const dropQueries = [
      `DROP POLICY IF EXISTS "Public read access for avatars" ON storage.objects;`,
      `DROP POLICY IF EXISTS "Authenticated users can upload avatars" ON storage.objects;`,
      `DROP POLICY IF EXISTS "Users can update their own avatars" ON storage.objects;`,
      `DROP POLICY IF EXISTS "Users can delete their own avatars" ON storage.objects;`,
    ];

    for (const query of dropQueries) {
      try {
        const { error } = await supabaseAdmin.rpc('exec_sql', { sql: query });
        if (error && !error.message.includes('does not exist')) {
          console.warn(`Avertissement:`, error.message);
        }
      } catch (e) {
        // Ignorer les erreurs de suppression
      }
    }

    // 2. Créer les nouvelles politiques corrigées
    console.log("2. Création des nouvelles politiques...");

    const policies = [
      {
        name: "Lecture publique des avatars",
        sql: `CREATE POLICY "Public read access for avatars" ON storage.objects
              FOR SELECT USING (bucket_id = 'avatars');`
      },
      {
        name: "Upload pour utilisateurs authentifiés",
        sql: `CREATE POLICY "Authenticated users can upload avatars" ON storage.objects
              FOR INSERT WITH CHECK (
                bucket_id = 'avatars' 
                AND auth.role() = 'authenticated'
                AND (storage.foldername(name))[1] = auth.uid()::text
              );`
      },
      {
        name: "Mise à jour de ses propres avatars",
        sql: `CREATE POLICY "Users can update their own avatars" ON storage.objects
              FOR UPDATE USING (
                bucket_id = 'avatars' 
                AND auth.role() = 'authenticated'
                AND (storage.foldername(name))[1] = auth.uid()::text
              );`
      },
      {
        name: "Suppression de ses propres avatars",
        sql: `CREATE POLICY "Users can delete their own avatars" ON storage.objects
              FOR DELETE USING (
                bucket_id = 'avatars' 
                AND auth.role() = 'authenticated'
                AND (storage.foldername(name))[1] = auth.uid()::text
              );`
      }
    ];

    // Utiliser des requêtes SQL directes
    const allPoliciesSQL = policies.map(p => p.sql).join('\n\n');
    
    console.log("Exécution des politiques via SQL direct...");
    console.log("SQL à exécuter dans Supabase Dashboard:");
    console.log("=====================================");
    console.log(allPoliciesSQL);
    console.log("=====================================");

    // 3. Test d'upload simple
    console.log("\n3. Test d'upload...");
    
    // Créer un utilisateur de test
    const testUserId = "test-user-123";
    const testFileName = `${testUserId}/avatar_test.txt`;
    const testContent = "test content";
    
    try {
      const { error: uploadError } = await supabaseAdmin.storage
        .from('avatars')
        .upload(testFileName, testContent, { upsert: true });
        
      if (uploadError) {
        console.log("❌ Erreur d'upload de test:", uploadError);
      } else {
        console.log("✅ Upload de test réussi");
        
        // Nettoyer
        await supabaseAdmin.storage.from('avatars').remove([testFileName]);
        console.log("✅ Fichier de test nettoyé");
      }
    } catch (error) {
      console.log("❌ Erreur lors du test:", error);
    }

    console.log("\n🎯 Instructions finales:");
    console.log("1. Copiez le SQL ci-dessus");
    console.log("2. Allez sur: https://supabase.com/dashboard");
    console.log("3. SQL Editor → Nouvelle requête");
    console.log("4. Collez et exécutez le SQL");
    console.log("5. Testez l'upload d'avatar dans l'application");

  } catch (error) {
    console.error("❌ Erreur:", error);
  }
}

// Exécuter le script
if (require.main === module) {
  fixAvatarRLS()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { fixAvatarRLS };
