/**
 * Hook personnalisé pour gérer les streaks
 * Fournit l'état et les actions pour le système de streaks
 */

import { useState, useEffect, useCallback } from 'react';
import { streaks, UserStreak, HabitTracker } from '~/lib/streaksService';
import { useAuth } from '~/lib/AuthContext';
import { showToast } from '~/lib/toastService';

export interface StreaksState {
  userStreaks: UserStreak[];
  habitHistory: HabitTracker[];
  loading: boolean;
  error: string | null;
  streakStats: {
    totalActiveStreaks: number;
    longestStreak: number;
    currentBestStreak: number;
    streakScore: number;
  };
}

export interface StreaksActions {
  loadStreaks: () => Promise<void>;
  updateDailyLogin: () => Promise<void>;
  trackActivity: (activity: Partial<HabitTracker>) => Promise<void>;
  refreshStreaks: () => Promise<void>;
}

export function useStreaks(): StreaksState & StreaksActions {
  const { session } = useAuth();
  const userId = session?.user?.id;

  const [state, setState] = useState<StreaksState>({
    userStreaks: [],
    habitHistory: [],
    loading: true,
    error: null,
    streakStats: {
      totalActiveStreaks: 0,
      longestStreak: 0,
      currentBestStreak: 0,
      streakScore: 0,
    },
  });

  /**
   * Charge les streaks de l'utilisateur
   */
  const loadStreaks = useCallback(async () => {
    if (!userId) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const [userStreaks, habitHistory] = await Promise.all([
        streaks.getUserStreaks(userId),
        streaks.getHabitHistory(userId, 30),
      ]);

      const streakStats = streaks.calculateStats(userStreaks);

      setState({
        userStreaks,
        habitHistory,
        loading: false,
        error: null,
        streakStats,
      });
    } catch (error) {
      console.error('Error loading streaks:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Erreur lors du chargement des streaks',
      }));
    }
  }, [userId]);

  /**
   * Met à jour le streak de connexion quotidienne
   */
  const updateDailyLogin = useCallback(async () => {
    if (!userId) return;

    try {
      const updatedStreak = await streaks.updateDailyLogin(userId);
      
      if (updatedStreak) {
        // Recharger les streaks pour refléter les changements
        await loadStreaks();
        
        // Afficher une notification si c'est un nouveau record
        if (updatedStreak.current_streak > updatedStreak.longest_streak - 1) {
          showToast(
            `🔥 Nouveau record ! ${updatedStreak.current_streak} jours consécutifs !`,
            { type: 'success', duration: 3000 }
          );
        }
      }
    } catch (error) {
      console.error('Error updating daily login streak:', error);
    }
  }, [userId, loadStreaks]);

  /**
   * Enregistre une activité
   */
  const trackActivity = useCallback(async (activity: Partial<HabitTracker>) => {
    if (!userId) return;

    try {
      await streaks.trackActivity(userId, activity);
      
      // Recharger les données
      await loadStreaks();
    } catch (error) {
      console.error('Error tracking activity:', error);
    }
  }, [userId, loadStreaks]);

  /**
   * Actualise les streaks
   */
  const refreshStreaks = useCallback(async () => {
    await loadStreaks();
  }, [loadStreaks]);

  // Charger les streaks au montage et quand l'utilisateur change
  useEffect(() => {
    loadStreaks();
  }, [loadStreaks]);

  // Mettre à jour le streak de connexion quotidienne au montage
  useEffect(() => {
    if (userId) {
      updateDailyLogin();
    }
  }, [userId, updateDailyLogin]);

  return {
    ...state,
    loadStreaks,
    updateDailyLogin,
    trackActivity,
    refreshStreaks,
  };
}

/**
 * Hook simplifié pour obtenir juste les streaks actifs
 */
export function useActiveStreaks() {
  const { userStreaks, loading, error } = useStreaks();
  
  const activeStreaks = userStreaks.filter(s => s.is_active && s.current_streak > 0);
  const hasActiveStreaks = activeStreaks.length > 0;
  const bestStreak = activeStreaks.reduce((best, current) => 
    current.current_streak > best ? current.current_streak : best, 0
  );

  return {
    activeStreaks,
    hasActiveStreaks,
    bestStreak,
    loading,
    error,
  };
}

/**
 * Hook pour les habitudes quotidiennes
 */
export function useDailyHabits() {
  const { habitHistory, trackActivity, loading } = useStreaks();
  
  const today = new Date().toISOString().split('T')[0];
  const todayHabits = habitHistory.find(h => h.date === today);
  
  const weeklyStats = habitHistory.slice(0, 7).reduce((stats, day) => ({
    events_created: stats.events_created + day.events_created,
    events_participated: stats.events_participated + day.events_participated,
    items_managed: stats.items_managed + day.items_managed,
    participants_invited: stats.participants_invited + day.participants_invited,
    app_opens: stats.app_opens + day.app_opens,
    time_spent_minutes: stats.time_spent_minutes + day.time_spent_minutes,
    achievements_unlocked: stats.achievements_unlocked + day.achievements_unlocked,
  }), {
    events_created: 0,
    events_participated: 0,
    items_managed: 0,
    participants_invited: 0,
    app_opens: 0,
    time_spent_minutes: 0,
    achievements_unlocked: 0,
  });

  const logActivity = async (activity: Partial<HabitTracker>) => {
    await trackActivity(activity);
  };

  return {
    todayHabits,
    weeklyStats,
    habitHistory,
    logActivity,
    loading,
  };
}

/**
 * Hook pour les milestones de streaks
 */
export function useStreakMilestones() {
  const { userStreaks } = useStreaks();
  
  const milestones = [
    { days: 3, title: 'Régulier', icon: '🔥', reward: '50 points' },
    { days: 7, title: 'Assidu', icon: '⚡', reward: 'Badge spécial' },
    { days: 14, title: 'Dévoué', icon: '💎', reward: '300 points' },
    { days: 30, title: 'Légendaire', icon: '👑', reward: 'Thèmes premium' },
    { days: 60, title: 'Immortel', icon: '🌟', reward: '1000 points' },
    { days: 100, title: 'Centenaire', icon: '🏆', reward: 'Profil doré' },
  ];

  const getNextMilestone = (streakType: string, currentStreak: number) => {
    return milestones.find(m => m.days > currentStreak);
  };

  const getProgress = (streakType: string, currentStreak: number) => {
    const nextMilestone = getNextMilestone(streakType, currentStreak);
    if (!nextMilestone) return { progress: 100, nextMilestone: null };
    
    const previousMilestone = milestones
      .filter(m => m.days <= currentStreak)
      .pop();
    
    const start = previousMilestone?.days || 0;
    const end = nextMilestone.days;
    const progress = ((currentStreak - start) / (end - start)) * 100;
    
    return { progress: Math.max(0, Math.min(100, progress)), nextMilestone };
  };

  return {
    milestones,
    getNextMilestone,
    getProgress,
    userStreaks,
  };
}
