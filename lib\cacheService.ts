/**
 * Service de cache intelligent pour Party Organizer
 * Gère le stockage local, TTL et synchronisation
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Types pour le cache
export interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live en millisecondes
  version: number;
  lastSync?: number;
}

export interface CacheConfig {
  ttl: number; // TTL par défaut en millisecondes
  maxSize: number; // Taille max du cache en MB
  syncOnExpiry: boolean; // Synchroniser automatiquement quand expiré
}

// Configuration par défaut
const DEFAULT_CONFIG: CacheConfig = {
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 50, // 50 MB
  syncOnExpiry: true,
};

// Clés de cache prédéfinies
export const CACHE_KEYS = {
  EVENTS: 'events',
  EVENT_DETAILS: 'event_details',
  PARTICIPANTS: 'participants',
  ITEMS: 'items',
  TEMPLATES: 'templates',
  NOTIFICATIONS: 'notifications',
  USER_PROFILE: 'user_profile',
  FINANCIAL_SUMMARIES: 'financial_summaries',
} as const;

// TTL spécifiques par type de données
const TTL_CONFIG = {
  [CACHE_KEYS.EVENTS]: 2 * 60 * 1000, // 2 minutes
  [CACHE_KEYS.EVENT_DETAILS]: 5 * 60 * 1000, // 5 minutes
  [CACHE_KEYS.PARTICIPANTS]: 1 * 60 * 1000, // 1 minute
  [CACHE_KEYS.ITEMS]: 30 * 1000, // 30 secondes
  [CACHE_KEYS.TEMPLATES]: 30 * 60 * 1000, // 30 minutes
  [CACHE_KEYS.NOTIFICATIONS]: 1 * 60 * 1000, // 1 minute
  [CACHE_KEYS.USER_PROFILE]: 10 * 60 * 1000, // 10 minutes
  [CACHE_KEYS.FINANCIAL_SUMMARIES]: 2 * 60 * 1000, // 2 minutes
};

class CacheService {
  private config: CacheConfig;
  private memoryCache: Map<string, CacheItem> = new Map();

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Génère une clé de cache unique
   */
  private generateKey(key: string, params?: Record<string, any>): string {
    if (!params) return key;
    const paramString = Object.keys(params)
      .sort()
      .map(k => `${k}:${params[k]}`)
      .join('|');
    return `${key}:${paramString}`;
  }

  /**
   * Vérifie si un item de cache est valide
   */
  private isValid(item: CacheItem): boolean {
    const now = Date.now();
    return (now - item.timestamp) < item.ttl;
  }

  /**
   * Stocke des données dans le cache
   */
  async set<T>(
    key: string, 
    data: T, 
    params?: Record<string, any>,
    customTtl?: number
  ): Promise<void> {
    try {
      const cacheKey = this.generateKey(key, params);
      const ttl = customTtl || TTL_CONFIG[key as keyof typeof TTL_CONFIG] || this.config.ttl;
      
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        ttl,
        version: 1,
        lastSync: Date.now(),
      };

      // Stocker en mémoire
      this.memoryCache.set(cacheKey, cacheItem);

      // Stocker de manière persistante
      await AsyncStorage.setItem(
        `cache:${cacheKey}`,
        JSON.stringify(cacheItem)
      );

      console.log(`Cache set: ${cacheKey} (TTL: ${ttl}ms)`);
    } catch (error) {
      console.error('Error setting cache:', error);
    }
  }

  /**
   * Récupère des données du cache
   */
  async get<T>(
    key: string, 
    params?: Record<string, any>
  ): Promise<T | null> {
    try {
      const cacheKey = this.generateKey(key, params);

      // Vérifier d'abord en mémoire
      let cacheItem = this.memoryCache.get(cacheKey);

      // Si pas en mémoire, vérifier le stockage persistant
      if (!cacheItem) {
        const stored = await AsyncStorage.getItem(`cache:${cacheKey}`);
        if (stored) {
          cacheItem = JSON.parse(stored);
          // Remettre en mémoire
          if (cacheItem) {
            this.memoryCache.set(cacheKey, cacheItem);
          }
        }
      }

      if (!cacheItem) {
        console.log(`Cache miss: ${cacheKey}`);
        return null;
      }

      // Vérifier la validité
      if (!this.isValid(cacheItem)) {
        console.log(`Cache expired: ${cacheKey}`);
        await this.delete(key, params);
        return null;
      }

      console.log(`Cache hit: ${cacheKey}`);
      return cacheItem.data as T;
    } catch (error) {
      console.error('Error getting cache:', error);
      return null;
    }
  }

  /**
   * Supprime des données du cache
   */
  async delete(key: string, params?: Record<string, any>): Promise<void> {
    try {
      const cacheKey = this.generateKey(key, params);
      
      // Supprimer de la mémoire
      this.memoryCache.delete(cacheKey);
      
      // Supprimer du stockage persistant
      await AsyncStorage.removeItem(`cache:${cacheKey}`);
      
      console.log(`Cache deleted: ${cacheKey}`);
    } catch (error) {
      console.error('Error deleting cache:', error);
    }
  }

  /**
   * Invalide tout le cache d'un type
   */
  async invalidate(keyPattern: string): Promise<void> {
    try {
      // Invalider la mémoire
      for (const [key] of this.memoryCache) {
        if (key.startsWith(keyPattern)) {
          this.memoryCache.delete(key);
        }
      }

      // Invalider le stockage persistant
      const allKeys = await AsyncStorage.getAllKeys();
      const keysToDelete = allKeys.filter(key => 
        key.startsWith(`cache:${keyPattern}`)
      );
      
      if (keysToDelete.length > 0) {
        await AsyncStorage.multiRemove(keysToDelete);
      }

      console.log(`Cache invalidated: ${keyPattern} (${keysToDelete.length} items)`);
    } catch (error) {
      console.error('Error invalidating cache:', error);
    }
  }

  /**
   * Nettoie le cache expiré
   */
  async cleanup(): Promise<void> {
    try {
      const now = Date.now();
      const keysToDelete: string[] = [];

      // Nettoyer la mémoire
      for (const [key, item] of this.memoryCache) {
        if (!this.isValid(item)) {
          this.memoryCache.delete(key);
          keysToDelete.push(`cache:${key}`);
        }
      }

      // Nettoyer le stockage persistant
      if (keysToDelete.length > 0) {
        await AsyncStorage.multiRemove(keysToDelete);
      }

      console.log(`Cache cleanup: ${keysToDelete.length} expired items removed`);
    } catch (error) {
      console.error('Error cleaning cache:', error);
    }
  }

  /**
   * Obtient les statistiques du cache
   */
  async getStats(): Promise<{
    memoryItems: number;
    persistentItems: number;
    totalSize: number;
    hitRate: number;
  }> {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const cacheKeys = allKeys.filter(key => key.startsWith('cache:'));
      
      // Calculer la taille approximative
      let totalSize = 0;
      for (const key of cacheKeys) {
        const item = await AsyncStorage.getItem(key);
        if (item) {
          totalSize += new Blob([item]).size;
        }
      }

      return {
        memoryItems: this.memoryCache.size,
        persistentItems: cacheKeys.length,
        totalSize: totalSize / (1024 * 1024), // En MB
        hitRate: 0, // À implémenter avec des compteurs
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return {
        memoryItems: 0,
        persistentItems: 0,
        totalSize: 0,
        hitRate: 0,
      };
    }
  }

  /**
   * Vide complètement le cache
   */
  async clear(): Promise<void> {
    try {
      // Vider la mémoire
      this.memoryCache.clear();

      // Vider le stockage persistant
      const allKeys = await AsyncStorage.getAllKeys();
      const cacheKeys = allKeys.filter(key => key.startsWith('cache:'));
      
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }

      console.log(`Cache cleared: ${cacheKeys.length} items removed`);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  /**
   * Précharge des données dans le cache
   */
  async preload<T>(
    key: string,
    dataLoader: () => Promise<T>,
    params?: Record<string, any>
  ): Promise<T | null> {
    try {
      // Vérifier si déjà en cache
      const cached = await this.get<T>(key, params);
      if (cached) {
        return cached;
      }

      // Charger et mettre en cache
      const data = await dataLoader();
      await this.set(key, data, params);
      return data;
    } catch (error) {
      console.error('Error preloading cache:', error);
      return null;
    }
  }
}

// Instance singleton
export const cacheService = new CacheService();

// Fonctions utilitaires
export const cache = {
  set: cacheService.set.bind(cacheService),
  get: cacheService.get.bind(cacheService),
  delete: cacheService.delete.bind(cacheService),
  invalidate: cacheService.invalidate.bind(cacheService),
  cleanup: cacheService.cleanup.bind(cacheService),
  clear: cacheService.clear.bind(cacheService),
  preload: cacheService.preload.bind(cacheService),
  getStats: cacheService.getStats.bind(cacheService),
};
