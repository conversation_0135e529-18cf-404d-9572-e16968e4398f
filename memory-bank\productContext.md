# Product Context: Party Organizer

## Pourquoi ce projet existe-t-il ?

Party Organizer existe pour **révolutionner l'organisation d'événements sociaux**. L'objectif est de transformer ce qui est souvent perçu comme une corvée stressante en une **expérience collaborative, équitable, et intuitive**. En s'appuyant sur une assistance proactive de l'intelligence artificielle, l'application vise à éliminer les tracas logistiques, permettant ainsi aux utilisateurs de se concentrer sur la création de moments mémorables avec leurs proches. Il s'agit de rendre l'organisation d'événements non seulement plus simple, mais aussi plus agréable et participative.

## Comment ça devrait fonctionner ? (Fonctionnalités Détaillées)

Party Organizer est une application multiplateforme (mobile iOS/Android et Web via Expo) conçue pour simplifier radicalement et fluidifier l'organisation de tout type d'événement social.

### I. Gestion d'Événements Intelligente et Personnalisée :

1.  **Création d'Événement Guidée et Assistée :**
    - **Informations Essentielles :** Saisie facile du nom de l'événement, date, heure, lieu précis (avec suggestion d'autocomplétion ou intégration carte optionnelle), et une description détaillée.
    - **Icône Personnalisable :** Choix d'un emoji ou d'une icône thématique.
    - **Types d'Événements et Modèles IA :** Catégorisation (ex: "Barbecue", "Anniversaire"). L'IA propose suggestions d'items et modèles d'événements pré-remplis.
    - **Options Initiales :**
      - Autoriser les suggestions d'items par les invités (Switch On/Off).
      - Activer la pré-attribution ('fixer') des items (Switch On/Off).
      - Définir un budget cible par participant/couple (Optionnel).
2.  **Tableau de Bord des Événements :**
    - Vue claire des événements "À venir" et "Passés" (cartes interactives).
    - Infos clés sur la carte : icône, titre, date/heure, résumé.
    - Distinction visuelle pour événements passés.
3.  **Modification et Duplication d'Événements :**
    - Modification de tous les détails par l'organisateur.
    - Duplication d'événements passés.
4.  **Suggestions Proactives d'Événements (IA - Vision Future) :**
    - Apprentissage des habitudes pour suggérer des événements récurrents.

### II. Coordination Intelligente des Contributions ("Items") :

1.  **Création et Gestion de la Liste d'Items :**
    - Ajout manuel par l'organisateur.
    - Suggestions contextuelles par IA (basées sur type d'événement, historique).
    - Catégorisation (Apéritif, Boissons, Plats, etc.) et catégories personnalisées.
2.  **Valorisation des Contributions :**
    - Estimation du Coût (€ Faible, €€ Moyen, €€€ Élevé).
    - Estimation de la Pénibilité/Effort (Niveau 1: Achat simple, 2: Petite préparation, 3: Fait maison).
3.  **Interaction des Invités avec la Liste d'Items :**
    - Suggestions d'items par les invités (si activé, avec approbation organisateur).
    - Pré-attribution ("Fixer") d'items (si activé).
    - Déclaration d'items "Hors Liste".

### III. Gestion des Participants et Intelligence Sociale :

1.  **Invitations et Suivi :**
    - Envoi de liens d'invitation uniques. Future intégration contacts téléphone/carnet d'adresses interne.
    - Suggestions d'Invités par IA pour groupes récurrents.
    - Suivi des réponses (Accepté, Refusé, En attente).
2.  **Rôles et Statuts des Participants :**
    - Organisateur(s) vs. Invité(s) avec permissions distinctes.
    - Statut Invité (Solo/Couple) pour équilibrage.
3.  **Délégation pour l'Organisateur :**
    - Option pour l'organisateur de ne pas participer aux contributions.

### IV. Algorithme de Répartition Équitable et Personnalisable :

1.  **Déclenchement par l'Organisateur.**
2.  **Critères de Répartition :** Coût, effort, statut invité (solo/couple), items fixés, budget cible.
3.  **Processus de Répartition :**
    - Répartition Automatique visant l'équité (coût + effort).
    - Assignation Dirigée (manuelle) par l'organisateur.
    - Suggestions d'Assignation par l'IA (Vision Future) basées sur historique/préférences.
4.  **Communication des Attributions :** Notifications claires aux invités.
5.  **Gestion des Refus (Optionnel) :** Signalement par l'invité, gestion par l'organisateur.

### V. Fonctionnalités Annexes pour une Expérience Complète :

1.  **Onglet "Mes Tâches" :** Vue centralisée des items attribués à l'utilisateur pour ses événements. Marquer comme "Préparée"/"Achetée".
2.  **Onglet "Contacts" (Évolutif) :** Carnet d'adresses interne, import contacts téléphone, groupes de contacts.
3.  **Onglet "Messages" (Évolutif) :** Messagerie de groupe par événement. (Note: Actuellement désactivé, mais prévu).
4.  **Onglet "Profil" :** Gestion du compte, préférences, accès abonnements.

### VI. Modèle d'Utilisation et de Monétisation (Freemium) :

1.  **Accès Invité (Sans Compte) :** Consultation via lien unique.
2.  **Organisateur Gratuit (Compte Requis) :** Fonctionnalités de base avec limitations (nb événements actifs, nb participants).
3.  **Party Organizer Pro (Abonnement) :** Levée des limitations, fonctionnalités avancées (reporting, export, modèles perso, IA poussée, support prioritaire).

### VII. Technologie et Design :

- **Développement :** Expo SDK (iOS/Android, Web), TypeScript.
- **Navigation :** Expo Router.
- **UI :** Composants React Native Reusables (RNR) inspirés de shadcn/ui.
- **Styling :** NativeWind.
- **Icônes :** Lucide Icons.
- **Backend & Base de Données :** Supabase (actuellement utilisé pour authentification et CRUD profils). L'IA pourrait être via des API externes ou des solutions backend intégrées.

## Objectifs de l'Expérience Utilisateur

L'expérience utilisateur de Party Organizer doit être :

- **Intuitive :** Facile à prendre en main et à naviguer, même pour des utilisateurs peu technophiles.
- **Collaborative :** Encourager et faciliter la participation de tous les invités.
- **Équitable :** Assurer une répartition juste des tâches et des coûts, perçue comme telle par tous.
- **Proactive et Assistée :** Fournir des suggestions intelligentes et anticiper les besoins pour réduire la charge mentale de l'organisateur.
- **Agréable et Engageante :** Transformer l'organisation en un processus positif, menant à des événements mémorables.
- **Fiable et Claire :** Fournir des informations précises et des notifications opportunes.

_(Cline's Note: Ce document détaille la vision produit. Il se base sur les informations fournies par l'utilisateur le 2025-05-08.)_
