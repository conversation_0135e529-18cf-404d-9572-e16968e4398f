-- Ce fichier contient des corrections pour les problèmes connus de récursion infinie dans les politiques RLS

-- Supprimer toutes les politiques problématiques
DROP POLICY IF EXISTS "Les utilisateurs peuvent voir les participants des événements auxquels ils participent" ON participants;
DROP POLICY IF EXISTS "Users can view participants of events they participate in" ON participants;
DROP POLICY IF EXISTS "Les utilisateurs peuvent voir les événements auxquels ils participent" ON events;
DROP POLICY IF EXISTS "Users can view events they participate in" ON events;
DROP POLICY IF EXISTS "Les utilisateurs peuvent voir les profils des participants à leurs événements" ON profiles;
DROP POLICY IF EXISTS "Users can view profiles of participants in their events" ON profiles;

-- Recréer les politiques pour events sans récursion
CREATE POLICY "Users can view events they created" ON events FOR SELECT
USING (organizer_id = auth.uid());

-- Politique pour voir les événements auxquels l'utilisateur participe (sans récursion)
CREATE POLICY "Users can view events they participate in" ON events FOR SELECT
USING (
    EXISTS (
        SELECT 1
        FROM participants p
        WHERE p.event_id = events.id AND p.user_id = auth.uid()
    )
);

-- Recréer les politiques pour participants sans récursion
-- Politique pour voir les participants des événements créés par l'utilisateur
CREATE POLICY "Users can view participants of events they created" ON participants FOR SELECT
USING (
    EXISTS (
        SELECT 1
        FROM events e
        WHERE e.id = participants.event_id AND e.organizer_id = auth.uid()
    )
);

-- Politique pour voir les participants des événements auxquels l'utilisateur participe
CREATE POLICY "Users can view participants of events they participate in" ON participants FOR SELECT
USING (
    EXISTS (
        SELECT 1
        FROM participants p
        WHERE 
            p.event_id = participants.event_id AND 
            p.user_id = auth.uid() AND
            p.id <> participants.id
    )
);

-- Recréer les politiques pour profiles sans récursion
CREATE POLICY "Users can view profiles of participants in their events" ON profiles FOR SELECT
USING (
    EXISTS (
        SELECT 1
        FROM events e
        JOIN participants p ON e.id = p.event_id
        WHERE 
            p.user_id = profiles.id AND
            (e.organizer_id = auth.uid() OR 
             EXISTS (
                SELECT 1 
                FROM participants p2
                WHERE p2.event_id = e.id AND p2.user_id = auth.uid() AND p2.id <> p.id
             )
            )
    )
);
