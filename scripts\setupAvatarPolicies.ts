/**
 * Script pour créer automatiquement les politiques RLS pour les avatars
 * Utilise la clé service_role pour créer les politiques
 */

// Charger les variables d'environnement
require('dotenv').config();

// Import direct de Supabase sans dépendances React Native
const { createClient } = require("@supabase/supabase-js");

// Configuration Supabase depuis les variables d'environnement
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("❌ Variables d'environnement Supabase manquantes");
  console.error(
    "Vérifiez EXPO_PUBLIC_SUPABASE_URL et SUPABASE_SERVICE_ROLE_KEY dans .env"
  );
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function setupAvatarPolicies() {
  try {
    console.log("🔐 Configuration des politiques RLS pour les avatars...");

    // 1. Supprimer les anciennes politiques si elles existent
    console.log("1. Suppression des anciennes politiques...");
    
    const dropPolicies = [
      `DROP POLICY IF EXISTS "Public read access for avatars" ON storage.objects;`,
      `DROP POLICY IF EXISTS "Authenticated users can upload avatars" ON storage.objects;`,
      `DROP POLICY IF EXISTS "Users can update their own avatars" ON storage.objects;`,
      `DROP POLICY IF EXISTS "Users can delete their own avatars" ON storage.objects;`,
    ];

    for (const policy of dropPolicies) {
      const { error } = await supabaseAdmin.rpc('exec_sql', { sql: policy });
      if (error && !error.message.includes('does not exist')) {
        console.warn(`Avertissement lors de la suppression:`, error.message);
      }
    }

    // 2. Créer les nouvelles politiques
    console.log("2. Création des nouvelles politiques...");

    const policies = [
      {
        name: "Lecture publique des avatars",
        sql: `CREATE POLICY "Public read access for avatars" ON storage.objects
              FOR SELECT USING (bucket_id = 'avatars');`
      },
      {
        name: "Upload pour utilisateurs authentifiés",
        sql: `CREATE POLICY "Authenticated users can upload avatars" ON storage.objects
              FOR INSERT WITH CHECK (
                bucket_id = 'avatars' 
                AND auth.role() = 'authenticated'
                AND (storage.foldername(name))[1] = auth.uid()::text
              );`
      },
      {
        name: "Mise à jour de ses propres avatars",
        sql: `CREATE POLICY "Users can update their own avatars" ON storage.objects
              FOR UPDATE USING (
                bucket_id = 'avatars' 
                AND auth.role() = 'authenticated'
                AND (storage.foldername(name))[1] = auth.uid()::text
              );`
      },
      {
        name: "Suppression de ses propres avatars",
        sql: `CREATE POLICY "Users can delete their own avatars" ON storage.objects
              FOR DELETE USING (
                bucket_id = 'avatars' 
                AND auth.role() = 'authenticated'
                AND (storage.foldername(name))[1] = auth.uid()::text
              );`
      }
    ];

    for (const policy of policies) {
      console.log(`   Création: ${policy.name}...`);
      const { error } = await supabaseAdmin.rpc('exec_sql', { sql: policy.sql });
      if (error) {
        console.error(`❌ Erreur lors de la création de "${policy.name}":`, error.message);
      } else {
        console.log(`   ✅ ${policy.name} créée`);
      }
    }

    // 3. Vérifier les politiques créées
    console.log("3. Vérification des politiques...");
    const { data: policies_check, error: checkError } = await supabaseAdmin
      .from('pg_policies')
      .select('policyname, cmd')
      .eq('tablename', 'objects')
      .like('policyname', '%avatar%');

    if (checkError) {
      console.warn("Impossible de vérifier les politiques:", checkError.message);
    } else {
      console.log("Politiques créées:");
      policies_check?.forEach((policy: any) => {
        console.log(`   - ${policy.policyname} (${policy.cmd})`);
      });
    }

    // 4. Vérifier le bucket
    console.log("4. Vérification du bucket avatars...");
    const { data: buckets, error: bucketError } = await supabaseAdmin.storage.listBuckets();
    
    if (bucketError) {
      console.error("Erreur lors de la vérification du bucket:", bucketError.message);
    } else {
      const avatarBucket = buckets?.find((bucket: any) => bucket.name === 'avatars');
      if (avatarBucket) {
        console.log("✅ Bucket avatars trouvé:", {
          public: avatarBucket.public,
          file_size_limit: avatarBucket.file_size_limit,
          allowed_mime_types: avatarBucket.allowed_mime_types,
        });
      } else {
        console.error("❌ Bucket avatars non trouvé");
      }
    }

    console.log("\n🎉 Configuration des politiques RLS terminée !");
    console.log("Le système d'avatars est maintenant prêt à l'emploi.");

  } catch (error) {
    console.error("❌ Erreur lors de la configuration des politiques:", error);
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  setupAvatarPolicies()
    .then(() => {
      console.log("\n✅ Script terminé avec succès");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ Erreur lors de l'exécution:", error);
      process.exit(1);
    });
}

export { setupAvatarPolicies };
