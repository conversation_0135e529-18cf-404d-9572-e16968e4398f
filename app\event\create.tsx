import React, { useState, useEffect, useRef } from "react";
import { View, ScrollView, Alert, Platform, Modal } from "react-native";
import * as Location from "expo-location";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { DatePicker } from "~/components/ui/date-picker";
import { TimePicker } from "~/components/ui/time-picker";

import { Dialog, DialogContent } from "~/components/ui/dialog"; // Import Dialog components
import { EmojiPicker, PickerEmoji } from "~/components/EmojiPicker"; // Import EmojiPicker
// Icône temporairement supprimée pour compatibilité web
import { cn } from "~/lib/utils"; // Import cn utility

import { useAuth } from "~/lib/AuthContext";
import { createEvent, createParticipant } from "~/lib/supabaseCrud";

import { useRouter } from "expo-router";
import { showToast } from "~/lib/toastService";
import { EventOptionsSection } from "~/components/EventOptionsSection";
import { ParticipantManager } from "~/components/ParticipantManager";
// Assuming ParticipantRole and ParticipantStatus are now string literal unions from database.types.ts
// If they are still enums from lib/types.ts, adjust import if needed.
// For now, assuming they are string literals as per recent refactoring.
// import { ParticipantRole, ParticipantStatus } from "~/lib/types";
type ParticipantRoleEnum = Database["public"]["Enums"]["participant_role"];
type ParticipantStatusEnum = Database["public"]["Enums"]["participant_status"];
import { Database } from "~/lib/database.types";
import MapComponent from "~/components/MapComponent";
import {
  geocodeAddress,
  getCurrentPosition,
  GeocodeError,
} from "~/lib/geocodingService";

// Interface pour les erreurs de validation
interface ValidationErrors {
  title?: string;
  dateTime?: string;
}

export default function CreateEventScreen() {
  const { session } = useAuth();
  const router = useRouter();

  // États du formulaire
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [dateTime, setDateTime] = useState(() => {
    const defaultDate = new Date();
    defaultDate.setHours(19, 0, 0, 0); // 19h00 par défaut
    return defaultDate;
  });
  const [location, setLocation] = useState("");
  const [icon, setIcon] = useState(""); // Will store the native emoji string
  const [allowSuggestions, setAllowSuggestions] = useState(false);
  const [allowPreAssignment, setAllowPreAssignment] = useState(false);
  const [mapLocation, setMapLocation] = useState({ lat: -3.745, lng: -38.523 });

  // États pour les participants
  const [participantNames, setParticipantNames] = useState<string[]>([]);

  // États UI
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [creationError, setCreationError] = useState<string | null>(null);
  const [showEmojiPickerDialog, setShowEmojiPickerDialog] = useState(false);
  const [creationSuccess, setCreationSuccess] = useState(false);

  useEffect(() => {
    if (submitAttempted) {
      validateForm();
    }
  }, [title, dateTime, submitAttempted]);

  const [locationSuggestions, setLocationSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const locationInputRef = useRef(null);

  const handleLocationSelect = async (selectedLocation: string) => {
    setLocation(selectedLocation);
    setShowSuggestions(false);
    try {
      const coords = await geocodeAddress(selectedLocation);
      setMapLocation(coords);
    } catch (error) {
      console.error("Geocoding error:", error);
      showToast(
        error instanceof Error ? error.message : "Could not find location",
        { type: "error" }
      );
    }
  };

  const handleCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== "granted") {
        throw new Error(GeocodeError.PERMISSION_DENIED);
      }

      const position = await getCurrentPosition();
      setMapLocation(position);

      // Reverse geocode to get address
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${position.lat},${position.lng}&key=${process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}`
      );
      const data = await response.json();
      if (data.status === "OK") {
        setLocation(data.results[0].formatted_address);
      }
    } catch (error) {
      console.error("Location error:", error);
      showToast(
        error instanceof Error
          ? error.message
          : "Could not get current location",
        { type: "error" }
      );
    }
  };

  useEffect(() => {
    if (location && location.length > 3) {
      const timer = setTimeout(async () => {
        try {
          // Simulate getting suggestions from API
          setLocationSuggestions([
            `${location}, Paris, France`,
            `${location}, Lyon, France`,
            `${location}, Marseille, France`,
          ]);
          setShowSuggestions(true);
        } catch (error) {
          console.error("Error getting suggestions:", error);
        }
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setLocationSuggestions([]);
      setShowSuggestions(false);
    }
  }, [location]);

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    if (!title.trim()) {
      newErrors.title = "Le titre est obligatoire";
    }
    const now = new Date();
    now.setSeconds(0, 0);
    const dateToCheck = new Date(dateTime);
    dateToCheck.setSeconds(0, 0);
    const isSameDay =
      dateToCheck.getDate() === now.getDate() &&
      dateToCheck.getMonth() === now.getMonth() &&
      dateToCheck.getFullYear() === now.getFullYear();
    if (isSameDay) {
      const nowTime = now.getHours() * 60 + now.getMinutes() - 5;
      const eventTime = dateToCheck.getHours() * 60 + dateToCheck.getMinutes();
      if (eventTime <= nowTime) {
        newErrors.dateTime =
          "Pour aujourd'hui, l'heure doit être dans le futur";
      }
    } else if (dateToCheck < now) {
      newErrors.dateTime = "La date ne peut pas être dans le passé";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleIconSelect = (selectedEmoji: PickerEmoji) => {
    setIcon(selectedEmoji.native);
    setShowEmojiPickerDialog(false); // Close the dialog
  };

  const handleCreateEvent = async () => {
    setSubmitAttempted(true);
    setCreationError(null);

    if (!session?.user?.id) {
      showToast("Vous devez être connecté pour créer un événement.", {
        type: "error",
      });
      return;
    }

    if (!validateForm()) {
      showToast("Veuillez corriger les erreurs dans le formulaire.", {
        type: "error",
      });
      return;
    }

    setLoading(true);
    try {
      const eventData = {
        title,
        description: description || null,
        date_time: dateTime.toISOString(),
        location: location || null,
        icon: icon || null,
        allow_suggestions: allowSuggestions,
        allow_pre_assignment: allowPreAssignment,
        organizer_delegated: false,
      };

      const createdEvent = await createEvent(eventData, session.user.id);

      if (!createdEvent) {
        throw new Error("Impossible de créer l'événement. Veuillez réessayer.");
      }

      const participantData = {
        event_id: createdEvent.id,
        user_id: session.user.id,
        role: "organizer" as ParticipantRoleEnum,
        status: "accepted" as ParticipantStatusEnum,
        anonymous_name: null,
        anonymous_email: null,
        anonymous_phone: null,
        invitation_token: null,
      };

      const participantResult = await createParticipant(participantData);

      if (!participantResult) {
        throw new Error(
          "L'événement a été créé mais vous n'avez pas été ajouté comme organisateur."
        );
      }

      // Créer les participants ajoutés
      if (participantNames.length > 0) {
        for (const name of participantNames) {
          try {
            const invitationToken = crypto.randomUUID();
            const participantData = {
              event_id: createdEvent.id,
              user_id: null,
              role: "guest" as ParticipantRoleEnum,
              status: "pending" as ParticipantStatusEnum,
              anonymous_name: name,
              anonymous_email: null,
              anonymous_phone: null,
              invitation_token: invitationToken,
            };
            await createParticipant(participantData);
          } catch (error) {
            console.error(
              `Erreur lors de la création du participant ${name}:`,
              error
            );
            // Non-blocking, just log it
          }
        }
      }

      showToast("🎉 Événement créé avec succès !", { type: "success" });
      setCreationSuccess(true);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erreur inconnue";
      setCreationError(`Une erreur est survenue: ${errorMessage}`);
      showToast(`Erreur: ${errorMessage}`, { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const ErrorMessage = ({ message }: { message?: string }) => {
    if (!message) return null;
    return <Text className="text-sm text-destructive mt-1">{message}</Text>;
  };

  if (creationSuccess) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background">
        <Card className="w-full max-w-md p-6 items-center">
          <Text className="text-6xl mb-4">🎉</Text>
          <Text className="text-2xl font-bold text-center mb-2 text-foreground">
            Événement créé !
          </Text>
          <Text className="text-muted-foreground text-center mb-6">
            Vous pouvez maintenant le partager et ajouter des items depuis la
            page de l'événement.
          </Text>
          <Button
            onPress={() => router.replace("/(tabs)")}
            className="w-full h-12"
          >
            <Text className="text-primary-foreground font-medium">
              Retour à l'accueil
            </Text>
          </Button>
        </Card>
      </View>
    );
  }

  return (
    <ScrollView
      className="flex-1 bg-background"
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        <View className="mb-8">
          <Text className="text-2xl font-bold text-center mb-2 text-foreground">
            Nouvel Événement
          </Text>
          <Text className="text-muted-foreground text-center">
            Créez votre événement en quelques étapes
          </Text>
        </View>

        {creationError && (
          <View className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg mb-6">
            <Text className="text-destructive">{creationError}</Text>
          </View>
        )}

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Informations principales
          </Text>
          <View className="mb-5">
            <Label nativeID="titleLabel" className="flex-row mb-1.5">
              <Text className="text-destructive mr-1">*</Text>
              <Text className="font-medium text-foreground">Titre</Text>
            </Label>
            <Input
              nativeID="titleLabel"
              placeholder="Ex: Anniversaire de Gauthier"
              value={title}
              onChangeText={setTitle}
              aria-required="true"
              className={`h-11 ${
                errors.title ? "border-destructive" : "border-border"
              }`}
            />
            <ErrorMessage message={errors.title} />
          </View>
          <View className="mb-5">
            <Label
              nativeID="descLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Description
            </Label>
            <Textarea
              nativeID="descLabel"
              placeholder="Détails supplémentaires (thème, code vestimentaire...)"
              value={description}
              onChangeText={setDescription}
              numberOfLines={3}
              className="border-border"
            />
          </View>
          <View className="mb-2">
            <Label nativeID="dateTimeLabel" className="flex-row mb-1.5">
              <Text className="text-destructive mr-1">*</Text>
              <Text className="font-medium text-foreground">Date et Heure</Text>
            </Label>
            <View
              className={Platform.OS === "web" ? "flex-row gap-4" : "gap-4"}
            >
              <View className={Platform.OS === "web" ? "flex-1" : "mb-4"}>
                <Label
                  nativeID="dateLabel"
                  className="mb-1.5 font-medium text-foreground"
                >
                  Date
                </Label>
                <DatePicker
                  value={dateTime}
                  onChange={setDateTime}
                  minimumDate={new Date()}
                  error={!!errors.dateTime}
                  placeholder="Sélectionner une date"
                />
              </View>
              <View className={Platform.OS === "web" ? "flex-1" : ""}>
                <Label
                  nativeID="timeLabel"
                  className="mb-1.5 font-medium text-foreground"
                >
                  Heure
                </Label>
                <TimePicker
                  value={dateTime}
                  onChange={setDateTime}
                  error={!!errors.dateTime}
                  placeholder="Sélectionner une heure"
                />
              </View>
            </View>
            <ErrorMessage message={errors.dateTime} />
          </View>
        </View>

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Détails supplémentaires
          </Text>
          <View className="mb-5">
            <Label
              nativeID="locationLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Lieu
            </Label>
            <View className="relative">
              <View className="flex-row">
                <Input
                  ref={locationInputRef}
                  nativeID="locationLabel"
                  placeholder="Ex: 12 Rue de la Paix, Paris"
                  value={location}
                  onChangeText={setLocation}
                  className="h-11 border-border flex-1"
                  onFocus={() => location && setShowSuggestions(true)}
                />
                <Button
                  variant="outline"
                  className="h-11 ml-2"
                  onPress={handleCurrentLocation}
                >
                  <Text>📍</Text>
                </Button>
              </View>

              {showSuggestions &&
                locationSuggestions.length > 0 &&
                (Platform.OS === "web" ? (
                  <div className="absolute top-12 left-0 right-0 bg-card border border-border rounded-lg z-10 shadow-lg">
                    {locationSuggestions.map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="ghost"
                        className="h-10 justify-start px-3"
                        onPress={() => handleLocationSelect(suggestion)}
                      >
                        <Text className="text-start">{suggestion}</Text>
                      </Button>
                    ))}
                  </div>
                ) : (
                  <View
                    style={{
                      position: "absolute",
                      top: 48,
                      left: 0,
                      right: 0,
                      backgroundColor: "white",
                      borderWidth: 1,
                      borderColor: "#e2e8f0",
                      borderRadius: 8,
                      zIndex: 10,
                      elevation: 5,
                    }}
                  >
                    {locationSuggestions.map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="ghost"
                        style={{
                          height: 40,
                          justifyContent: "flex-start",
                          paddingHorizontal: 12,
                        }}
                        onPress={() => handleLocationSelect(suggestion)}
                      >
                        <Text style={{ textAlign: "left" }}>{suggestion}</Text>
                      </Button>
                    ))}
                  </View>
                ))}
            </View>
          </View>

          {/* Carte */}
          <View className="mb-5">
            <Label
              nativeID="mapLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Carte
            </Label>
            <MapComponent location={mapLocation} />
          </View>

          {/* Icône (Emoji Picker) */}
          <View className="mb-5">
            <Label
              nativeID="iconLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Icône
            </Label>
            <Button
              variant="outline"
              className="h-12 w-full flex-row items-center justify-between px-3 py-2"
              onPress={() => setShowEmojiPickerDialog(true)}
            >
              <Text
                className={cn(
                  "text-sm",
                  icon ? "text-foreground" : "text-muted-foreground"
                )}
              >
                {icon || "Choisir une icône"}
              </Text>
              {icon ? (
                <Text className="text-2xl">{icon}</Text>
              ) : (
                <Text className="text-muted-foreground text-xl">😊</Text>
              )}
            </Button>
            {Platform.OS === "web" ? (
              <Dialog
                open={showEmojiPickerDialog}
                onOpenChange={setShowEmojiPickerDialog}
              >
                <DialogContent className="p-0 w-auto max-w-md bg-transparent border-none shadow-none z-[9999] web:z-[999999999]">
                  <EmojiPicker onEmojiSelected={handleIconSelect} />
                </DialogContent>
              </Dialog>
            ) : (
              <Modal
                visible={showEmojiPickerDialog}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowEmojiPickerDialog(false)}
                statusBarTranslucent={true}
              >
                <View className="flex-1 justify-center items-center bg-black/50 p-4">
                  <View className="bg-background rounded-lg w-full max-w-md max-h-[85%]">
                    <View className="flex-row justify-between items-center p-4 border-b border-border">
                      <Text className="text-lg font-semibold">
                        Choisir une icône
                      </Text>
                      <Button
                        variant="ghost"
                        onPress={() => setShowEmojiPickerDialog(false)}
                      >
                        <Text>✕</Text>
                      </Button>
                    </View>
                    <EmojiPicker onEmojiSelected={handleIconSelect} />
                  </View>
                </View>
              </Modal>
            )}
          </View>
        </View>

        <EventOptionsSection
          allowSuggestions={allowSuggestions}
          allowPreAssignment={allowPreAssignment}
          onAllowSuggestionsChange={setAllowSuggestions}
          onAllowPreAssignmentChange={setAllowPreAssignment}
        />

        {/* Section Participants */}
        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Participants
          </Text>
          <Text className="text-sm text-muted-foreground mb-4">
            Ajoutez les noms des personnes que vous souhaitez inviter
            (optionnel)
          </Text>

          <ParticipantManager
            participants={participantNames}
            onParticipantsChange={setParticipantNames}
            currentUserName="Vous"
            placeholder="Ex: Maxence Manson"
            addButtonText="Ajouter"
          />
        </View>

        {/* Section Prévisualisation */}
        {title && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Prévisualisation</CardTitle>
            </CardHeader>
            <CardContent>
              <View className="flex-row items-center mb-3">
                <Text className="text-2xl mr-3">{icon || "🎉"}</Text>
                <View className="flex-1">
                  <Text className="text-lg font-semibold text-foreground">
                    {title}
                  </Text>
                  <Text className="text-sm text-muted-foreground">
                    {dateTime.toLocaleDateString("fr-FR", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}{" "}
                    à{" "}
                    {dateTime.toLocaleTimeString("fr-FR", {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </Text>
                </View>
              </View>
              {location && (
                <Text className="text-sm text-muted-foreground mb-2">
                  📍 {location}
                </Text>
              )}
              {description && (
                <Text className="text-sm text-foreground mb-2">
                  {description}
                </Text>
              )}
              {participantNames.length > 0 && (
                <Text className="text-sm text-muted-foreground">
                  👥 {participantNames.length + 1} participant
                  {participantNames.length > 0 ? "s" : ""} (vous inclus)
                </Text>
              )}
            </CardContent>
          </Card>
        )}

        <Button
          onPress={handleCreateEvent}
          disabled={loading}
          loading={loading}
          className="h-12 bg-primary rounded-lg mb-4"
        >
          <Text className="text-primary-foreground font-medium text-center w-full">
            {loading ? "Création en cours..." : "Créer l'Événement"}
          </Text>
        </Button>
        <Text className="text-sm text-muted-foreground text-center mb-8">
          <Text className="text-destructive">*</Text> Champs obligatoires
        </Text>
      </View>
    </ScrollView>
  );
}
