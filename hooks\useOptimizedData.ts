import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  OptimizedEventQueries, 
  OptimizedItemQueries, 
  OptimizedParticipantQueries,
  OptimizedContactQueries,
  CacheUtils 
} from '~/lib/optimizedQueries';
import { Event, Item, Participant, Contact } from '~/lib/types';
import { useAuth } from '~/lib/AuthContext';

/**
 * Hook pour gérer les événements avec cache optimisé
 */
export function useOptimizedEvents() {
  const { session } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const loadEvents = useCallback(async (forceRefresh = false) => {
    if (!session?.user?.id) return;

    try {
      setError(null);
      if (forceRefresh) setRefreshing(true);
      else setLoading(true);

      const userEvents = await OptimizedEventQueries.getUserEvents(session.user.id);
      setEvents(userEvents);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des événements');
      console.error('Error loading events:', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [session?.user?.id]);

  useEffect(() => {
    loadEvents();
  }, [loadEvents]);

  const refresh = useCallback(() => {
    loadEvents(true);
  }, [loadEvents]);

  return {
    events,
    loading,
    error,
    refreshing,
    refresh,
    reload: loadEvents,
  };
}

/**
 * Hook pour gérer un événement spécifique avec ses données associées
 */
export function useOptimizedEvent(eventId: number | null) {
  const [event, setEvent] = useState<Event | null>(null);
  const [items, setItems] = useState<Item[]>([]);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const loadEventData = useCallback(async (forceRefresh = false) => {
    if (!eventId) return;

    try {
      setError(null);
      if (forceRefresh) setRefreshing(true);
      else setLoading(true);

      const data = await OptimizedEventQueries.preloadEventData(eventId);
      
      setEvent(data.event);
      setItems(data.items);
      setParticipants(data.participants);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement de l\'événement');
      console.error('Error loading event data:', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [eventId]);

  useEffect(() => {
    if (eventId) {
      loadEventData();
    } else {
      setEvent(null);
      setItems([]);
      setParticipants([]);
      setLoading(false);
    }
  }, [eventId, loadEventData]);

  const refresh = useCallback(() => {
    loadEventData(true);
  }, [loadEventData]);

  const updateItems = useCallback(async (newItems: Item[]) => {
    setItems(newItems);
    // Le cache sera invalidé par les mutations dans OptimizedItemQueries
  }, []);

  const updateParticipants = useCallback(async (newParticipants: Participant[]) => {
    setParticipants(newParticipants);
    // Le cache sera invalidé par les mutations
  }, []);

  return {
    event,
    items,
    participants,
    loading,
    error,
    refreshing,
    refresh,
    updateItems,
    updateParticipants,
  };
}

/**
 * Hook pour gérer les items d'un événement
 */
export function useOptimizedItems(eventId: number | null) {
  const [items, setItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadItems = useCallback(async () => {
    if (!eventId) return;

    try {
      setError(null);
      setLoading(true);

      const eventItems = await OptimizedItemQueries.getEventItems(eventId);
      setItems(eventItems);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des items');
      console.error('Error loading items:', err);
    } finally {
      setLoading(false);
    }
  }, [eventId]);

  useEffect(() => {
    loadItems();
  }, [loadItems]);

  const addItem = useCallback(async (itemData: any) => {
    if (!eventId) return null;

    try {
      const newItem = await OptimizedItemQueries.createItem({
        ...itemData,
        event_id: eventId,
      });
      
      if (newItem) {
        // Recharger les items pour avoir la liste à jour
        await loadItems();
      }
      
      return newItem;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de l\'ajout de l\'item');
      return null;
    }
  }, [eventId, loadItems]);

  const updateItem = useCallback(async (itemId: number, updates: any) => {
    if (!eventId) return null;

    try {
      const updatedItem = await OptimizedItemQueries.updateItem(itemId, eventId, updates);
      
      if (updatedItem) {
        // Recharger les items pour avoir la liste à jour
        await loadItems();
      }
      
      return updatedItem;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la mise à jour de l\'item');
      return null;
    }
  }, [eventId, loadItems]);

  const removeItem = useCallback(async (itemId: number) => {
    if (!eventId) return false;

    try {
      const success = await OptimizedItemQueries.deleteItem(itemId, eventId);
      
      if (success) {
        // Recharger les items pour avoir la liste à jour
        await loadItems();
      }
      
      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la suppression de l\'item');
      return false;
    }
  }, [eventId, loadItems]);

  return {
    items,
    loading,
    error,
    addItem,
    updateItem,
    removeItem,
    reload: loadItems,
  };
}

/**
 * Hook pour gérer les contacts avec cache optimisé
 */
export function useOptimizedContacts() {
  const { session } = useAuth();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadContacts = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      setError(null);
      setLoading(true);

      const userContacts = await OptimizedContactQueries.getUserContacts(session.user.id);
      setContacts(userContacts);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement des contacts');
      console.error('Error loading contacts:', err);
    } finally {
      setLoading(false);
    }
  }, [session?.user?.id]);

  useEffect(() => {
    loadContacts();
  }, [loadContacts]);

  return {
    contacts,
    loading,
    error,
    reload: loadContacts,
  };
}

/**
 * Hook pour précharger les données utilisateur au démarrage
 */
export function useDataPreloader() {
  const { session } = useAuth();
  const [preloaded, setPreloaded] = useState(false);
  const preloadingRef = useRef(false);

  const preloadData = useCallback(async () => {
    if (!session?.user?.id || preloadingRef.current) return;

    try {
      preloadingRef.current = true;
      await CacheUtils.warmupCache(session.user.id);
      setPreloaded(true);
    } catch (error) {
      console.error('Data preloading error:', error);
    } finally {
      preloadingRef.current = false;
    }
  }, [session?.user?.id]);

  useEffect(() => {
    if (session?.user?.id && !preloaded) {
      preloadData();
    }
  }, [session?.user?.id, preloaded, preloadData]);

  const clearCache = useCallback(async () => {
    if (session?.user?.id) {
      await CacheUtils.clearUserCache(session.user.id);
      setPreloaded(false);
    }
  }, [session?.user?.id]);

  return {
    preloaded,
    clearCache,
  };
}

/**
 * Hook pour gérer les statistiques de performance
 */
export function usePerformanceStats() {
  const [stats, setStats] = useState({
    cacheHitRate: 0,
    totalRequests: 0,
    averageResponseTime: 0,
  });

  const updateStats = useCallback(() => {
    // Ici on pourrait récupérer les vraies statistiques du cache
    // Pour l'instant, on simule
    setStats({
      cacheHitRate: Math.random() * 100,
      totalRequests: Math.floor(Math.random() * 1000),
      averageResponseTime: Math.random() * 500,
    });
  }, []);

  useEffect(() => {
    const interval = setInterval(updateStats, 10000); // Mise à jour toutes les 10 secondes
    return () => clearInterval(interval);
  }, [updateStats]);

  return stats;
}
