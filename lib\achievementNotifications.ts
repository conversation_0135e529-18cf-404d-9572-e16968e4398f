/**
 * Service de notifications pour les achievements
 * Gère les célébrations et notifications visuelles
 */

import { showToast } from './toastService';
import { Badge } from './achievementsService';
import { UserStreak } from './streaksService';
import { CommunityChallenge } from './challengesService';
import { BadgeCollection } from './collectionsService';

export interface AchievementNotification {
  id: string;
  type: 'badge' | 'streak' | 'challenge' | 'collection' | 'level_up' | 'milestone';
  title: string;
  description: string;
  icon: string;
  points?: number;
  rarity?: 'common' | 'rare' | 'epic' | 'legendary';
  timestamp: Date;
  data?: any;
}

class AchievementNotificationsService {
  private notifications: AchievementNotification[] = [];
  private listeners: ((notification: AchievementNotification) => void)[] = [];

  /**
   * Ajoute un listener pour les nouvelles notifications
   */
  addListener(callback: (notification: AchievementNotification) => void): () => void {
    this.listeners.push(callback);
    
    // Retourner une fonction de cleanup
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Émet une notification
   */
  private emit(notification: AchievementNotification): void {
    this.notifications.unshift(notification);
    
    // Garder seulement les 50 dernières notifications
    if (this.notifications.length > 50) {
      this.notifications = this.notifications.slice(0, 50);
    }

    // Notifier tous les listeners
    this.listeners.forEach(listener => {
      try {
        listener(notification);
      } catch (error) {
        console.error('Error in notification listener:', error);
      }
    });
  }

  /**
   * Célèbre un nouveau badge
   */
  celebrateBadge(badge: Badge, isFirstTime: boolean = true): void {
    const rarityEmojis = {
      common: '🎉',
      rare: '✨',
      epic: '🌟',
      legendary: '👑',
    };

    const rarityColors = {
      common: 'text-gray-600',
      rare: 'text-blue-600',
      epic: 'text-purple-600',
      legendary: 'text-yellow-600',
    };

    const emoji = rarityEmojis[badge.rarity] || '🎉';
    const title = isFirstTime ? 'Nouveau badge débloqué !' : 'Badge obtenu !';
    
    const notification: AchievementNotification = {
      id: `badge_${badge.id}_${Date.now()}`,
      type: 'badge',
      title,
      description: `${emoji} ${badge.title} - ${badge.description}`,
      icon: badge.icon,
      points: badge.points,
      rarity: badge.rarity,
      timestamp: new Date(),
      data: { badge },
    };

    this.emit(notification);

    // Toast notification
    showToast(
      `${emoji} ${badge.title} débloqué ! +${badge.points} points`,
      { 
        type: 'success',
        duration: badge.rarity === 'legendary' ? 8000 : 5000,
      }
    );

    // Effet spécial pour les badges légendaires
    if (badge.rarity === 'legendary') {
      this.triggerSpecialEffect('legendary_badge');
    }
  }

  /**
   * Célèbre un milestone de streak
   */
  celebrateStreak(streak: UserStreak, milestone: { days: number; title: string; icon: string }): void {
    const notification: AchievementNotification = {
      id: `streak_${streak.id}_${milestone.days}_${Date.now()}`,
      type: 'streak',
      title: `Streak ${milestone.title} !`,
      description: `🔥 ${milestone.days} jours consécutifs de ${this.getStreakTypeName(streak.streak_type)}`,
      icon: milestone.icon,
      timestamp: new Date(),
      data: { streak, milestone },
    };

    this.emit(notification);

    showToast(
      `🔥 Streak ${milestone.title} ! ${milestone.days} jours !`,
      { type: 'success', duration: 5000 }
    );
  }

  /**
   * Célèbre la completion d'un challenge
   */
  celebrateChallenge(challenge: CommunityChallenge): void {
    const typeEmojis = {
      individual: '🎯',
      community: '🌟',
      competitive: '🏆',
    };

    const emoji = typeEmojis[challenge.challenge_type] || '🎯';
    
    const notification: AchievementNotification = {
      id: `challenge_${challenge.id}_${Date.now()}`,
      type: 'challenge',
      title: 'Challenge terminé !',
      description: `${emoji} ${challenge.title} - ${challenge.reward_points} points gagnés`,
      icon: challenge.icon,
      points: challenge.reward_points,
      timestamp: new Date(),
      data: { challenge },
    };

    this.emit(notification);

    showToast(
      `${emoji} Challenge "${challenge.title}" terminé ! +${challenge.reward_points} points`,
      { type: 'success', duration: 6000 }
    );
  }

  /**
   * Célèbre la completion d'une collection
   */
  celebrateCollection(collection: BadgeCollection): void {
    const notification: AchievementNotification = {
      id: `collection_${collection.id}_${Date.now()}`,
      type: 'collection',
      title: 'Collection complétée !',
      description: `📚 ${collection.title} - Tous les badges collectés !`,
      icon: collection.icon,
      points: collection.completion_reward_points,
      rarity: 'epic',
      timestamp: new Date(),
      data: { collection },
    };

    this.emit(notification);

    showToast(
      `📚 Collection "${collection.title}" complétée ! +${collection.completion_reward_points} points`,
      { type: 'success', duration: 7000 }
    );

    this.triggerSpecialEffect('collection_complete');
  }

  /**
   * Célèbre un passage de niveau
   */
  celebrateLevelUp(oldLevel: number, newLevel: number, totalPoints: number): void {
    const notification: AchievementNotification = {
      id: `level_up_${newLevel}_${Date.now()}`,
      type: 'level_up',
      title: 'Niveau supérieur !',
      description: `🆙 Niveau ${newLevel} atteint avec ${totalPoints} points !`,
      icon: '🆙',
      timestamp: new Date(),
      data: { oldLevel, newLevel, totalPoints },
    };

    this.emit(notification);

    showToast(
      `🆙 Niveau ${newLevel} atteint ! Félicitations !`,
      { type: 'success', duration: 5000 }
    );

    // Effet spécial pour les niveaux importants
    if (newLevel % 10 === 0) {
      this.triggerSpecialEffect('major_level_up');
    }
  }

  /**
   * Célèbre un milestone général
   */
  celebrateMilestone(title: string, description: string, icon: string, points?: number): void {
    const notification: AchievementNotification = {
      id: `milestone_${Date.now()}`,
      type: 'milestone',
      title,
      description,
      icon,
      points,
      timestamp: new Date(),
    };

    this.emit(notification);

    showToast(
      `${icon} ${title} - ${description}`,
      { type: 'success', duration: 4000 }
    );
  }

  /**
   * Déclenche des effets spéciaux
   */
  private triggerSpecialEffect(type: 'legendary_badge' | 'collection_complete' | 'major_level_up'): void {
    // Ici on pourrait ajouter des animations, confettis, etc.
    console.log(`🎊 Special effect triggered: ${type}`);
    
    // Exemple d'effet : vibration sur mobile
    if (typeof navigator !== 'undefined' && navigator.vibrate) {
      switch (type) {
        case 'legendary_badge':
          navigator.vibrate([200, 100, 200, 100, 200]);
          break;
        case 'collection_complete':
          navigator.vibrate([300, 150, 300]);
          break;
        case 'major_level_up':
          navigator.vibrate([100, 50, 100, 50, 100, 50, 300]);
          break;
      }
    }
  }

  /**
   * Obtient le nom lisible d'un type de streak
   */
  private getStreakTypeName(type: string): string {
    switch (type) {
      case 'daily_login': return 'connexion quotidienne';
      case 'weekly_event': return 'organisation hebdomadaire';
      case 'monthly_organization': return 'organisation mensuelle';
      case 'participation_streak': return 'participation continue';
      default: return 'activité';
    }
  }

  /**
   * Récupère les notifications récentes
   */
  getRecentNotifications(limit: number = 10): AchievementNotification[] {
    return this.notifications.slice(0, limit);
  }

  /**
   * Marque une notification comme lue
   */
  markAsRead(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.data = { ...notification.data, read: true };
    }
  }

  /**
   * Efface toutes les notifications
   */
  clearAll(): void {
    this.notifications = [];
  }

  /**
   * Efface les notifications anciennes (plus de 7 jours)
   */
  clearOld(): void {
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    
    this.notifications = this.notifications.filter(
      n => n.timestamp > weekAgo
    );
  }

  /**
   * Obtient les statistiques des notifications
   */
  getStats(): {
    total: number;
    byType: Record<string, number>;
    unread: number;
    today: number;
  } {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const byType: Record<string, number> = {};
    let unread = 0;
    let todayCount = 0;

    this.notifications.forEach(notification => {
      // Compter par type
      byType[notification.type] = (byType[notification.type] || 0) + 1;
      
      // Compter non lues
      if (!notification.data?.read) {
        unread++;
      }
      
      // Compter aujourd'hui
      if (notification.timestamp >= today) {
        todayCount++;
      }
    });

    return {
      total: this.notifications.length,
      byType,
      unread,
      today: todayCount,
    };
  }
}

// Instance singleton
export const achievementNotifications = new AchievementNotificationsService();

// Fonctions utilitaires
export const notifications = {
  badge: (badge: Badge, isFirstTime?: boolean) => 
    achievementNotifications.celebrateBadge(badge, isFirstTime),
  streak: (streak: UserStreak, milestone: any) => 
    achievementNotifications.celebrateStreak(streak, milestone),
  challenge: (challenge: CommunityChallenge) => 
    achievementNotifications.celebrateChallenge(challenge),
  collection: (collection: BadgeCollection) => 
    achievementNotifications.celebrateCollection(collection),
  levelUp: (oldLevel: number, newLevel: number, totalPoints: number) => 
    achievementNotifications.celebrateLevelUp(oldLevel, newLevel, totalPoints),
  milestone: (title: string, description: string, icon: string, points?: number) => 
    achievementNotifications.celebrateMilestone(title, description, icon, points),
  addListener: (callback: (notification: AchievementNotification) => void) => 
    achievementNotifications.addListener(callback),
  getRecent: (limit?: number) => 
    achievementNotifications.getRecentNotifications(limit),
  markAsRead: (id: string) => 
    achievementNotifications.markAsRead(id),
  clearAll: () => 
    achievementNotifications.clearAll(),
  clearOld: () => 
    achievementNotifications.clearOld(),
  getStats: () => 
    achievementNotifications.getStats(),
};
