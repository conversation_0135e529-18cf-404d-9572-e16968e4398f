import React, { useState, useEffect } from "react";
import { View, FlatList, RefreshControl, Platform, Modal } from "react-native";
import { Text } from "~/components/ui/text";
import { H1 } from "~/components/ui/typography";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Separator } from "~/components/ui/separator";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import {
  fetchContacts,
  createContact,
  updateContact,
  deleteContact,
} from "~/lib/supabaseCrud";
import { Contact } from "~/lib/types";

interface ContactFormData {
  name: string;
  email: string;
  phone: string;
}

export default function ContactsScreen() {
  const { session } = useAuth();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [formData, setFormData] = useState<ContactFormData>({
    name: "",
    email: "",
    phone: "",
  });

  const loadContacts = async () => {
    if (!session?.user?.id) return;

    setLoading(true);
    try {
      const fetchedContacts = await fetchContacts(session.user.id);
      setContacts(fetchedContacts || []);
    } catch (error) {
      console.error("Erreur lors du chargement des contacts:", error);
      showToast("Impossible de charger les contacts", { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadContacts();
    setRefreshing(false);
  };

  useEffect(() => {
    loadContacts();
  }, [session?.user?.id]);

  const handleAddContact = () => {
    setEditingContact(null);
    setFormData({ name: "", email: "", phone: "" });
    setShowAddModal(true);
  };

  const handleEditContact = (contact: Contact) => {
    setEditingContact(contact);
    setFormData({
      name: contact.name,
      email: contact.email || "",
      phone: contact.phone || "",
    });
    setShowAddModal(true);
  };

  const handleSaveContact = async () => {
    if (!formData.name.trim()) {
      showToast("Le nom est obligatoire", { type: "error" });
      return;
    }

    if (!session?.user?.id) return;

    try {
      if (editingContact) {
        await updateContact(editingContact.id, {
          name: formData.name.trim(),
          email: formData.email.trim() || null,
          phone: formData.phone.trim() || null,
        });
        showToast("Contact mis à jour !", { type: "success" });
      } else {
        await createContact({
          user_id: session.user.id,
          name: formData.name.trim(),
          email: formData.email.trim() || null,
          phone: formData.phone.trim() || null,
          contact_profile_id: null,
        });
        showToast("Contact ajouté !", { type: "success" });
      }

      setShowAddModal(false);
      await loadContacts();
    } catch (error) {
      console.error("Erreur lors de la sauvegarde:", error);
      showToast("Erreur lors de la sauvegarde", { type: "error" });
    }
  };

  const handleDeleteContact = async (contactId: number) => {
    try {
      const success = await deleteContact(contactId);
      if (success) {
        showToast("Contact supprimé", { type: "success" });
        await loadContacts();
      } else {
        throw new Error("Échec de la suppression");
      }
    } catch (error) {
      console.error("Erreur lors de la suppression:", error);
      showToast("Erreur lors de la suppression", { type: "error" });
    }
  };

  const renderContactCard = ({ item: contact }: { item: Contact }) => (
    <Card className="mb-3 border border-border bg-white">
      <CardContent className="p-4">
        <View className="flex-row items-center justify-between">
          <View className="flex-1">
            <Text className="text-lg font-semibold text-foreground mb-1">
              {contact.name}
            </Text>
            {contact.email && (
              <Text className="text-sm text-muted-foreground mb-1">
                📧 {contact.email}
              </Text>
            )}
            {contact.phone && (
              <Text className="text-sm text-muted-foreground">
                📱 {contact.phone}
              </Text>
            )}
          </View>
          <View className="flex-row gap-2">
            <Button
              variant="outline"
              size="sm"
              onPress={() => handleEditContact(contact)}
            >
              <Text>✏️</Text>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onPress={() => handleDeleteContact(contact.id)}
            >
              <Text>🗑️</Text>
            </Button>
          </View>
        </View>
      </CardContent>
    </Card>
  );

  if (!session) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background">
        <Text className="text-6xl mb-4">👥</Text>
        <Text className="text-xl font-semibold mb-2 text-center">
          Connexion requise
        </Text>
        <Text className="text-muted-foreground text-center mb-6">
          Connectez-vous pour gérer vos contacts
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background">
      <View className="p-4 pb-2">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <H1 className="text-2xl font-bold text-foreground">Mes Contacts</H1>
            <Text className="text-muted-foreground">
              {contacts.length} contact{contacts.length !== 1 ? "s" : ""}
            </Text>
          </View>
          <Button onPress={handleAddContact} size="sm">
            <Text>➕ Ajouter</Text>
          </Button>
        </View>
      </View>

      {contacts.length === 0 ? (
        <View className="flex-1 justify-center items-center gap-4 p-4">
          <View className="bg-muted/20 p-6 rounded-full">
            <Text className="text-6xl">👥</Text>
          </View>
          <View className="items-center gap-2">
            <Text className="text-xl font-semibold text-foreground">
              Aucun contact
            </Text>
            <Text className="text-muted-foreground text-center max-w-sm">
              Ajoutez vos premiers contacts pour faciliter les invitations à vos
              événements
            </Text>
          </View>
          <Button onPress={handleAddContact} className="mt-4">
            <Text>➕ Ajouter mon premier contact</Text>
          </Button>
        </View>
      ) : (
        <FlatList
          data={contacts}
          renderItem={renderContactCard}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ padding: 16, paddingTop: 0 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      )}

      {/* Modal d'ajout/édition */}
      <Modal
        visible={showAddModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-end">
          <View
            className="bg-white rounded-t-xl p-6"
            style={{ maxHeight: "80%" }}
          >
            <View className="flex-row items-center justify-between mb-6">
              <Text className="text-xl font-semibold">
                {editingContact ? "Modifier le contact" : "Nouveau contact"}
              </Text>
              <Button
                variant="ghost"
                size="sm"
                onPress={() => setShowAddModal(false)}
              >
                <Text>✕</Text>
              </Button>
            </View>

            <View className="gap-4">
              <View>
                <Label>Nom *</Label>
                <Input
                  placeholder="Nom du contact"
                  value={formData.name}
                  onChangeText={(text) =>
                    setFormData({ ...formData, name: text })
                  }
                />
              </View>

              <View>
                <Label>Email</Label>
                <Input
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChangeText={(text) =>
                    setFormData({ ...formData, email: text })
                  }
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              <View>
                <Label>Téléphone</Label>
                <Input
                  placeholder="+33 6 12 34 56 78"
                  value={formData.phone}
                  onChangeText={(text) =>
                    setFormData({ ...formData, phone: text })
                  }
                  keyboardType="phone-pad"
                />
              </View>

              <View className="flex-row gap-3 mt-6">
                <Button
                  variant="outline"
                  className="flex-1"
                  onPress={() => setShowAddModal(false)}
                >
                  <Text>Annuler</Text>
                </Button>
                <Button className="flex-1" onPress={handleSaveContact}>
                  <Text>{editingContact ? "Modifier" : "Ajouter"}</Text>
                </Button>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}
